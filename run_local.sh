#!/bin/bash
# AutoTrader Local Runner - No Docker Required
# Chạy bot trực tiếp với Python thay vì Docker

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Constants
AUTOTRADER_DIR="$HOME/.autotrader"
CREDENTIALS_DIR="$AUTOTRADER_DIR/credentials"
CONFIG_DIR="./configs"
DATA_DIR="./data"
LOGS_DIR="./logs"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Utility functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check Python
check_python() {
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 is not installed"
        return 1
    fi
    
    local python_version=$(python3 --version 2>&1 | cut -d' ' -f2)
    log_success "Python3 found: $python_version"
    return 0
}

# Setup virtual environment
setup_venv() {
    log_info "Setting up Python virtual environment..."
    
    if [[ ! -d "venv" ]]; then
        log_info "Creating virtual environment..."
        python3 -m venv venv
    fi
    
    log_info "Activating virtual environment..."
    source venv/bin/activate
    
    log_info "Installing dependencies..."
    pip install --upgrade pip
    
    if [[ -f "requirements-telegram.txt" ]]; then
        pip install -r requirements-telegram.txt
    fi
    
    if [[ -f "requirements.txt" ]]; then
        pip install -r requirements.txt
    fi
    
    log_success "Virtual environment setup complete"
}

# Ensure directories exist
ensure_directories() {
    mkdir -p "$AUTOTRADER_DIR" "$CREDENTIALS_DIR" "$CONFIG_DIR" "$DATA_DIR" "$LOGS_DIR"
    chmod 755 "$AUTOTRADER_DIR" "$CREDENTIALS_DIR" 2>/dev/null || true
    log_success "Directories created"
}

# Create basic template if not exists
create_template() {
    local template_file="$CONFIG_DIR/template.json"
    
    if [[ ! -f "$template_file" ]]; then
        log_info "Creating template config..."
        cat > "$template_file" << 'EOF'
{
  "symbol": "SYMBOL_PLACEHOLDER",
  "exchange": "bybit",
  "direction": "LONG",
  "amount": 50.0,
  "use_test_mode": false,
  "use_sandbox": false,
  "order_type": "limit",
  "signal_cooldown_minutes": 3.0,
  "trading_loop_interval_seconds": 10,
  "log_level": "INFO",
  "save_trades_to_csv": true,
  "enable_notifications": true
}
EOF
        log_success "Template config created: $template_file"
    fi
}

# Start Telegram bot
start_telegram() {
    log_info "Starting Telegram Bot (Local Python)"
    log_info "===================================="
    
    # Check Python
    if ! check_python; then
        return 1
    fi
    
    # Setup environment
    ensure_directories
    create_template
    setup_venv
    
    # Check Telegram token
    if [[ -z "${TELEGRAM_BOT_TOKEN:-}" ]]; then
        log_error "Missing TELEGRAM_BOT_TOKEN environment variable"
        log_info "Set it with: export TELEGRAM_BOT_TOKEN='your_bot_token'"
        return 1
    fi
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Set Python path
    export PYTHONPATH="$SCRIPT_DIR:$PYTHONPATH"
    
    log_info "Starting Telegram bot..."
    log_info "Token: ${TELEGRAM_BOT_TOKEN:0:10}..."
    
    # Run the Telegram bot
    python3 run_telegram_app.py start --token "$TELEGRAM_BOT_TOKEN"
}

# Start trading bot
start_trading() {
    local symbol="$1"
    shift
    
    log_info "Starting Trading Bot: $symbol (Local Python)"
    log_info "============================================="
    
    # Check Python
    if ! check_python; then
        return 1
    fi
    
    # Setup environment
    ensure_directories
    create_template
    setup_venv
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Set Python path
    export PYTHONPATH="$SCRIPT_DIR:$PYTHONPATH"
    
    # Parse options
    local amount="50"
    local test_mode="false"
    local direction="LONG"
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --amount)
                amount="$2"
                shift 2
                ;;
            --test-mode|--test)
                test_mode="true"
                shift
                ;;
            --live-mode|--live)
                test_mode="false"
                shift
                ;;
            --direction)
                direction="$2"
                shift 2
                ;;
            *)
                log_warning "Unknown option: $1"
                shift
                ;;
        esac
    done
    
    # Set environment variables
    export TRADE_SYMBOL="$symbol"
    export TRADE_AMOUNT="$amount"
    export TRADE_DIRECTION="$direction"
    export TEST_MODE="$test_mode"
    
    log_info "Symbol: $symbol"
    log_info "Amount: $amount"
    log_info "Direction: $direction"
    log_info "Test Mode: $test_mode"
    
    # Run the trading bot
    python3 main.py --start
}

# Show help
show_help() {
    cat << EOF
🤖 AutoTrader Local Runner (No Docker)

USAGE: $0 <command> [options]

COMMANDS:
    telegram                Start Telegram bot locally
    start <symbol> [opts]   Start trading bot locally
    setup                   Setup environment (venv, dependencies)
    help                    Show this help

TRADING BOT OPTIONS:
    --amount <amount>       Trading amount (default: 50)
    --test                  Enable test mode
    --live                  Enable live mode (default)
    --direction <dir>       Trading direction (LONG/SHORT)

EXAMPLES:
    # Setup environment
    $0 setup

    # Start Telegram bot
    export TELEGRAM_BOT_TOKEN='your_token'
    $0 telegram

    # Start trading bot
    export BYBIT_API_KEY='your_key'
    export BYBIT_API_SECRET='your_secret'
    $0 start eth --amount 100 --test

REQUIREMENTS:
    - Python 3.8+
    - pip
    - Virtual environment support

NOTE: This script runs bots locally without Docker.
      For Docker-based deployment, use ./bot.sh instead.
EOF
}

# Main function
main() {
    case "${1:-help}" in
        telegram)
            start_telegram
            ;;
        start)
            if [[ -z "${2:-}" ]]; then
                log_error "Symbol is required"
                log_info "Usage: $0 start <symbol> [options]"
                exit 1
            fi
            shift
            start_trading "$@"
            ;;
        setup)
            log_info "Setting up local environment..."
            check_python
            ensure_directories
            create_template
            setup_venv
            log_success "Setup complete!"
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "Unknown command: $1"
            log_info "Use '$0 help' for available commands"
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
