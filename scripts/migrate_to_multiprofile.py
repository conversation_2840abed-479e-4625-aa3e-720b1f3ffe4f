#!/usr/bin/env python3
"""
Migration script for converting legacy containers to multi-profile format
Safely migrates existing containers with backup and rollback support
"""

import asyncio
import subprocess
import sys
import os
import json
import shutil
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Optional, Tuple

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.constants import get_container_name, get_config_filename, CONFIG_DIR

class MultiProfileMigrator:
    def __init__(self):
        self.backup_dir = Path(f"migration_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        self.migrated_containers = []
        self.failed_migrations = []
        
    async def run_migration(self, dry_run: bool = True, profile_mapping: Dict[str, str] = None):
        """Run the complete migration process"""
        print("🔄 **MULTI-PROFILE MIGRATION TOOL**")
        print("=" * 60)
        
        if dry_run:
            print("🔍 **DRY RUN MODE** - No actual changes will be made")
        else:
            print("⚠️  **LIVE MIGRATION MODE** - Changes will be applied")
        
        print("=" * 60)
        
        try:
            # Step 1: Discover legacy containers
            legacy_containers = await self.discover_legacy_containers()
            
            if not legacy_containers:
                print("✅ No legacy containers found - migration not needed")
                return True
            
            # Step 2: Create backup
            if not dry_run:
                await self.create_backup()
            
            # Step 3: Plan migration
            migration_plan = await self.create_migration_plan(legacy_containers, profile_mapping)
            
            # Step 4: Show migration plan
            await self.show_migration_plan(migration_plan)
            
            if not dry_run:
                # Step 5: Confirm migration
                if not await self.confirm_migration():
                    print("❌ Migration cancelled by user")
                    return False
                
                # Step 6: Execute migration
                success = await self.execute_migration(migration_plan)
                
                # Step 7: Verify migration
                if success:
                    await self.verify_migration()
                
                # Step 8: Cleanup or rollback
                if success:
                    await self.cleanup_migration()
                else:
                    await self.rollback_migration()
                
                return success
            else:
                print("\n✅ Dry run completed - use --execute to perform actual migration")
                return True
                
        except Exception as e:
            print(f"❌ Migration failed with error: {e}")
            if not dry_run:
                await self.rollback_migration()
            return False
    
    async def discover_legacy_containers(self) -> List[Dict[str, str]]:
        """Discover existing legacy containers"""
        print("\n📋 **Step 1: Discovering Legacy Containers**")
        print("-" * 40)
        
        try:
            # Get all containers
            result = subprocess.run(
                ["docker", "ps", "-a", "--format", "{{.Names}}\t{{.Status}}\t{{.Image}}"],
                capture_output=True,
                text=True
            )
            
            if result.returncode != 0:
                raise Exception(f"Failed to list containers: {result.stderr}")
            
            legacy_containers = []
            
            for line in result.stdout.strip().split('\n'):
                if not line:
                    continue
                
                parts = line.split('\t')
                if len(parts) >= 3:
                    name, status, image = parts[0], parts[1], parts[2]
                    
                    # Check if it's a legacy trading container
                    if (name.endswith('usdt') and 
                        '-' not in name and 
                        'autotrader' in image):
                        
                        symbol = name[:-4].upper()  # Remove 'usdt'
                        legacy_containers.append({
                            'name': name,
                            'symbol': symbol,
                            'status': status,
                            'image': image
                        })
                        
                        print(f"🔍 Found legacy container: {name} ({symbol}) - {status}")
            
            if not legacy_containers:
                print("ℹ️  No legacy containers found")
            else:
                print(f"\n📊 Found {len(legacy_containers)} legacy containers")
            
            return legacy_containers
            
        except Exception as e:
            print(f"❌ Failed to discover containers: {e}")
            return []
    
    async def create_backup(self):
        """Create backup of current state"""
        print(f"\n📋 **Step 2: Creating Backup**")
        print("-" * 40)
        
        try:
            # Create backup directory
            self.backup_dir.mkdir(parents=True, exist_ok=True)
            print(f"📁 Created backup directory: {self.backup_dir}")
            
            # Backup configs
            if CONFIG_DIR.exists():
                config_backup = self.backup_dir / "configs"
                shutil.copytree(CONFIG_DIR, config_backup)
                print(f"✅ Backed up configs to: {config_backup}")
            
            # Backup container list
            result = subprocess.run(
                ["docker", "ps", "-a", "--format", "json"],
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                container_backup = self.backup_dir / "containers.json"
                with open(container_backup, 'w') as f:
                    f.write(result.stdout)
                print(f"✅ Backed up container list to: {container_backup}")
            
            print(f"✅ Backup completed: {self.backup_dir}")
            
        except Exception as e:
            print(f"❌ Backup failed: {e}")
            raise
    
    async def create_migration_plan(self, legacy_containers: List[Dict], profile_mapping: Dict[str, str] = None) -> List[Dict]:
        """Create migration plan"""
        print(f"\n📋 **Step 3: Creating Migration Plan**")
        print("-" * 40)
        
        migration_plan = []
        
        for container in legacy_containers:
            symbol = container['symbol']
            old_name = container['name']
            
            # Determine target profile
            if profile_mapping and symbol.lower() in profile_mapping:
                profile = profile_mapping[symbol.lower()]
            else:
                profile = "main"  # Default profile
            
            new_name = get_container_name(symbol, profile)
            old_config = CONFIG_DIR / f"{symbol.lower()}.config.json"
            new_config = CONFIG_DIR / get_config_filename(symbol, profile)
            
            migration_plan.append({
                'symbol': symbol,
                'old_name': old_name,
                'new_name': new_name,
                'profile': profile,
                'old_config': old_config,
                'new_config': new_config,
                'status': container['status']
            })
            
            print(f"📋 {old_name} → {new_name} (profile: {profile})")
        
        return migration_plan
    
    async def show_migration_plan(self, migration_plan: List[Dict]):
        """Show detailed migration plan"""
        print(f"\n📋 **Step 4: Migration Plan**")
        print("-" * 40)
        
        print(f"📊 **Summary:**")
        print(f"   Containers to migrate: {len(migration_plan)}")
        print(f"   Backup directory: {self.backup_dir}")
        print()
        
        print(f"📋 **Detailed Plan:**")
        for i, plan in enumerate(migration_plan, 1):
            print(f"\n{i}. **{plan['symbol']}**")
            print(f"   Container: {plan['old_name']} → {plan['new_name']}")
            print(f"   Profile: {plan['profile']}")
            print(f"   Config: {plan['old_config'].name} → {plan['new_config'].name}")
            print(f"   Status: {plan['status']}")
    
    async def confirm_migration(self) -> bool:
        """Get user confirmation for migration"""
        print(f"\n⚠️  **CONFIRMATION REQUIRED**")
        print("-" * 40)
        print("This will:")
        print("1. Stop existing legacy containers")
        print("2. Create new profile-based containers")
        print("3. Migrate configuration files")
        print("4. Start new containers")
        print()
        print("⚠️  Legacy containers will be stopped but not removed")
        print("⚠️  You can rollback if needed")
        print()
        
        while True:
            response = input("Continue with migration? (yes/no): ").lower().strip()
            if response in ['yes', 'y']:
                return True
            elif response in ['no', 'n']:
                return False
            else:
                print("Please enter 'yes' or 'no'")
    
    async def execute_migration(self, migration_plan: List[Dict]) -> bool:
        """Execute the migration plan"""
        print(f"\n📋 **Step 5: Executing Migration**")
        print("-" * 40)
        
        success_count = 0
        
        for plan in migration_plan:
            try:
                print(f"\n🔄 Migrating {plan['old_name']} → {plan['new_name']}")
                
                # Stop old container
                print(f"   Stopping {plan['old_name']}...")
                result = subprocess.run(
                    ["docker", "stop", plan['old_name']],
                    capture_output=True,
                    text=True
                )
                
                if result.returncode != 0:
                    print(f"   ⚠️  Warning: Could not stop {plan['old_name']}: {result.stderr}")
                
                # Migrate config
                if plan['old_config'].exists():
                    print(f"   Migrating config...")
                    with open(plan['old_config'], 'r') as f:
                        config = json.load(f)
                    
                    # Add profile information
                    config['profile'] = plan['profile']
                    config['migrated_from'] = plan['old_config'].name
                    config['migration_date'] = datetime.now().isoformat()
                    
                    with open(plan['new_config'], 'w') as f:
                        json.dump(config, f, indent=2)
                    
                    print(f"   ✅ Config migrated: {plan['new_config'].name}")
                
                # Create new container using bot.sh
                print(f"   Creating new container...")
                cmd = [
                    "./bot.sh", "start", plan['symbol'].lower(),
                    "--profile", plan['profile']
                ]
                
                # Add amount from config if available
                if plan['new_config'].exists():
                    with open(plan['new_config'], 'r') as f:
                        config = json.load(f)
                    if 'amount' in config:
                        cmd.extend(["--amount", str(config['amount'])])
                
                result = subprocess.run(cmd, capture_output=True, text=True, cwd=project_root)
                
                if result.returncode == 0:
                    print(f"   ✅ Successfully created {plan['new_name']}")
                    self.migrated_containers.append(plan)
                    success_count += 1
                else:
                    print(f"   ❌ Failed to create {plan['new_name']}: {result.stderr}")
                    self.failed_migrations.append(plan)
                
            except Exception as e:
                print(f"   ❌ Migration failed for {plan['old_name']}: {e}")
                self.failed_migrations.append(plan)
        
        print(f"\n📊 **Migration Results:**")
        print(f"   Successful: {success_count}/{len(migration_plan)}")
        print(f"   Failed: {len(self.failed_migrations)}")
        
        return len(self.failed_migrations) == 0
    
    async def verify_migration(self):
        """Verify migration results"""
        print(f"\n📋 **Step 6: Verifying Migration**")
        print("-" * 40)
        
        for plan in self.migrated_containers:
            # Check if new container exists and is running
            result = subprocess.run(
                ["docker", "ps", "--filter", f"name=^{plan['new_name']}$", "--format", "{{.Names}}\t{{.Status}}"],
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0 and result.stdout.strip():
                name, status = result.stdout.strip().split('\t')
                print(f"✅ {name}: {status}")
            else:
                print(f"❌ {plan['new_name']}: Not found or not running")
    
    async def cleanup_migration(self):
        """Clean up after successful migration"""
        print(f"\n📋 **Step 7: Cleanup**")
        print("-" * 40)
        
        print(f"✅ Migration completed successfully!")
        print(f"📁 Backup available at: {self.backup_dir}")
        print(f"💡 Legacy containers are stopped but not removed")
        print(f"💡 Use 'docker rm <container>' to remove them if desired")
    
    async def rollback_migration(self):
        """Rollback failed migration"""
        print(f"\n📋 **Step 7: Rolling Back Migration**")
        print("-" * 40)
        
        print("🔄 Rolling back changes...")
        
        # Remove failed new containers
        for plan in self.migrated_containers:
            try:
                subprocess.run(["docker", "rm", "-f", plan['new_name']], 
                             capture_output=True, text=True)
                print(f"✅ Removed {plan['new_name']}")
            except:
                pass
        
        # Restore configs from backup
        if (self.backup_dir / "configs").exists():
            if CONFIG_DIR.exists():
                shutil.rmtree(CONFIG_DIR)
            shutil.copytree(self.backup_dir / "configs", CONFIG_DIR)
            print("✅ Restored configs from backup")
        
        # Restart legacy containers
        for plan in self.migrated_containers:
            try:
                subprocess.run(["docker", "start", plan['old_name']], 
                             capture_output=True, text=True)
                print(f"✅ Restarted {plan['old_name']}")
            except:
                pass
        
        print("✅ Rollback completed")

async def main():
    """Main migration runner"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Migrate legacy containers to multi-profile format")
    parser.add_argument("--execute", action="store_true", help="Execute migration (default: dry run)")
    parser.add_argument("--profile-mapping", type=str, help="JSON file with symbol->profile mapping")
    
    args = parser.parse_args()
    
    # Load profile mapping if provided
    profile_mapping = None
    if args.profile_mapping:
        try:
            with open(args.profile_mapping, 'r') as f:
                profile_mapping = json.load(f)
        except Exception as e:
            print(f"❌ Failed to load profile mapping: {e}")
            return 1
    
    migrator = MultiProfileMigrator()
    success = await migrator.run_migration(
        dry_run=not args.execute,
        profile_mapping=profile_mapping
    )
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
