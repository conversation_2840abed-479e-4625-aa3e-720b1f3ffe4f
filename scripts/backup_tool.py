#!/usr/bin/env python3
"""
Backup tool for AutoTrader configurations and containers
Creates comprehensive backups before migrations or major changes
"""

import asyncio
import subprocess
import sys
import os
import json
import shutil
import tarfile
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Optional

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.constants import CONFIG_DIR

class AutoTraderBackup:
    def __init__(self, backup_name: str = None):
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        self.backup_name = backup_name or f"autotrader_backup_{timestamp}"
        self.backup_dir = Path(f"backups/{self.backup_name}")
        
    async def create_full_backup(self) -> bool:
        """Create complete backup of AutoTrader setup"""
        print("💾 **AUTOTRADER BACKUP TOOL**")
        print("=" * 60)
        print(f"📁 Backup name: {self.backup_name}")
        print(f"📁 Backup directory: {self.backup_dir}")
        print("=" * 60)
        
        try:
            # Create backup directory
            self.backup_dir.mkdir(parents=True, exist_ok=True)
            
            # Backup components
            await self.backup_configs()
            await self.backup_containers()
            await self.backup_environment()
            await self.backup_scripts()
            await self.create_backup_manifest()
            await self.create_backup_archive()
            
            print(f"\n✅ **BACKUP COMPLETED SUCCESSFULLY**")
            print(f"📁 Location: {self.backup_dir}")
            print(f"📦 Archive: {self.backup_dir}.tar.gz")
            
            return True
            
        except Exception as e:
            print(f"❌ Backup failed: {e}")
            return False
    
    async def backup_configs(self):
        """Backup configuration files"""
        print(f"\n📋 **Backing up Configurations**")
        print("-" * 40)
        
        config_backup_dir = self.backup_dir / "configs"
        
        if CONFIG_DIR.exists():
            shutil.copytree(CONFIG_DIR, config_backup_dir)
            
            # Count configs
            config_files = list(config_backup_dir.glob("*.json"))
            print(f"✅ Backed up {len(config_files)} config files")
            
            for config_file in config_files:
                print(f"   📄 {config_file.name}")
        else:
            print("ℹ️  No config directory found")
            config_backup_dir.mkdir(parents=True, exist_ok=True)
    
    async def backup_containers(self):
        """Backup container information and data"""
        print(f"\n📋 **Backing up Container Information**")
        print("-" * 40)
        
        container_backup_dir = self.backup_dir / "containers"
        container_backup_dir.mkdir(parents=True, exist_ok=True)
        
        try:
            # Get container list
            result = subprocess.run(
                ["docker", "ps", "-a", "--format", "json"],
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                # Save container list
                with open(container_backup_dir / "containers.json", 'w') as f:
                    f.write(result.stdout)
                
                # Parse and backup trading containers
                containers = []
                for line in result.stdout.strip().split('\n'):
                    if line:
                        container = json.loads(line)
                        if (container['Names'].endswith('usdt') or 
                            'telegram-bot' in container['Names']):
                            containers.append(container)
                
                # Save trading containers info
                with open(container_backup_dir / "trading_containers.json", 'w') as f:
                    json.dump(containers, f, indent=2)
                
                print(f"✅ Backed up {len(containers)} trading containers info")
                
                # Backup container logs
                await self.backup_container_logs(containers, container_backup_dir)
                
            else:
                print(f"⚠️  Could not list containers: {result.stderr}")
                
        except Exception as e:
            print(f"❌ Container backup failed: {e}")
    
    async def backup_container_logs(self, containers: List[Dict], backup_dir: Path):
        """Backup container logs"""
        logs_dir = backup_dir / "logs"
        logs_dir.mkdir(exist_ok=True)
        
        for container in containers:
            try:
                container_name = container['Names']
                
                # Get last 1000 lines of logs
                result = subprocess.run(
                    ["docker", "logs", "--tail", "1000", container_name],
                    capture_output=True,
                    text=True
                )
                
                if result.returncode == 0:
                    log_file = logs_dir / f"{container_name}.log"
                    with open(log_file, 'w') as f:
                        f.write(result.stdout)
                        if result.stderr:
                            f.write("\n--- STDERR ---\n")
                            f.write(result.stderr)
                    
                    print(f"   📄 {container_name}.log")
                    
            except Exception as e:
                print(f"   ⚠️  Could not backup logs for {container_name}: {e}")
    
    async def backup_environment(self):
        """Backup environment configuration"""
        print(f"\n📋 **Backing up Environment**")
        print("-" * 40)
        
        env_backup_dir = self.backup_dir / "environment"
        env_backup_dir.mkdir(parents=True, exist_ok=True)
        
        # Backup .env file (without sensitive data)
        env_file = project_root / ".env"
        if env_file.exists():
            with open(env_file, 'r') as f:
                env_content = f.read()
            
            # Mask sensitive values
            masked_content = []
            for line in env_content.split('\n'):
                if '=' in line and not line.strip().startswith('#'):
                    key, value = line.split('=', 1)
                    if any(sensitive in key.upper() for sensitive in ['TOKEN', 'KEY', 'SECRET', 'PASSWORD']):
                        masked_content.append(f"{key}=***MASKED***")
                    else:
                        masked_content.append(line)
                else:
                    masked_content.append(line)
            
            with open(env_backup_dir / ".env.masked", 'w') as f:
                f.write('\n'.join(masked_content))
            
            print("✅ Environment file backed up (sensitive data masked)")
        
        # Backup system info
        system_info = {
            "timestamp": datetime.now().isoformat(),
            "python_version": sys.version,
            "platform": sys.platform,
            "cwd": str(Path.cwd()),
        }
        
        # Get Docker info
        try:
            result = subprocess.run(["docker", "version", "--format", "json"], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                system_info["docker_version"] = json.loads(result.stdout)
        except:
            pass
        
        with open(env_backup_dir / "system_info.json", 'w') as f:
            json.dump(system_info, f, indent=2)
        
        print("✅ System information backed up")
    
    async def backup_scripts(self):
        """Backup important scripts"""
        print(f"\n📋 **Backing up Scripts**")
        print("-" * 40)
        
        scripts_backup_dir = self.backup_dir / "scripts"
        scripts_backup_dir.mkdir(parents=True, exist_ok=True)
        
        # Important files to backup
        important_files = [
            "bot.sh",
            "requirements.txt",
            ".env.example",
            "docker-compose.yml",
        ]
        
        backed_up = 0
        for file_name in important_files:
            file_path = project_root / file_name
            if file_path.exists():
                shutil.copy2(file_path, scripts_backup_dir / file_name)
                print(f"   📄 {file_name}")
                backed_up += 1
        
        # Backup scripts directory if exists
        scripts_dir = project_root / "scripts"
        if scripts_dir.exists():
            shutil.copytree(scripts_dir, scripts_backup_dir / "scripts_original")
            script_count = len(list((scripts_backup_dir / "scripts_original").glob("*")))
            print(f"   📁 scripts/ ({script_count} files)")
            backed_up += script_count
        
        print(f"✅ Backed up {backed_up} script files")
    
    async def create_backup_manifest(self):
        """Create backup manifest with metadata"""
        print(f"\n📋 **Creating Backup Manifest**")
        print("-" * 40)
        
        manifest = {
            "backup_name": self.backup_name,
            "created_at": datetime.now().isoformat(),
            "version": "1.0",
            "type": "full_backup",
            "components": {
                "configs": (self.backup_dir / "configs").exists(),
                "containers": (self.backup_dir / "containers").exists(),
                "environment": (self.backup_dir / "environment").exists(),
                "scripts": (self.backup_dir / "scripts").exists(),
            }
        }
        
        # Add file counts
        if (self.backup_dir / "configs").exists():
            manifest["config_count"] = len(list((self.backup_dir / "configs").glob("*.json")))
        
        if (self.backup_dir / "containers/trading_containers.json").exists():
            with open(self.backup_dir / "containers/trading_containers.json", 'r') as f:
                containers = json.load(f)
                manifest["container_count"] = len(containers)
        
        with open(self.backup_dir / "manifest.json", 'w') as f:
            json.dump(manifest, f, indent=2)
        
        print("✅ Backup manifest created")
    
    async def create_backup_archive(self):
        """Create compressed archive of backup"""
        print(f"\n📋 **Creating Backup Archive**")
        print("-" * 40)
        
        archive_path = f"{self.backup_dir}.tar.gz"
        
        with tarfile.open(archive_path, "w:gz") as tar:
            tar.add(self.backup_dir, arcname=self.backup_name)
        
        # Get archive size
        archive_size = Path(archive_path).stat().st_size
        size_mb = archive_size / (1024 * 1024)
        
        print(f"✅ Archive created: {archive_path}")
        print(f"📦 Size: {size_mb:.1f} MB")
    
    async def restore_backup(self, backup_path: str) -> bool:
        """Restore from backup archive"""
        print("🔄 **RESTORING FROM BACKUP**")
        print("=" * 60)
        
        backup_file = Path(backup_path)
        if not backup_file.exists():
            print(f"❌ Backup file not found: {backup_path}")
            return False
        
        try:
            # Extract archive
            print("📦 Extracting backup archive...")
            with tarfile.open(backup_file, "r:gz") as tar:
                tar.extractall("temp_restore")
            
            # Find backup directory
            restore_dir = Path("temp_restore") / backup_file.stem.replace(".tar", "")
            
            # Restore configs
            if (restore_dir / "configs").exists():
                if CONFIG_DIR.exists():
                    shutil.rmtree(CONFIG_DIR)
                shutil.copytree(restore_dir / "configs", CONFIG_DIR)
                print("✅ Configs restored")
            
            # Restore scripts
            if (restore_dir / "scripts").exists():
                for script_file in (restore_dir / "scripts").glob("*"):
                    if script_file.is_file():
                        shutil.copy2(script_file, project_root / script_file.name)
                        print(f"✅ Restored {script_file.name}")
            
            # Cleanup temp directory
            shutil.rmtree("temp_restore")
            
            print("✅ Backup restored successfully")
            return True
            
        except Exception as e:
            print(f"❌ Restore failed: {e}")
            return False

async def main():
    """Main backup tool runner"""
    import argparse
    
    parser = argparse.ArgumentParser(description="AutoTrader Backup Tool")
    parser.add_argument("--name", type=str, help="Backup name")
    parser.add_argument("--restore", type=str, help="Restore from backup file")
    
    args = parser.parse_args()
    
    backup_tool = AutoTraderBackup(args.name)
    
    if args.restore:
        success = await backup_tool.restore_backup(args.restore)
    else:
        success = await backup_tool.create_full_backup()
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
