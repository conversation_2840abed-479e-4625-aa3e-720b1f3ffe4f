#!/usr/bin/env python3
"""
Test symbol extraction from container names
"""

import re

def extract_symbol_from_name(container_name: str) -> str:
    """Extract trading symbol from container name"""
    # Container names follow pattern:
    # New format: {profile}-{symbol}usdt (e.g., main-btcusdt)
    # Old format: {symbol}usdt (e.g., btcusdt)

    name = container_name.lower()

    # Remove common suffixes and prefixes
    name = re.sub(r'[-_](bot|trader|trading|container)$', '', name)
    name = re.sub(r'^(bot|trader|trading)[-_]', '', name)

    # Handle new format: profile-symbolusdt
    if '-' in name and name.endswith('usdt'):
        # Extract symbol part after the last dash
        parts = name.split('-')
        if len(parts) >= 2:
            symbol_part = parts[-1]  # Get the last part
            if symbol_part.endswith('usdt'):
                symbol = symbol_part[:-4]  # Remove 'usdt'
            else:
                symbol = symbol_part
        else:
            symbol = name[:-4] if name.endswith('usdt') else name
    elif name.endswith('usdt'):
        # Old format: symbolusdt
        symbol = name[:-4]  # Remove 'usdt'
    else:
        symbol = name

    # Handle special cases where container name doesn't match symbol
    special_cases = { }

    if symbol in special_cases:
        return special_cases[symbol]

    return symbol.upper()

def test_symbol_extraction():
    """Test symbol extraction with various container names"""
    test_cases = [
        ("main-solusdt", "SOL"),
        ("main-btcusdt", "BTC"),
        ("test-ethusdt", "ETH"),
        ("backup-adausdt", "ADA"),
        ("btcusdt", "BTC"),  # Old format
        ("ethusdt", "ETH"),  # Old format
        ("solusdt", "SOL"),  # Old format
        ("profile1-dogeusdt", "DOGE"),
        ("longprofile-xrpusdt", "XRP"),
    ]
    
    print("🧪 Testing Symbol Extraction")
    print("=" * 40)
    
    all_passed = True
    for container_name, expected_symbol in test_cases:
        result = extract_symbol_from_name(container_name)
        status = "✅" if result == expected_symbol else "❌"
        print(f"{status} {container_name} -> {result} (expected: {expected_symbol})")
        
        if result != expected_symbol:
            all_passed = False
    
    print(f"\n📊 Test Results: {'All Passed' if all_passed else 'Some Failed'}")
    return all_passed

if __name__ == "__main__":
    test_symbol_extraction()