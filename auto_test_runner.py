#!/usr/bin/env python3
"""
Automated Test Runner - Self-executing test system
This script will automatically test the Telegram bot functionality
"""

import asyncio
import json
import time
import logging
import sys
import os
from typing import Dict, List, Any

# Add src to path
sys.path.append('src')

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class AutoTestRunner:
    """Automated test runner that executes tests independently"""
    
    def __init__(self):
        self.test_results: Dict[str, Any] = {}
        self.start_time = time.time()
        
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all automated tests"""
        logger.info("🚀 Starting Automated Test Suite")
        
        results = {
            'start_time': self.start_time,
            'tests': {},
            'summary': {}
        }
        
        # Test 1: Import Tests
        logger.info("📦 Running Import Tests...")
        import_results = await self._test_imports()
        results['tests']['imports'] = import_results
        
        # Test 2: Template Tests
        logger.info("📋 Running Template Tests...")
        template_results = await self._test_templates()
        results['tests']['templates'] = template_results
        
        # Test 3: Service Tests
        logger.info("🔧 Running Service Tests...")
        service_results = await self._test_services()
        results['tests']['services'] = service_results
        
        # Test 4: Handler Tests
        logger.info("🤖 Running Handler Tests...")
        handler_results = await self._test_handlers()
        results['tests']['handlers'] = handler_results
        
        # Test 5: Integration Tests
        logger.info("🔗 Running Integration Tests...")
        integration_results = await self._test_integration()
        results['tests']['integration'] = integration_results
        
        # Test 6: Callback Routing Tests
        logger.info("📊 Running Callback Tests...")
        callback_results = await self._test_callback_routing()
        results['tests']['callbacks'] = callback_results
        
        # Generate summary
        results['summary'] = self._generate_summary(results['tests'])
        results['end_time'] = time.time()
        results['duration'] = results['end_time'] - results['start_time']
        
        return results
    
    async def _test_imports(self) -> Dict[str, Any]:
        """Test all critical imports"""
        results = {'passed': 0, 'failed': 0, 'details': []}
        
        imports_to_test = [
            ('Strategy Builder Handler', 'src.infrastructure.telegram.handlers.strategy_builder_handler', 'StrategyBuilderHandler'),
            ('Strategy Builder Templates', 'src.infrastructure.telegram.template_builders.strategy_builder_templates', 'StrategyBuilderTemplates'),
            ('Custom Strategy Service', 'src.infrastructure.services.custom_strategy_service', 'CustomStrategyService'),
            ('Strategy Builder Model', 'src.core.models.custom_strategy', 'StrategyBuilder'),
            ('Custom Strategy Model', 'src.core.models.custom_strategy', 'CustomStrategy'),
            ('Telegram Templates', 'src.infrastructure.telegram.templates', 'TelegramTemplates'),
            ('Base Handler', 'src.infrastructure.telegram.handlers.base_handler', 'BaseTelegramHandler')
        ]
        
        for name, module_path, class_name in imports_to_test:
            try:
                module = __import__(module_path, fromlist=[class_name])
                cls = getattr(module, class_name)
                results['details'].append(f"✅ {name}: Import successful")
                results['passed'] += 1
            except Exception as e:
                results['details'].append(f"❌ {name}: Import failed - {str(e)}")
                results['failed'] += 1
        
        return results
    
    async def _test_templates(self) -> Dict[str, Any]:
        """Test template system"""
        results = {'passed': 0, 'failed': 0, 'details': []}
        
        try:
            from src.infrastructure.telegram.template_builders.strategy_builder_templates import StrategyBuilderTemplates
            
            # Test template methods
            template_methods = [
                ('Strategy Welcome', 'strategy_builder_welcome'),
                ('Step Selection', 'strategy_step_selection'),
                ('Entry Conditions', 'entry_conditions_setup'),
                ('DCA Settings', 'dca_settings_setup'),
                ('Exit Settings', 'exit_settings_setup'),
                ('Take Profit Setup', 'take_profit_setup'),
                ('Stop Loss Setup', 'stop_loss_setup')
            ]
            
            for name, method_name in template_methods:
                try:
                    method = getattr(StrategyBuilderTemplates, method_name)
                    template = method()
                    
                    if hasattr(template, 'content') and template.content:
                        results['details'].append(f"✅ {name}: Template generated successfully")
                        results['passed'] += 1
                    else:
                        results['details'].append(f"❌ {name}: Template content empty")
                        results['failed'] += 1
                        
                except Exception as e:
                    results['details'].append(f"❌ {name}: Template generation failed - {str(e)}")
                    results['failed'] += 1
                    
        except Exception as e:
            results['details'].append(f"❌ Template system: Failed to import - {str(e)}")
            results['failed'] += 1
        
        return results
    
    async def _test_services(self) -> Dict[str, Any]:
        """Test service layer"""
        results = {'passed': 0, 'failed': 0, 'details': []}
        
        try:
            from src.infrastructure.services.custom_strategy_service import CustomStrategyService
            
            # Test service initialization
            service = CustomStrategyService()
            results['details'].append("✅ Custom Strategy Service: Initialized successfully")
            results['passed'] += 1
            
            # Test service methods
            try:
                # Test list_user_strategies
                strategies = service.list_user_strategies(12345)
                results['details'].append(f"✅ List User Strategies: Returned {len(strategies)} strategies")
                results['passed'] += 1
            except Exception as e:
                results['details'].append(f"❌ List User Strategies: Failed - {str(e)}")
                results['failed'] += 1
            
            # Test strategy_exists
            try:
                exists = service.strategy_exists(12345, "test_strategy")
                results['details'].append(f"✅ Strategy Exists Check: Returned {exists}")
                results['passed'] += 1
            except Exception as e:
                results['details'].append(f"❌ Strategy Exists Check: Failed - {str(e)}")
                results['failed'] += 1
                
        except Exception as e:
            results['details'].append(f"❌ Service layer: Failed to initialize - {str(e)}")
            results['failed'] += 1
        
        return results
    
    async def _test_handlers(self) -> Dict[str, Any]:
        """Test handler initialization"""
        results = {'passed': 0, 'failed': 0, 'details': []}
        
        try:
            from src.infrastructure.telegram.handlers.strategy_builder_handler import StrategyBuilderHandler
            
            # Test handler initialization
            handler = StrategyBuilderHandler("test_token")
            results['details'].append("✅ Strategy Builder Handler: Initialized successfully")
            results['passed'] += 1
            
            # Test handler methods exist
            required_methods = [
                'handle_createstrategy',
                'handle_mystrategies',
                'handle_builder_callback',
                '_handle_step_selection',
                '_handle_step'
            ]
            
            for method_name in required_methods:
                if hasattr(handler, method_name):
                    results['details'].append(f"✅ Handler Method {method_name}: Exists")
                    results['passed'] += 1
                else:
                    results['details'].append(f"❌ Handler Method {method_name}: Missing")
                    results['failed'] += 1
                    
        except Exception as e:
            results['details'].append(f"❌ Handler initialization: Failed - {str(e)}")
            results['failed'] += 1
        
        return results
    
    async def _test_integration(self) -> Dict[str, Any]:
        """Test component integration"""
        results = {'passed': 0, 'failed': 0, 'details': []}
        
        try:
            # Test Strategy Builder integration
            from src.core.models.custom_strategy import StrategyBuilder
            from src.infrastructure.services.custom_strategy_service import CustomStrategyService
            
            # Create strategy builder
            builder = StrategyBuilder(12345)
            builder.set_basic_info("test_integration", "Test Integration", "Integration test strategy")
            
            # Add conditions
            builder.add_long_condition("RSI < 30", "AND")
            builder.add_long_condition("EMA_34 > EMA_89", "AND")
            
            # Set exit settings
            builder.set_exit_settings(take_profit_percentage=3.0, stop_loss_percentage=2.0)
            
            # Build strategy
            strategy = builder.build()
            
            if strategy:
                results['details'].append("✅ Strategy Integration: Strategy built successfully")
                results['passed'] += 1
                
                # Test strategy properties
                if hasattr(strategy, 'name') and strategy.name == "test_integration":
                    results['details'].append("✅ Strategy Properties: Name set correctly")
                    results['passed'] += 1
                else:
                    results['details'].append("❌ Strategy Properties: Name not set correctly")
                    results['failed'] += 1
                    
                if hasattr(strategy, 'entry_logic') and strategy.entry_logic:
                    results['details'].append("✅ Strategy Logic: Entry logic created")
                    results['passed'] += 1
                else:
                    results['details'].append("❌ Strategy Logic: Entry logic missing")
                    results['failed'] += 1
                    
            else:
                results['details'].append("❌ Strategy Integration: Failed to build strategy")
                results['failed'] += 1
                
        except Exception as e:
            results['details'].append(f"❌ Integration test: Failed - {str(e)}")
            results['failed'] += 1
        
        return results
    
    async def _test_callback_routing(self) -> Dict[str, Any]:
        """Test callback routing logic"""
        results = {'passed': 0, 'failed': 0, 'details': []}
        
        # Test callback routing patterns
        test_cases = [
            ("step_basic_info", True, "Step callback"),
            ("step_entry", True, "Step callback"),
            ("step_dca", True, "Step callback"),
            ("step_exit", True, "Step callback"),
            ("builder_save", True, "Builder callback"),
            ("builder_preview", True, "Builder callback"),
            ("builder_cancel", True, "Builder callback"),
            ("strategy_view_test", True, "Strategy callback"),
            ("my_strategies", True, "Special callback"),
            ("delete_strategy_test", True, "Delete callback"),
            ("confirm_delete_test", True, "Confirm callback"),
            ("entry_long", True, "Entry callback"),
            ("dca_triggers", True, "DCA callback"),
            ("exit_tp", True, "Exit callback"),
            ("tp_fixed", True, "TP callback"),
            ("sl_dynamic", True, "SL callback"),
            ("random_callback", False, "Should not route"),
            ("unknown_data", False, "Should not route")
        ]
        
        for callback_data, should_route, description in test_cases:
            # Test routing logic (same as in main_handler.py)
            routes = (
                callback_data.startswith("builder_") or 
                callback_data.startswith("template_") or 
                callback_data == "my_strategies" or 
                callback_data.startswith("delete_strategy_") or 
                callback_data.startswith("confirm_delete_") or 
                callback_data.startswith("strategy_") or 
                callback_data.startswith("step_") or 
                callback_data.startswith("entry_") or 
                callback_data.startswith("dca_") or 
                callback_data.startswith("exit_") or 
                callback_data.startswith("tp_") or 
                callback_data.startswith("sl_")
            )
            
            if routes == should_route:
                results['details'].append(f"✅ {description}: '{callback_data}' routed correctly")
                results['passed'] += 1
            else:
                results['details'].append(f"❌ {description}: '{callback_data}' routing failed")
                results['failed'] += 1
        
        return results
    
    def _generate_summary(self, tests: Dict[str, Any]) -> Dict[str, Any]:
        """Generate test summary"""
        total_passed = sum(test['passed'] for test in tests.values())
        total_failed = sum(test['failed'] for test in tests.values())
        total_tests = total_passed + total_failed
        
        success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0
        
        return {
            'total_tests': total_tests,
            'total_passed': total_passed,
            'total_failed': total_failed,
            'success_rate': success_rate,
            'test_categories': len(tests),
            'status': 'PASSED' if total_failed == 0 else 'FAILED'
        }
    
    def save_results(self, results: Dict[str, Any], filename: str = None) -> str:
        """Save test results to file"""
        if filename is None:
            timestamp = time.strftime('%Y%m%d_%H%M%S')
            filename = f"auto_test_results_{timestamp}.json"
        
        try:
            with open(filename, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            return filename
        except Exception as e:
            logger.error(f"Failed to save results: {e}")
            return None
    
    def print_summary(self, results: Dict[str, Any]) -> None:
        """Print test summary to console"""
        summary = results['summary']
        duration = results['duration']
        
        print("\n" + "="*60)
        print("🎉 AUTOMATED TEST RESULTS")
        print("="*60)
        print(f"⏱️  Duration: {duration:.2f} seconds")
        print(f"📊 Total Tests: {summary['total_tests']}")
        print(f"✅ Passed: {summary['total_passed']}")
        print(f"❌ Failed: {summary['total_failed']}")
        print(f"📈 Success Rate: {summary['success_rate']:.1f}%")
        print(f"🏆 Status: {summary['status']}")
        print("="*60)
        
        # Print category breakdown
        for category, test_data in results['tests'].items():
            status = "✅" if test_data['failed'] == 0 else "❌"
            print(f"{status} {category.title()}: {test_data['passed']} passed, {test_data['failed']} failed")
        
        print("="*60)


async def main():
    """Main execution function"""
    print("🚀 Starting Automated Test Runner...")
    
    runner = AutoTestRunner()
    results = await runner.run_all_tests()
    
    # Print summary
    runner.print_summary(results)
    
    # Save results
    filename = runner.save_results(results)
    if filename:
        print(f"📁 Results saved to: {filename}")
    
    # Return exit code based on results
    return 0 if results['summary']['status'] == 'PASSED' else 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
