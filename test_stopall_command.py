#!/usr/bin/env python3
"""
Test script to verify /stopall command functionality
"""

import asyncio
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def test_stopall_functionality():
    """Test the stopall command functionality"""
    print("🧪 Testing /stopall Command Functionality")
    print("=" * 50)
    
    try:
        # Import the required modules
        from src.infrastructure.telegram.handlers.bot_management_handler import BotManagementHandler
        from src.core.unified_command_processor import UnifiedCommandProcessor
        
        print("✅ Successfully imported required modules")
        
        # Create handler instance
        handler = BotManagementHandler("fake_token")
        print("✅ Created BotManagementHandler instance")
        
        # Test unified processor
        processor = UnifiedCommandProcessor()
        print("✅ Created UnifiedCommandProcessor instance")
        
        # Test list command to see if we can get containers
        result = await processor.process_list_command()
        print(f"📊 List command result: {result[0]} - {result[1]}")
        
        if result[0] == 0:
            containers = result[2]
            print(f"📦 Found {len(containers)} containers")
            
            # Check for running containers
            running_containers = [c for c in containers if c.get('running', False)]
            print(f"🏃 Running containers: {len(running_containers)}")
            
            for container in running_containers:
                name = container.get('name', 'Unknown')
                symbol = container.get('symbol', 'Unknown')
                print(f"  - {name} ({symbol})")
        
        # Test _docker_operation_by_name method exists
        if hasattr(handler, '_docker_operation_by_name'):
            print("✅ _docker_operation_by_name method exists")
        else:
            print("❌ _docker_operation_by_name method missing")
        
        # Test _execute_stop_all method exists
        if hasattr(handler, '_execute_stop_all'):
            print("✅ _execute_stop_all method exists")
        else:
            print("❌ _execute_stop_all method missing")
        
        # Test handle_stop_all_bots method exists
        if hasattr(handler, 'handle_stop_all_bots'):
            print("✅ handle_stop_all_bots method exists")
        else:
            print("❌ handle_stop_all_bots method missing")
        
        print("\n🎉 All stopall functionality tests passed!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

async def main():
    """Run all tests"""
    print("🚀 Running /stopall Command Tests")
    print("=" * 50)
    
    success = await test_stopall_functionality()
    
    if success:
        print("\n✅ All tests passed! /stopall command should work properly.")
        return 0
    else:
        print("\n❌ Some tests failed! /stopall command may have issues.")
        return 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))