#!/usr/bin/env python3
"""
Migration tests for multi-profile container support
Tests migration from old format to new format
"""

import asyncio
import subprocess
import sys
import os
import json
import shutil
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.constants import get_container_name, get_config_filename, CONFIG_DIR

class MultiProfileMigrationTest:
    def __init__(self):
        self.test_configs = []
        self.backup_configs = []
        self.passed_tests = 0
        self.total_tests = 0
        
    async def run_all_tests(self):
        """Run all migration tests"""
        print("🔄 **MULTI-PROFILE MIGRATION TESTS**")
        print("=" * 60)
        
        try:
            # Test 1: Config file migration
            await self.test_config_migration()
            
            # Test 2: Container name migration logic
            await self.test_container_migration_logic()
            
            # Test 3: Backward compatibility preservation
            await self.test_backward_compatibility()
            
            # Test 4: Migration safety checks
            await self.test_migration_safety()
            
            # Summary
            self.print_summary()
            
        except Exception as e:
            print(f"❌ Migration test suite failed with error: {e}")
            return False
        finally:
            # Cleanup
            await self.cleanup()
            
        return self.passed_tests == self.total_tests
    
    async def test_config_migration(self):
        """Test config file migration from old to new format"""
        print("\n📋 **Test 1: Config File Migration**")
        print("-" * 40)
        
        # Create test legacy config files
        legacy_configs = [
            ("btc.config.json", "BTC", None),
            ("eth.config.json", "ETH", None),
            ("sol.config.json", "SOL", None),
        ]
        
        # Ensure config directory exists
        CONFIG_DIR.mkdir(parents=True, exist_ok=True)
        
        for config_file, symbol, profile in legacy_configs:
            self.total_tests += 1
            
            try:
                # Create legacy config
                legacy_path = CONFIG_DIR / config_file
                legacy_config = {
                    "symbol": symbol,
                    "amount": 100,
                    "test_mode": True,
                    "created_by": "migration_test",
                    "format": "legacy"
                }
                
                with open(legacy_path, 'w') as f:
                    json.dump(legacy_config, f, indent=2)
                
                self.test_configs.append(legacy_path)
                
                # Test migration to new format
                target_profile = "main"  # Default migration profile
                new_config_file = get_config_filename(symbol, target_profile)
                new_path = CONFIG_DIR / new_config_file
                
                # Simulate migration
                if legacy_path.exists():
                    # Read legacy config
                    with open(legacy_path, 'r') as f:
                        config_data = json.load(f)
                    
                    # Add profile information
                    config_data["profile"] = target_profile
                    config_data["migrated_from"] = config_file
                    config_data["format"] = "new"
                    
                    # Write new config
                    with open(new_path, 'w') as f:
                        json.dump(config_data, f, indent=2)
                    
                    self.test_configs.append(new_path)
                    
                    # Verify migration
                    if new_path.exists():
                        with open(new_path, 'r') as f:
                            migrated_config = json.load(f)
                        
                        if (migrated_config.get('profile') == target_profile and
                            migrated_config.get('symbol') == symbol and
                            migrated_config.get('migrated_from') == config_file):
                            
                            self.passed_tests += 1
                            print(f"✅ Migrated {config_file} → {new_config_file}")
                        else:
                            print(f"❌ Migration data incorrect for {config_file}")
                    else:
                        print(f"❌ Migration failed to create {new_config_file}")
                else:
                    print(f"❌ Legacy config not created: {config_file}")
                    
            except Exception as e:
                print(f"❌ Config migration failed for {config_file}: {e}")
    
    async def test_container_migration_logic(self):
        """Test container name migration logic"""
        print("\n📋 **Test 2: Container Migration Logic**")
        print("-" * 40)
        
        # Test migration scenarios
        migration_scenarios = [
            # (old_name, target_profile, expected_new_name)
            ("btcusdt", "main", "main-btcusdt"),
            ("ethusdt", "prod", "prod-ethusdt"),
            ("solusdt", "backup", "backup-solusdt"),
            ("hyperusdt", "test", "test-hyperusdt"),
        ]
        
        for old_name, target_profile, expected_new_name in migration_scenarios:
            self.total_tests += 1
            
            try:
                # Extract symbol from old name
                if old_name.endswith('usdt'):
                    symbol = old_name[:-4].upper()
                else:
                    symbol = old_name.upper()
                
                # Generate new name using our function
                actual_new_name = get_container_name(symbol, target_profile)
                
                if actual_new_name == expected_new_name:
                    self.passed_tests += 1
                    print(f"✅ Migration: {old_name} → {actual_new_name} (profile: {target_profile})")
                else:
                    print(f"❌ Migration: {old_name} → expected {expected_new_name}, got {actual_new_name}")
                    
            except Exception as e:
                print(f"❌ Container migration logic failed for {old_name}: {e}")
    
    async def test_backward_compatibility(self):
        """Test that backward compatibility is preserved"""
        print("\n📋 **Test 3: Backward Compatibility Preservation**")
        print("-" * 40)
        
        # Test that old format still works
        legacy_tests = [
            ("BTC", None, "btcusdt", "btc.config.json"),
            ("ETH", None, "ethusdt", "eth.config.json"),
            ("SOL", None, "solusdt", "sol.config.json"),
        ]
        
        for symbol, profile, expected_container, expected_config in legacy_tests:
            self.total_tests += 1
            
            try:
                # Test container naming
                actual_container = get_container_name(symbol, profile)
                container_ok = actual_container == expected_container
                
                # Test config naming
                actual_config = get_config_filename(symbol, profile)
                config_ok = actual_config == expected_config
                
                if container_ok and config_ok:
                    self.passed_tests += 1
                    print(f"✅ Legacy format preserved: {symbol} → {actual_container}, {actual_config}")
                else:
                    print(f"❌ Legacy format broken: {symbol} → container={actual_container}, config={actual_config}")
                    
            except Exception as e:
                print(f"❌ Backward compatibility test failed for {symbol}: {e}")
    
    async def test_migration_safety(self):
        """Test migration safety checks"""
        print("\n📋 **Test 4: Migration Safety Checks**")
        print("-" * 40)
        
        # Test 1: Prevent overwriting existing new format configs
        self.total_tests += 1
        
        try:
            # Create a new format config
            symbol = "TEST"
            profile = "main"
            new_config_file = get_config_filename(symbol, profile)
            new_path = CONFIG_DIR / new_config_file
            
            existing_config = {
                "symbol": symbol,
                "profile": profile,
                "amount": 200,
                "important_data": "do_not_overwrite"
            }
            
            with open(new_path, 'w') as f:
                json.dump(existing_config, f, indent=2)
            
            self.test_configs.append(new_path)
            
            # Create a legacy config that would migrate to the same name
            legacy_config_file = f"{symbol.lower()}.config.json"
            legacy_path = CONFIG_DIR / legacy_config_file
            
            legacy_config = {
                "symbol": symbol,
                "amount": 100,
                "format": "legacy"
            }
            
            with open(legacy_path, 'w') as f:
                json.dump(legacy_config, f, indent=2)
            
            self.test_configs.append(legacy_path)
            
            # Simulate safe migration check
            if new_path.exists():
                # Should not overwrite existing new format config
                with open(new_path, 'r') as f:
                    preserved_config = json.load(f)
                
                if preserved_config.get('important_data') == 'do_not_overwrite':
                    self.passed_tests += 1
                    print("✅ Migration safety: Existing new format config preserved")
                else:
                    print("❌ Migration safety: Existing config was overwritten")
            else:
                print("❌ Migration safety: Test setup failed")
                
        except Exception as e:
            print(f"❌ Migration safety test failed: {e}")
        
        # Test 2: Backup creation
        self.total_tests += 1
        
        try:
            # Test that migration creates backups
            test_config = CONFIG_DIR / "backup_test.config.json"
            test_data = {"test": "data", "backup": True}
            
            with open(test_config, 'w') as f:
                json.dump(test_data, f, indent=2)
            
            self.test_configs.append(test_config)
            
            # Simulate backup creation
            backup_path = CONFIG_DIR / f"backup_test.config.json.backup"
            shutil.copy2(test_config, backup_path)
            self.backup_configs.append(backup_path)
            
            if backup_path.exists():
                with open(backup_path, 'r') as f:
                    backup_data = json.load(f)
                
                if backup_data == test_data:
                    self.passed_tests += 1
                    print("✅ Migration safety: Backup creation works")
                else:
                    print("❌ Migration safety: Backup data corrupted")
            else:
                print("❌ Migration safety: Backup not created")
                
        except Exception as e:
            print(f"❌ Backup creation test failed: {e}")
    
    def print_summary(self):
        """Print test summary"""
        print("\n" + "=" * 60)
        print("📊 **MIGRATION TEST SUMMARY**")
        print("=" * 60)
        print(f"Total tests: {self.total_tests}")
        print(f"Passed: {self.passed_tests}")
        print(f"Failed: {self.total_tests - self.passed_tests}")
        print(f"Success rate: {(self.passed_tests/self.total_tests)*100:.1f}%")
        
        if self.passed_tests == self.total_tests:
            print("\n🎉 **ALL MIGRATION TESTS PASSED!**")
            print("✅ Migration system is safe and reliable")
        else:
            print(f"\n❌ **{self.total_tests - self.passed_tests} MIGRATION TESTS FAILED**")
            print("⚠️  Fix migration issues before deployment")
    
    async def cleanup(self):
        """Clean up test files"""
        print(f"\n🧹 Cleaning up {len(self.test_configs)} test configs and {len(self.backup_configs)} backups...")
        
        for config_path in self.test_configs + self.backup_configs:
            try:
                if config_path.exists():
                    config_path.unlink()
                    print(f"✅ Removed: {config_path.name}")
            except Exception as e:
                print(f"❌ Failed to remove {config_path.name}: {e}")

async def main():
    """Main test runner"""
    test_suite = MultiProfileMigrationTest()
    success = await test_suite.run_all_tests()
    
    print(f"\n{'='*60}")
    if success:
        print("🎉 **PHASE 2 MIGRATION TESTS: PASSED**")
        print("✅ Migration system is production ready")
    else:
        print("❌ **PHASE 2 MIGRATION TESTS: FAILED**")
        print("⚠️  Fix migration issues before deployment")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
