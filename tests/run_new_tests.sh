#!/bin/bash

# Test runner for new functionality tests
# This script runs all the new tests created for the implemented features

set -e

echo "🚀 Running New Functionality Tests"
echo "=================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test results tracking
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

run_test() {
    local test_name="$1"
    local test_command="$2"
    
    echo -e "${BLUE}📋 Running: $test_name${NC}"
    echo "Command: $test_command"
    echo ""
    
    ((TOTAL_TESTS++))
    
    if eval "$test_command"; then
        echo -e "${GREEN}✅ $test_name PASSED${NC}"
        ((PASSED_TESTS++))
    else
        echo -e "${RED}❌ $test_name FAILED${NC}"
        ((FAILED_TESTS++))
    fi
    
    echo ""
    echo "----------------------------------------"
    echo ""
}

# Change to project root
cd "$(dirname "$0")/.."

echo "📁 Working directory: $(pwd)"
echo ""

# Test 1: System Commands
run_test "System Commands Test" "python3 tests/test_system_commands.py"

# Test 2: UnifiedCommandProcessor Remove
run_test "UnifiedCommandProcessor Remove Test" "python3 tests/test_unified_processor_remove.py"

# Test 3: Error Handling
run_test "Error Handling Test" "python3 tests/test_error_handling.py"

# Test 4: Basic bot.sh functionality
run_test "Bot.sh Help Command" "./bot.sh help > /dev/null"

# Test 5: Bot.sh version command
run_test "Bot.sh Version Command" "./bot.sh version > /dev/null"

# Test 6: Bot.sh system-status command
run_test "Bot.sh System Status Command" "./bot.sh system-status > /dev/null || true"

# Test 7: Bot.sh invalid command handling
run_test "Bot.sh Invalid Command Handling" "! ./bot.sh invalid-command-test 2>/dev/null"

# Test 8: Bot.sh parameter validation
run_test "Bot.sh Parameter Validation" "! ./bot.sh status 2>/dev/null"

# Summary
echo "🏁 Test Summary"
echo "==============="
echo -e "Total Tests: ${BLUE}$TOTAL_TESTS${NC}"
echo -e "Passed: ${GREEN}$PASSED_TESTS${NC}"
echo -e "Failed: ${RED}$FAILED_TESTS${NC}"
echo ""

if [[ $FAILED_TESTS -eq 0 ]]; then
    echo -e "${GREEN}🎉 All tests passed!${NC}"
    echo ""
    echo "✨ New functionality is working correctly:"
    echo "  - System commands (start-all, stop-all, system-status)"
    echo "  - Improved remove command with smart detection"
    echo "  - Standardized error handling with Vietnamese messages"
    echo "  - Consistent command delegation pattern"
    echo ""
    exit 0
else
    echo -e "${RED}💥 $FAILED_TESTS test(s) failed!${NC}"
    echo ""
    echo "🔧 Please check the failed tests and fix any issues."
    echo ""
    exit 1
fi