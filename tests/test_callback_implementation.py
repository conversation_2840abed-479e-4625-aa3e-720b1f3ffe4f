#!/usr/bin/env python3
"""
Test callback implementation completeness
"""

import sys
import re
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def extract_callback_patterns_from_templates():
    """Extract all callback_data patterns from templates"""
    templates_file = project_root / "src/infrastructure/telegram/templates.py"
    
    if not templates_file.exists():
        print("❌ Templates file not found")
        return set()
    
    content = templates_file.read_text()
    
    # Find all callback_data patterns
    callback_pattern = r'"callback_data":\s*["\']([^"\']+)["\']'
    callbacks = set(re.findall(callback_pattern, content))
    
    # Also find f-string patterns
    fstring_pattern = r'"callback_data":\s*f["\']([^"\']+)["\']'
    fstring_callbacks = re.findall(fstring_pattern, content)
    
    # Process f-string patterns to get base patterns
    for callback in fstring_callbacks:
        # Replace {variable} with placeholder
        base_pattern = re.sub(r'\{[^}]+\}', '*', callback)
        callbacks.add(base_pattern)
    
    return callbacks

def extract_implemented_callbacks_from_handlers():
    """Extract implemented callback patterns from handlers"""
    main_handler_file = project_root / "src/infrastructure/telegram/handlers/main_handler.py"
    
    if not main_handler_file.exists():
        print("❌ Main handler file not found")
        return set()
    
    content = main_handler_file.read_text()
    
    implemented = set()
    
    # Find direct callback checks
    direct_pattern = r'data\s*==\s*["\']([^"\']+)["\']'
    direct_callbacks = re.findall(direct_pattern, content)
    implemented.update(direct_callbacks)
    
    # Find startswith patterns
    startswith_pattern = r'data\.startswith\(["\']([^"\']+)["\']'
    startswith_callbacks = re.findall(startswith_pattern, content)
    for callback in startswith_callbacks:
        implemented.add(callback + "*")  # Add wildcard for startswith
    
    return implemented

def analyze_callback_coverage():
    """Analyze callback coverage"""
    print("🔍 Analyzing Callback Implementation Coverage")
    print("=" * 60)
    
    # Extract patterns
    template_callbacks = extract_callback_patterns_from_templates()
    implemented_callbacks = extract_implemented_callbacks_from_handlers()
    
    print(f"\n📋 Found {len(template_callbacks)} callback patterns in templates")
    print(f"✅ Found {len(implemented_callbacks)} implemented callback patterns")
    
    # Categorize callbacks
    categories = {
        'bot_': [],
        'creds_': [],
        'help_': [],
        'admin_': [],
        'profile_': [],
        'wizard_': [],
        'createbot_': [],
        'startbot_': [],
        'stop_': [],
        'restart_': [],
        'remove_': [],
        'other': []
    }
    
    for callback in template_callbacks:
        categorized = False
        for prefix in categories:
            if prefix != 'other' and callback.startswith(prefix):
                categories[prefix].append(callback)
                categorized = True
                break
        if not categorized:
            categories['other'].append(callback)
    
    # Check implementation status
    print(f"\n📊 Callback Implementation Status by Category:")
    print("-" * 60)
    
    total_callbacks = 0
    implemented_count = 0
    
    for category, callbacks in categories.items():
        if not callbacks:
            continue
            
        print(f"\n🏷️  {category.upper().rstrip('_')} Callbacks:")
        
        for callback in sorted(callbacks):
            total_callbacks += 1
            
            # Check if implemented
            is_implemented = False
            
            # Direct match
            if callback in implemented_callbacks:
                is_implemented = True
            
            # Wildcard match (for dynamic callbacks like bot_start_{container})
            elif '*' in callback:
                base_pattern = callback.replace('*', '')
                if any(impl.startswith(base_pattern) for impl in implemented_callbacks):
                    is_implemented = True
            
            # Prefix match (for startswith patterns)
            else:
                callback_prefix = callback.split('_')[0] + '_'
                if callback_prefix + '*' in implemented_callbacks:
                    is_implemented = True
            
            if is_implemented:
                print(f"  ✅ {callback}")
                implemented_count += 1
            else:
                print(f"  ❌ {callback}")
    
    # Summary
    coverage_percent = (implemented_count / total_callbacks * 100) if total_callbacks > 0 else 0
    
    print(f"\n📈 Implementation Summary:")
    print(f"Total Callbacks: {total_callbacks}")
    print(f"Implemented: {implemented_count}")
    print(f"Missing: {total_callbacks - implemented_count}")
    print(f"Coverage: {coverage_percent:.1f}%")
    
    if coverage_percent >= 90:
        print(f"\n🎉 Excellent coverage! ({coverage_percent:.1f}%)")
        return 0
    elif coverage_percent >= 75:
        print(f"\n👍 Good coverage ({coverage_percent:.1f}%), but room for improvement")
        return 0
    else:
        print(f"\n⚠️  Coverage needs improvement ({coverage_percent:.1f}%)")
        return 1

def main():
    """Run callback analysis"""
    try:
        return analyze_callback_coverage()
    except Exception as e:
        print(f"💥 Error analyzing callbacks: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())