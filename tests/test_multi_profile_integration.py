#!/usr/bin/env python3
"""
Integration tests for multi-profile container support
Tests the complete workflow from creation to management
"""

import asyncio
import subprocess
import sys
import os
import json
import time
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.constants import get_container_name, get_config_filename, CONFIG_DIR

class MultiProfileIntegrationTest:
    def __init__(self):
        self.test_containers = []
        self.test_configs = []
        self.passed_tests = 0
        self.total_tests = 0
        
    async def run_all_tests(self):
        """Run all integration tests"""
        print("🧪 **MULTI-PROFILE INTEGRATION TESTS**")
        print("=" * 60)
        
        try:
            # Test 1: Core naming functions
            await self.test_core_naming_functions()
            
            # Test 2: Docker environment check
            await self.test_docker_environment()
            
            # Test 3: Config file creation
            await self.test_config_file_creation()
            
            # Test 4: Container detection logic
            await self.test_container_detection()
            
            # Test 5: Smart detection scenarios
            await self.test_smart_detection_scenarios()
            
            # Summary
            self.print_summary()
            
        except Exception as e:
            print(f"❌ Test suite failed with error: {e}")
            return False
        finally:
            # Cleanup
            await self.cleanup()
            
        return self.passed_tests == self.total_tests
    
    async def test_core_naming_functions(self):
        """Test core naming functions"""
        print("\n📋 **Test 1: Core Naming Functions**")
        print("-" * 40)
        
        test_cases = [
            # (symbol, profile, expected_container, expected_config)
            ("BTC", "main", "main-btcusdt", "main-btc.config.json"),
            ("ETH", "test", "test-ethusdt", "test-eth.config.json"),
            ("SOL", "prod", "prod-solusdt", "prod-sol.config.json"),
            ("BTC", None, "btcusdt", "btc.config.json"),  # Backward compatibility
            ("BTC/USDT:USDT", "main", "main-btcusdt", "main-btc.config.json"),
        ]
        
        for symbol, profile, expected_container, expected_config in test_cases:
            self.total_tests += 1
            
            # Test container naming
            actual_container = get_container_name(symbol, profile)
            container_ok = actual_container == expected_container
            
            # Test config naming
            actual_config = get_config_filename(symbol, profile)
            config_ok = actual_config == expected_config
            
            if container_ok and config_ok:
                self.passed_tests += 1
                profile_text = f"'{profile}'" if profile else "None"
                print(f"✅ {symbol} + {profile_text} → {actual_container}")
            else:
                print(f"❌ {symbol} + {profile}: Container={actual_container}, Config={actual_config}")
    
    async def test_docker_environment(self):
        """Test Docker environment availability"""
        print("\n📋 **Test 2: Docker Environment**")
        print("-" * 40)
        
        self.total_tests += 1
        
        try:
            # Check if Docker is running
            result = subprocess.run(
                ["docker", "version"], 
                capture_output=True, 
                text=True, 
                timeout=10
            )
            
            if result.returncode == 0:
                self.passed_tests += 1
                print("✅ Docker daemon is running")
                
                # Check for existing containers
                result = subprocess.run(
                    ["docker", "ps", "-a", "--format", "{{.Names}}"],
                    capture_output=True,
                    text=True
                )
                
                if result.returncode == 0:
                    containers = [line.strip() for line in result.stdout.split('\n') if line.strip()]
                    print(f"📊 Found {len(containers)} existing containers")
                    
                    # Show multi-profile containers
                    profile_containers = [c for c in containers if '-' in c and c.endswith('usdt')]
                    legacy_containers = [c for c in containers if c.endswith('usdt') and '-' not in c]
                    
                    if profile_containers:
                        print(f"🔑 Profile containers: {', '.join(profile_containers)}")
                    if legacy_containers:
                        print(f"📦 Legacy containers: {', '.join(legacy_containers)}")
                        
            else:
                print("❌ Docker daemon not running or not accessible")
                print(f"Error: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            print("❌ Docker command timed out")
        except FileNotFoundError:
            print("❌ Docker not installed")
        except Exception as e:
            print(f"❌ Docker check failed: {e}")
    
    async def test_config_file_creation(self):
        """Test config file creation with profiles"""
        print("\n📋 **Test 3: Config File Creation**")
        print("-" * 40)
        
        test_configs = [
            ("BTC", "main"),
            ("ETH", "test"),
            ("SOL", None),  # Legacy format
        ]
        
        for symbol, profile in test_configs:
            self.total_tests += 1
            
            try:
                # Create test config
                config_filename = get_config_filename(symbol, profile)
                config_path = CONFIG_DIR / config_filename
                
                # Ensure config directory exists
                CONFIG_DIR.mkdir(parents=True, exist_ok=True)
                
                # Create test config content
                test_config = {
                    "symbol": symbol,
                    "profile": profile,
                    "amount": 100,
                    "test_mode": True,
                    "created_by": "integration_test"
                }
                
                # Write config file
                with open(config_path, 'w') as f:
                    json.dump(test_config, f, indent=2)
                
                # Verify file exists and is readable
                if config_path.exists():
                    with open(config_path, 'r') as f:
                        loaded_config = json.load(f)
                    
                    if loaded_config.get('symbol') == symbol and loaded_config.get('profile') == profile:
                        self.passed_tests += 1
                        self.test_configs.append(config_path)
                        profile_text = f" (profile: {profile})" if profile else " (legacy)"
                        print(f"✅ Config created: {config_filename}{profile_text}")
                    else:
                        print(f"❌ Config content mismatch: {config_filename}")
                else:
                    print(f"❌ Config file not created: {config_filename}")
                    
            except Exception as e:
                print(f"❌ Config creation failed for {symbol}: {e}")
    
    async def test_container_detection(self):
        """Test container detection logic"""
        print("\n📋 **Test 4: Container Detection Logic**")
        print("-" * 40)
        
        # This test simulates container detection without actually creating containers
        mock_containers = [
            "main-btcusdt",
            "test-btcusdt", 
            "prod-btcusdt",
            "main-ethusdt",
            "btcusdt",  # Legacy
            "ethusdt",  # Legacy
            "telegram-bot",  # Non-trading
        ]
        
        test_cases = [
            ("BTC", ["main-btcusdt", "test-btcusdt", "prod-btcusdt", "btcusdt"]),
            ("ETH", ["main-ethusdt", "ethusdt"]),
            ("SOL", []),  # No containers
        ]
        
        for symbol, expected_matches in test_cases:
            self.total_tests += 1
            
            # Simulate container detection logic
            base_symbol = symbol.lower()
            matches = []
            
            for container in mock_containers:
                # New format: profile-symbolusdt
                if container.endswith(f"-{base_symbol}usdt"):
                    matches.append(container)
                # Old format: symbolusdt
                elif container == f"{base_symbol}usdt":
                    matches.append(container)
            
            if set(matches) == set(expected_matches):
                self.passed_tests += 1
                print(f"✅ {symbol}: Found {len(matches)} containers")
            else:
                print(f"❌ {symbol}: Expected {expected_matches}, got {matches}")
    
    async def test_smart_detection_scenarios(self):
        """Test smart detection scenarios"""
        print("\n📋 **Test 5: Smart Detection Scenarios**")
        print("-" * 40)
        
        scenarios = [
            {
                "name": "Single container found",
                "containers": ["main-btcusdt"],
                "search": "BTC",
                "expected_action": "direct_access"
            },
            {
                "name": "Multiple containers found", 
                "containers": ["main-btcusdt", "test-btcusdt", "prod-btcusdt"],
                "search": "BTC",
                "expected_action": "show_selection"
            },
            {
                "name": "No containers found",
                "containers": [],
                "search": "DOGE",
                "expected_action": "not_found"
            },
            {
                "name": "Direct container access",
                "containers": ["main-btcusdt", "test-btcusdt"],
                "search": "main-btcusdt",
                "expected_action": "direct_access"
            }
        ]
        
        for scenario in scenarios:
            self.total_tests += 1
            
            containers = scenario["containers"]
            search = scenario["search"]
            expected = scenario["expected_action"]
            
            # Simulate smart detection logic
            if '-' in search and search.endswith('usdt'):
                # Direct container name
                if search in containers:
                    action = "direct_access"
                else:
                    action = "not_found"
            else:
                # Symbol search
                base_symbol = search.lower()
                matches = [c for c in containers if 
                          c.endswith(f"-{base_symbol}usdt") or c == f"{base_symbol}usdt"]
                
                if len(matches) == 0:
                    action = "not_found"
                elif len(matches) == 1:
                    action = "direct_access"
                else:
                    action = "show_selection"
            
            if action == expected:
                self.passed_tests += 1
                print(f"✅ {scenario['name']}: {action}")
            else:
                print(f"❌ {scenario['name']}: Expected {expected}, got {action}")
    
    def print_summary(self):
        """Print test summary"""
        print("\n" + "=" * 60)
        print("📊 **INTEGRATION TEST SUMMARY**")
        print("=" * 60)
        print(f"Total tests: {self.total_tests}")
        print(f"Passed: {self.passed_tests}")
        print(f"Failed: {self.total_tests - self.passed_tests}")
        print(f"Success rate: {(self.passed_tests/self.total_tests)*100:.1f}%")
        
        if self.passed_tests == self.total_tests:
            print("\n🎉 **ALL INTEGRATION TESTS PASSED!**")
            print("✅ Multi-profile support is ready for production")
        else:
            print(f"\n❌ **{self.total_tests - self.passed_tests} TESTS FAILED**")
            print("⚠️  Fix issues before deploying")
    
    async def cleanup(self):
        """Clean up test files"""
        print(f"\n🧹 Cleaning up {len(self.test_configs)} test config files...")
        
        for config_path in self.test_configs:
            try:
                if config_path.exists():
                    config_path.unlink()
                    print(f"✅ Removed: {config_path.name}")
            except Exception as e:
                print(f"❌ Failed to remove {config_path.name}: {e}")

async def main():
    """Main test runner"""
    test_suite = MultiProfileIntegrationTest()
    success = await test_suite.run_all_tests()
    
    print(f"\n{'='*60}")
    if success:
        print("🎉 **PHASE 2 INTEGRATION TESTS: PASSED**")
        print("✅ Ready for end-to-end testing")
    else:
        print("❌ **PHASE 2 INTEGRATION TESTS: FAILED**") 
        print("⚠️  Fix issues before proceeding")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
