#!/usr/bin/env python3
"""
Test system commands: start-all, stop-all, system-status

This test suite validates the new system management commands that were implemented
to provide comprehensive system control and monitoring capabilities.

Features tested:
- system-status: Comprehensive system health check
- start-all: Complete system startup with error handling
- stop-all: Graceful shutdown of all services
- Improved error messages with Vietnamese translations
- Standardized helper functions and messaging
"""

import subprocess
import sys
import os
import time
import json
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def run_command(cmd, timeout=30):
    """Run shell command and return result"""
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            capture_output=True, 
            text=True, 
            timeout=timeout,
            cwd=project_root
        )
        return {
            'returncode': result.returncode,
            'stdout': result.stdout,
            'stderr': result.stderr
        }
    except subprocess.TimeoutExpired:
        return {
            'returncode': 124,  # Timeout exit code
            'stdout': '',
            'stderr': 'Command timed out'
        }

def test_system_status():
    """Test system-status command"""
    print("🧪 Testing system-status command...")
    
    result = run_command("./bot.sh system-status")
    
    # Should always return (even if Docker not available)
    assert result['returncode'] in [0, 1], f"Unexpected return code: {result['returncode']}"
    
    # Should contain expected sections
    stdout = result['stdout']
    expected_sections = [
        "AutoTrader System Status",
        "Docker Status:",
        "Docker Images Status:",
        "Container Status:",
        "Environment Status:",
        "Credentials Status:",
        "Configuration Status:",
        "Overall Health:"
    ]
    
    for section in expected_sections:
        assert section in stdout, f"Missing section: {section}"
    
    # Should contain health percentage
    assert "System Health:" in stdout, "Missing health percentage"
    
    print("✅ system-status test passed")

def test_start_all():
    """Test start-all command"""
    print("🧪 Testing start-all command...")
    
    result = run_command("./bot.sh start-all")
    
    # Should handle Docker not available gracefully
    if "Docker is required" in result['stdout']:
        assert result['returncode'] == 1, "Should return 1 when Docker not available"
        print("✅ start-all correctly handles Docker unavailable")
    else:
        # If Docker is available, should proceed
        assert "Starting Complete AutoTrader System" in result['stdout']
        print("✅ start-all test passed")

def test_stop_all():
    """Test stop-all command"""
    print("🧪 Testing stop-all command...")
    
    result = run_command("./bot.sh stop-all")
    
    # Should handle Docker not available gracefully
    if "Docker is not available" in result['stdout']:
        assert result['returncode'] == 1, "Should return 1 when Docker not available"
        print("✅ stop-all correctly handles Docker unavailable")
    else:
        # If Docker is available, should proceed
        assert "Stopping All AutoTrader Services" in result['stdout']
        print("✅ stop-all test passed")

def test_error_messages():
    """Test improved error messages"""
    print("🧪 Testing improved error messages...")
    
    # Test invalid command
    result = run_command("./bot.sh invalid-command")
    assert result['returncode'] == 1, "Should return 1 for invalid command"
    assert "Lệnh không hợp lệ" in result['stderr'], "Should show Vietnamese error message"
    assert "Các lệnh phổ biến" in result['stdout'], "Should show command suggestions"
    
    # Test missing parameter
    result = run_command("./bot.sh status")
    assert result['returncode'] == 1, "Should return 1 for missing parameter"
    assert "Cần tên symbol hoặc container" in result['stderr'], "Should show Vietnamese error message"
    
    print("✅ Error messages test passed")

def test_help_functions():
    """Test standardized helper functions"""
    print("🧪 Testing helper functions...")
    
    # Test help command
    result = run_command("./bot.sh help")
    assert result['returncode'] == 0, "Help should return 0"
    assert "AutoTrader Bot" in result['stdout'], "Should show help text"
    
    # Test version command
    result = run_command("./bot.sh version")
    assert result['returncode'] == 0, "Version should return 0"
    assert "AutoTrader Bot" in result['stdout'], "Should show version info"
    
    print("✅ Helper functions test passed")

def main():
    """Run all system command tests"""
    print("🚀 Running System Commands Tests")
    print("=" * 50)
    
    try:
        test_system_status()
        test_start_all()
        test_stop_all()
        test_error_messages()
        test_help_functions()
        
        print("\n🎉 All system command tests passed!")
        return 0
        
    except AssertionError as e:
        print(f"\n❌ Test failed: {e}")
        return 1
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())