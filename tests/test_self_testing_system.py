#!/usr/bin/env python3
"""
Test script for Telegram bot self-testing system
Tests the /selftest, /simulate, and /testcmd commands
"""

import asyncio
import os
import sys

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.infrastructure.telegram.telegram_api_client import TelegramAPIClient

async def test_self_testing_system():
    """Test the self-testing capabilities of the Telegram bot"""
    
    # Get credentials from environment
    token = os.environ.get('TELEGRAM_BOT_TOKEN')
    chat_id = os.environ.get('TELEGRAM_CHAT_ID')
    
    if not token or not chat_id:
        print("❌ Missing TELEGRAM_BOT_TOKEN or TELEGRAM_CHAT_ID")
        print("💡 Set them with:")
        print("   export TELEGRAM_BOT_TOKEN='your_bot_token'")
        print("   export TELEGRAM_CHAT_ID='your_chat_id'")
        return 1
    
    client = TelegramAPIClient(token)
    
    print("🧪 Testing Telegram Bot Self-Testing System")
    print("=" * 50)
    print(f"Bot Token: {token[:10]}...")
    print(f"Chat ID: {chat_id}")
    print()
    
    # Test 1: Basic connectivity
    print("📋 Test 1: Basic Connectivity")
    print("-" * 30)
    try:
        await client.send_message(chat_id, "🧪 **Self-Testing System Test Started**\n\nTesting bot self-testing capabilities...")
        print("✅ Basic connectivity: OK")
        await asyncio.sleep(2)
    except Exception as e:
        print(f"❌ Basic connectivity failed: {e}")
        return 1
    
    # Test 2: Test /selftest command
    print("\n📋 Test 2: /selftest Command")
    print("-" * 30)
    try:
        await client.send_message(chat_id, "/selftest")
        print("✅ /selftest command sent")
        print("   → Bot should now run comprehensive self-tests")
        print("   → Check chat for test progress messages")
        await asyncio.sleep(15)  # Wait for selftest to complete
    except Exception as e:
        print(f"❌ /selftest failed: {e}")
    
    # Test 3: Test /simulate command
    print("\n📋 Test 3: /simulate Command")
    print("-" * 30)
    simulate_tests = [
        "/help",
        "/createbot", 
        "/list",
        "Hello World"
    ]
    
    for test_msg in simulate_tests:
        try:
            command = f"/simulate {test_msg}"
            await client.send_message(chat_id, command)
            print(f"✅ Sent: {command}")
            await asyncio.sleep(3)  # Wait between simulations
        except Exception as e:
            print(f"❌ Failed to send /simulate {test_msg}: {e}")
    
    # Test 4: Test /testcmd command
    print("\n📋 Test 4: /testcmd Command")
    print("-" * 30)
    testcmd_tests = [
        "/help",
        "/addcreds",
        "/listcreds",
        "/status"
    ]
    
    for test_cmd in testcmd_tests:
        try:
            command = f"/testcmd {test_cmd}"
            await client.send_message(chat_id, command)
            print(f"✅ Sent: {command}")
            await asyncio.sleep(3)  # Wait between tests
        except Exception as e:
            print(f"❌ Failed to send /testcmd {test_cmd}: {e}")
    
    # Test 5: Test error handling
    print("\n📋 Test 5: Error Handling")
    print("-" * 30)
    error_tests = [
        "/testcmd",  # Missing argument
        "/simulate",  # Missing argument
        "/testcmd /nonexistent",  # Non-existent command
    ]
    
    for error_test in error_tests:
        try:
            await client.send_message(chat_id, error_test)
            print(f"✅ Sent error test: {error_test}")
            await asyncio.sleep(2)
        except Exception as e:
            print(f"❌ Failed to send error test {error_test}: {e}")
    
    # Final summary
    print("\n📋 Test Summary")
    print("-" * 30)
    await client.send_message(
        chat_id, 
        "✅ **Self-Testing System Test Complete**\n\n"
        "📊 **Tests Performed:**\n"
        "• `/selftest` - Comprehensive auto-test\n"
        "• `/simulate` - Message simulation\n"
        "• `/testcmd` - Command testing\n"
        "• Error handling tests\n\n"
        "🔍 **Check logs and messages above for results!**"
    )
    
    print("✅ All self-testing system tests completed!")
    print("📱 Check your Telegram chat for detailed results")
    print("🔍 Monitor bot logs for simulation details")
    
    return 0

async def test_individual_commands():
    """Test individual self-testing commands"""

    token = os.environ.get('TELEGRAM_BOT_TOKEN')

    if not token:
        print("❌ Missing TELEGRAM_BOT_TOKEN")
        print("💡 This test requires a running Telegram bot to test against")
        return 1

    client = TelegramAPIClient(token)
    
    print("\n🎯 Individual Command Tests")
    print("=" * 30)
    
    # Test specific scenarios
    scenarios = [
        ("Basic Help", "/testcmd /help"),
        ("Create Bot Wizard", "/simulate /createbot"),
        ("List Credentials", "/testcmd /listcreds"),
        ("Add Credentials", "/simulate /addcreds"),
        ("Bot Status", "/testcmd /status"),
        ("Invalid Command", "/testcmd /invalid"),
        ("Text Message", "/simulate This is a test message"),
    ]
    
    for scenario_name, command in scenarios:
        print(f"\n🧪 Testing: {scenario_name}")
        try:
            await client.send_message(chat_id, command)
            print(f"✅ Sent: {command}")
            await asyncio.sleep(3)
        except Exception as e:
            print(f"❌ Failed: {e}")
    
    return 0

def main():
    """Main function"""
    print("🤖 Telegram Bot Self-Testing System Tester")
    print("=" * 50)
    
    # Check environment
    if not os.environ.get('TELEGRAM_BOT_TOKEN'):
        print("❌ TELEGRAM_BOT_TOKEN not set")
        return 1
    
    if not os.environ.get('TELEGRAM_CHAT_ID'):
        print("❌ TELEGRAM_CHAT_ID not set")
        return 1
    
    try:
        # Run main tests
        result = asyncio.run(test_self_testing_system())
        
        # Run individual tests
        if result == 0:
            result = asyncio.run(test_individual_commands())
        
        return result
        
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        return 1
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
