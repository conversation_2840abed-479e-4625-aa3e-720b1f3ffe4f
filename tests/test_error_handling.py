#!/usr/bin/env python3
"""
Test standardized error handling and messaging
"""

import subprocess
import sys
import re
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def run_command(cmd, timeout=10):
    """Run shell command and return result"""
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            capture_output=True, 
            text=True, 
            timeout=timeout,
            cwd=project_root
        )
        return {
            'returncode': result.returncode,
            'stdout': result.stdout,
            'stderr': result.stderr
        }
    except subprocess.TimeoutExpired:
        return {
            'returncode': 124,
            'stdout': '',
            'stderr': 'Command timed out'
        }

def test_vietnamese_error_messages():
    """Test Vietnamese error messages"""
    print("🧪 Testing Vietnamese error messages...")
    
    test_cases = [
        {
            'command': './bot.sh invalid-command',
            'expected_patterns': [
                r'<PERSON><PERSON><PERSON> không hợp lệ',
                r'<PERSON><PERSON><PERSON>nh ph<PERSON> biến'
            ]
        },
        {
            'command': './bot.sh status',
            'expected_patterns': [
                r'Cần tên symbol hoặc container',
                r'<PERSON><PERSON>ch sử dụng'
            ]
        },
        {
            'command': './bot.sh logs',
            'expected_patterns': [
                r'Cần tên symbol hoặc container',
                r'Ví dụ'
            ]
        },
        {
            'command': './bot.sh stop',
            'expected_patterns': [
                r'Cần tên symbol hoặc container'
            ]
        },
        {
            'command': './bot.sh restart',
            'expected_patterns': [
                r'Cần tên symbol hoặc container'
            ]
        },
        {
            'command': './bot.sh remove',
            'expected_patterns': [
                r'Cần tên symbol hoặc container'
            ]
        },
        {
            'command': './bot.sh assets',
            'expected_patterns': [
                r'Cần tên profile'
            ]
        },
        {
            'command': './bot.sh load-credentials',
            'expected_patterns': [
                r'Cần tên profile'
            ]
        }
    ]
    
    for test_case in test_cases:
        result = run_command(test_case['command'])
        
        # Should return error code
        assert result['returncode'] != 0, f"Command should fail: {test_case['command']}"
        
        # Check error patterns
        combined_output = result['stdout'] + result['stderr']
        for pattern in test_case['expected_patterns']:
            assert re.search(pattern, combined_output), \
                f"Pattern '{pattern}' not found in output for command: {test_case['command']}"
    
    print("✅ Vietnamese error messages test passed")

def test_error_message_format():
    """Test consistent error message format"""
    print("🧪 Testing error message format...")
    
    # Test that error messages use consistent emojis
    result = run_command('./bot.sh invalid-command')
    combined_output = result['stdout'] + result['stderr']
    
    # Should contain error emoji
    assert '❌' in combined_output, "Should contain error emoji"
    
    # Should contain suggestion emoji
    assert '💡' in combined_output, "Should contain suggestion emoji"
    
    # Should contain help reference
    assert 'help' in combined_output.lower(), "Should reference help command"
    
    print("✅ Error message format test passed")

def test_contextual_suggestions():
    """Test contextual suggestions in error messages"""
    print("🧪 Testing contextual suggestions...")
    
    test_cases = [
        {
            'command': './bot.sh status',
            'expected_suggestions': [
                'Cách sử dụng',
                'Ví dụ',
                'Smart detection'
            ]
        },
        {
            'command': './bot.sh assets',
            'expected_suggestions': [
                'list-credentials',
                'profiles có sẵn'
            ]
        }
    ]
    
    for test_case in test_cases:
        result = run_command(test_case['command'])
        combined_output = result['stdout'] + result['stderr']
        
        for suggestion in test_case['expected_suggestions']:
            assert suggestion in combined_output, \
                f"Missing suggestion '{suggestion}' for command: {test_case['command']}"
    
    print("✅ Contextual suggestions test passed")

def test_help_command_integration():
    """Test help command integration in error messages"""
    print("🧪 Testing help command integration...")
    
    # Test that error messages reference help
    result = run_command('./bot.sh invalid-command')
    combined_output = result['stdout'] + result['stderr']
    
    # Should reference help command
    assert './bot.sh help' in combined_output, "Should reference help command"
    
    # Test that help command actually works
    help_result = run_command('./bot.sh help')
    assert help_result['returncode'] == 0, "Help command should work"
    assert 'AutoTrader Bot' in help_result['stdout'], "Help should show bot info"
    
    print("✅ Help command integration test passed")

def test_system_status_integration():
    """Test system-status command integration in error messages"""
    print("🧪 Testing system-status integration...")
    
    # Test that error messages reference system-status
    result = run_command('./bot.sh invalid-command')
    combined_output = result['stdout'] + result['stderr']
    
    # Should reference system-status command
    assert 'system-status' in combined_output, "Should reference system-status command"
    
    # Test that system-status command actually works
    status_result = run_command('./bot.sh system-status')
    # Should not fail completely (even if Docker not available)
    assert status_result['returncode'] in [0, 1], "System-status should handle errors gracefully"
    assert 'AutoTrader System Status' in status_result['stdout'], "Should show system status"
    
    print("✅ System-status integration test passed")

def test_consistent_emoji_usage():
    """Test consistent emoji usage across commands"""
    print("🧪 Testing consistent emoji usage...")

    # Test various commands and check emoji consistency
    error_commands = [
        './bot.sh invalid-command',
        './bot.sh status',  # Missing parameter
        './bot.sh logs'     # Missing parameter
    ]

    info_commands = [
        './bot.sh system-status'
    ]

    emoji_patterns = {
        '❌': 'error',
        '✅': 'success',
        '⚠️': 'warning',
        'ℹ️': 'info',
        '🔄': 'step',
        '💡': 'suggestion'
    }

    # Test error commands - should have error emojis
    for cmd in error_commands:
        result = run_command(cmd)
        combined_output = result['stdout'] + result['stderr']

        # Error commands should have error emoji
        assert result['returncode'] != 0, f"Command should fail: {cmd}"
        assert '❌' in combined_output, f"Error command should have ❌ emoji: {cmd}"

    # Test info commands - should have some emojis
    for cmd in info_commands:
        result = run_command(cmd)
        combined_output = result['stdout'] + result['stderr']

        # Info commands should have some emoji for better UX
        has_emoji = any(emoji in combined_output for emoji in emoji_patterns.keys())
        assert has_emoji, f"Info command should have emojis for better UX: {cmd}"

    print("✅ Consistent emoji usage test passed")

def main():
    """Run all error handling tests"""
    print("🚀 Running Error Handling Tests")
    print("=" * 50)
    
    try:
        test_vietnamese_error_messages()
        test_error_message_format()
        test_contextual_suggestions()
        test_help_command_integration()
        test_system_status_integration()
        test_consistent_emoji_usage()
        
        print("\n🎉 All error handling tests passed!")
        return 0
        
    except AssertionError as e:
        print(f"\n❌ Test failed: {e}")
        return 1
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())