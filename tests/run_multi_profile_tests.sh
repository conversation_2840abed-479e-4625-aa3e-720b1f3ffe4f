#!/bin/bash
"""
Test runner for multi-profile container support
Runs all Phase 2 integration tests
"""

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Test results
TOTAL_SUITES=0
PASSED_SUITES=0
FAILED_SUITES=0

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

print_header() {
    echo -e "\n${BLUE}$1${NC}"
    echo "$(printf '=%.0s' {1..80})"
}

print_suite() {
    echo -e "\n${CYAN}🧪 Running: $1${NC}"
    echo "$(printf -- '-%.0s' {1..60})"
}

run_test_suite() {
    local suite_name="$1"
    local test_command="$2"
    local description="$3"
    
    print_suite "$suite_name"
    echo "Description: $description"
    echo "Command: $test_command"
    echo ""
    
    ((TOTAL_SUITES++))
    
    # Run the test
    if eval "$test_command"; then
        ((PASSED_SUITES++))
        echo -e "${GREEN}✅ $suite_name: PASSED${NC}"
    else
        ((FAILED_SUITES++))
        echo -e "${RED}❌ $suite_name: FAILED${NC}"
    fi
}

check_prerequisites() {
    print_header "🔍 CHECKING PREREQUISITES"
    
    local all_good=true
    
    # Check Python
    if command -v python3 &> /dev/null; then
        echo -e "${GREEN}✅ Python 3 is available${NC}"
    else
        echo -e "${RED}❌ Python 3 not found${NC}"
        all_good=false
    fi
    
    # Check Docker (optional)
    if command -v docker &> /dev/null; then
        if docker version &> /dev/null; then
            echo -e "${GREEN}✅ Docker is available and running${NC}"
        else
            echo -e "${YELLOW}⚠️  Docker installed but daemon not running${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️  Docker not available (some tests will be skipped)${NC}"
    fi
    
    # Check bot.sh
    if [ -f "$PROJECT_ROOT/bot.sh" ]; then
        echo -e "${GREEN}✅ bot.sh found${NC}"
    else
        echo -e "${RED}❌ bot.sh not found in project root${NC}"
        all_good=false
    fi
    
    # Check src directory
    if [ -d "$PROJECT_ROOT/src" ]; then
        echo -e "${GREEN}✅ src directory found${NC}"
    else
        echo -e "${RED}❌ src directory not found${NC}"
        all_good=false
    fi
    
    if [ "$all_good" = false ]; then
        echo -e "\n${RED}❌ Prerequisites not met. Please fix issues before running tests.${NC}"
        exit 1
    fi
    
    echo -e "\n${GREEN}✅ All prerequisites met${NC}"
}

run_all_tests() {
    print_header "🚀 PHASE 2: MULTI-PROFILE INTEGRATION TESTS"
    
    # Change to project root for consistent paths
    cd "$PROJECT_ROOT"
    
    # Test Suite 1: Integration Tests
    run_test_suite \
        "Integration Tests" \
        "python3 tests/test_multi_profile_integration.py" \
        "Core integration tests for multi-profile functionality"
    
    # Test Suite 2: End-to-End Tests
    run_test_suite \
        "End-to-End Tests" \
        "python3 tests/test_multi_profile_e2e.py" \
        "End-to-end tests with real Docker environment"
    
    # Test Suite 3: Bot.sh Integration Tests (Simplified)
    run_test_suite \
        "Bot.sh Integration Tests" \
        "bash tests/test_multi_profile_botsh_simple.sh" \
        "Bot.sh core functionality and structure tests"
    
    # Test Suite 4: Migration Tests
    run_test_suite \
        "Migration Tests" \
        "python3 tests/test_multi_profile_migration.py" \
        "Migration from old format to new format tests"
}

print_summary() {
    print_header "📊 PHASE 2 TEST SUMMARY"
    
    echo "Total test suites: $TOTAL_SUITES"
    echo "Passed: $PASSED_SUITES"
    echo "Failed: $FAILED_SUITES"
    
    if [ $TOTAL_SUITES -gt 0 ]; then
        local success_rate=$((PASSED_SUITES * 100 / TOTAL_SUITES))
        echo "Success rate: ${success_rate}%"
    fi
    
    echo ""
    
    if [ $FAILED_SUITES -eq 0 ]; then
        echo -e "${GREEN}🎉 ALL PHASE 2 TESTS PASSED!${NC}"
        echo -e "${GREEN}✅ Multi-profile support is ready for production${NC}"
        echo ""
        echo -e "${BLUE}📋 Next Steps:${NC}"
        echo "1. Deploy to staging environment"
        echo "2. Run production validation tests"
        echo "3. Update documentation"
        echo "4. Train users on new features"
        return 0
    else
        echo -e "${RED}❌ $FAILED_SUITES TEST SUITE(S) FAILED${NC}"
        echo -e "${RED}⚠️  Fix issues before proceeding to production${NC}"
        echo ""
        echo -e "${YELLOW}📋 Recommended Actions:${NC}"
        echo "1. Review failed test output above"
        echo "2. Fix identified issues"
        echo "3. Re-run tests: ./tests/run_multi_profile_tests.sh"
        echo "4. Consider running individual test suites for debugging"
        return 1
    fi
}

show_help() {
    echo "Multi-Profile Test Runner"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help     Show this help message"
    echo "  -v, --verbose  Enable verbose output"
    echo "  --integration  Run only integration tests"
    echo "  --e2e          Run only end-to-end tests"
    echo "  --botsh        Run only bot.sh tests"
    echo "  --migration    Run only migration tests"
    echo ""
    echo "Examples:"
    echo "  $0                    # Run all tests"
    echo "  $0 --integration      # Run only integration tests"
    echo "  $0 --verbose          # Run all tests with verbose output"
}

main() {
    # Parse command line arguments
    local run_all=true
    local verbose=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -v|--verbose)
                verbose=true
                shift
                ;;
            --integration)
                run_all=false
                run_test_suite "Integration Tests" "python3 tests/test_multi_profile_integration.py" "Core integration tests"
                shift
                ;;
            --e2e)
                run_all=false
                run_test_suite "End-to-End Tests" "python3 tests/test_multi_profile_e2e.py" "End-to-end tests"
                shift
                ;;
            --botsh)
                run_all=false
                run_test_suite "Bot.sh Tests" "bash tests/test_multi_profile_botsh.sh" "Bot.sh integration tests"
                shift
                ;;
            --migration)
                run_all=false
                run_test_suite "Migration Tests" "python3 tests/test_multi_profile_migration.py" "Migration tests"
                shift
                ;;
            *)
                echo "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # Set verbose mode
    if [ "$verbose" = true ]; then
        set -x
    fi
    
    # Check prerequisites
    check_prerequisites
    
    # Run tests
    if [ "$run_all" = true ]; then
        run_all_tests
    fi
    
    # Print summary and exit
    if print_summary; then
        exit 0
    else
        exit 1
    fi
}

# Run main function with all arguments
main "$@"
