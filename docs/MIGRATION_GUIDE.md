# 🔄 Migration Guide: Legacy to Multi-Profile

## 📋 Overview

Hướng dẫn này gi<PERSON><PERSON> bạn migrate từ legacy container format sang multi-profile format một cách an toàn. Migration không bắt buộc - legacy containers vẫn hoạt động bình thường.

## ⚠️ Before You Start

### **Backup First!**
```bash
# Backup configs
cp -r configs/ configs_backup_$(date +%Y%m%d)/

# Backup container data (if any)
docker exec btcusdt tar -czf /tmp/backup.tar.gz /app/data/ 2>/dev/null || true
```

### **Check Current Setup**
```bash
# List current containers
docker ps -a --format "table {{.Names}}\t{{.Status}}\t{{.Image}}"

# Check configs
ls -la configs/
```

## 🎯 Migration Strategies

### **Strategy 1: Gradual Migration (Recommended)**
- Keep existing containers running
- Create new profile-based containers alongside
- Migrate one by one when ready

### **Strategy 2: Full Migration**
- Stop all containers
- Rename/migrate all at once
- Restart with new format

### **Strategy 3: Hybrid Approach**
- Keep some legacy containers
- Use profiles for new containers only

## 🚀 Step-by-Step Migration

### **Step 1: Identify Current Containers**

```bash
# List trading containers
docker ps -a --format "{{.Names}}" | grep usdt

# Example output:
# btcusdt
# ethusdt
# solusdt
```

### **Step 2: Plan Your Profiles**

**Example Planning:**
```
Current: btcusdt
New Options:
├─ main-btcusdt (primary account)
├─ backup-btcusdt (secondary account)  
└─ test-btcusdt (paper trading)

Current: ethusdt
New Options:
├─ main-ethusdt (primary account)
└─ dca-ethusdt (DCA strategy)
```

### **Step 3: Create New Profile Containers**

#### **Option A: Alongside Existing (Safe)**
```bash
# Keep btcusdt running, create new profile container
./bot.sh start btc --profile main --amount 1000

# Result: Both btcusdt and main-btcusdt running
```

#### **Option B: Replace Existing**
```bash
# Stop old container
./bot.sh stop btc  # This stops btcusdt

# Create new profile container
./bot.sh start btc --profile main --amount 1000

# Remove old container (optional)
./bot.sh remove btc --force
```

### **Step 4: Migrate Configuration**

#### **Manual Config Migration**
```bash
# Copy existing config
cp configs/btc.config.json configs/main-btc.config.json

# Edit new config to add profile
vim configs/main-btc.config.json
```

**Add profile field:**
```json
{
  "symbol": "BTC",
  "profile": "main",
  "amount": 1000,
  "migrated_from": "btc.config.json"
}
```

#### **Automated Config Migration**
```bash
# Use migration script (when available)
./scripts/migrate_config.py btc.config.json main
```

### **Step 5: Test New Containers**

```bash
# Test new container
./bot.sh status main-btc
./bot.sh logs main-btc 50

# Test Telegram commands
/status main-btc
/logs main-btc
```

### **Step 6: Cleanup (Optional)**

```bash
# After confirming new container works
./bot.sh stop btc        # Stop legacy container
./bot.sh remove btc      # Remove legacy container
rm configs/btc.config.json  # Remove legacy config
```

## 📋 Migration Checklist

### **Pre-Migration**
- [ ] Backup all configs
- [ ] Document current container names
- [ ] Plan profile names
- [ ] Test new container creation
- [ ] Verify Telegram bot access

### **During Migration**
- [ ] Create new profile containers
- [ ] Copy/migrate configurations
- [ ] Test new containers functionality
- [ ] Verify smart detection works
- [ ] Test Telegram bot commands

### **Post-Migration**
- [ ] Monitor new containers for 24h
- [ ] Update documentation/notes
- [ ] Clean up old containers (optional)
- [ ] Update monitoring/alerts
- [ ] Train team on new commands

## 🔧 Migration Scripts

### **Container Migration Script**

```bash
#!/bin/bash
# migrate_container.sh

OLD_SYMBOL="$1"
NEW_PROFILE="$2"

if [[ -z "$OLD_SYMBOL" || -z "$NEW_PROFILE" ]]; then
    echo "Usage: $0 <symbol> <profile>"
    echo "Example: $0 btc main"
    exit 1
fi

echo "🔄 Migrating ${OLD_SYMBOL}usdt to ${NEW_PROFILE}-${OLD_SYMBOL}usdt"

# Stop old container
echo "Stopping old container..."
./bot.sh stop "$OLD_SYMBOL"

# Get old config
OLD_CONFIG="configs/${OLD_SYMBOL}.config.json"
NEW_CONFIG="configs/${NEW_PROFILE}-${OLD_SYMBOL}.config.json"

if [[ -f "$OLD_CONFIG" ]]; then
    echo "Migrating config..."
    cp "$OLD_CONFIG" "$NEW_CONFIG"
    
    # Add profile field (requires jq)
    if command -v jq >/dev/null; then
        jq --arg profile "$NEW_PROFILE" '. + {profile: $profile, migrated_from: "'$OLD_CONFIG'"}' "$NEW_CONFIG" > "${NEW_CONFIG}.tmp"
        mv "${NEW_CONFIG}.tmp" "$NEW_CONFIG"
    fi
fi

# Create new container
echo "Creating new container..."
./bot.sh start "$OLD_SYMBOL" --profile "$NEW_PROFILE"

echo "✅ Migration completed!"
echo "Test with: ./bot.sh status ${NEW_PROFILE}-${OLD_SYMBOL}"
```

### **Batch Migration Script**

```bash
#!/bin/bash
# batch_migrate.sh

# Define migrations
declare -A MIGRATIONS=(
    ["btc"]="main"
    ["eth"]="main"
    ["sol"]="dca"
)

for symbol in "${!MIGRATIONS[@]}"; do
    profile="${MIGRATIONS[$symbol]}"
    echo "🔄 Migrating $symbol to profile $profile"
    ./migrate_container.sh "$symbol" "$profile"
    sleep 5  # Wait between migrations
done

echo "✅ Batch migration completed!"
```

## 🚨 Troubleshooting

### **Common Issues**

#### **"Container name already exists"**
```bash
# Check existing containers
docker ps -a --format "{{.Names}}" | grep btc

# Use different profile name
./bot.sh start btc --profile main2 --amount 1000
```

#### **"Config file not found"**
```bash
# Create new config manually
./bot.sh start btc --profile main --amount 1000
# This creates the config automatically
```

#### **"Old container won't stop"**
```bash
# Force stop
./bot.sh stop btc --force

# Or use Docker directly
docker stop btcusdt
docker rm btcusdt
```

#### **"Smart detection not working"**
```bash
# Check container names
docker ps -a --format "{{.Names}}" | grep usdt

# Verify naming convention
# Should be: profile-symbolusdt (e.g., main-btcusdt)
```

### **Rollback Procedure**

If migration fails:

```bash
# Stop new container
./bot.sh stop main-btc

# Restore old config
cp configs_backup/btc.config.json configs/

# Restart old container
./bot.sh start btc --amount 1000

# Remove failed new container
./bot.sh remove main-btc --force
```

## 📊 Migration Examples

### **Example 1: Simple Migration**
```bash
# Before
docker ps: btcusdt, ethusdt

# Migration
./migrate_container.sh btc main
./migrate_container.sh eth main

# After  
docker ps: main-btcusdt, main-ethusdt
```

### **Example 2: Multi-Profile Migration**
```bash
# Before
docker ps: btcusdt

# Migration (create multiple profiles)
./bot.sh start btc --profile main --amount 1000
./bot.sh start btc --profile test --amount 100
./bot.sh start btc --profile backup --amount 500

# After
docker ps: btcusdt, main-btcusdt, test-btcusdt, backup-btcusdt

# Optional: Remove legacy
./bot.sh remove btc --force
```

### **Example 3: Gradual Migration**
```bash
# Week 1: Create new containers alongside old
./bot.sh start btc --profile main --amount 1000

# Week 2: Test and monitor
./bot.sh logs main-btc
/status main-btc

# Week 3: Stop old container
./bot.sh stop btc

# Week 4: Remove old container
./bot.sh remove btc --force
```

## 📞 Support

- **Issues:** Check troubleshooting section first
- **Rollback:** Follow rollback procedure
- **Help:** Use `/help` in Telegram bot
- **Documentation:** [MULTI_PROFILE_GUIDE.md](MULTI_PROFILE_GUIDE.md)

---

**🎯 Take your time - migration is optional and reversible!**
