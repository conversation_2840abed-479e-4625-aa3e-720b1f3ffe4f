# 🔑 Multi-Profile Container Support Guide

## 📋 Overview

Multi-Profile Container Support cho phép bạn chạy nhiều bot trading với cùng một symbol nhưng sử dụng các tài kho<PERSON>n, chi<PERSON><PERSON> lư<PERSON>, hoặc môi trường khác nhau. Tính năng này giải quyết vấn đề giới hạn "một symbol một container" của phiên bản cũ.

## 🎯 Key Features

### ✅ **Multiple Profiles per Symbol**
- Chạy nhiều bot BTC với các tài khoản khác nhau
- Tách biệt chiến lược trading (scalping, swing, DCA)
- <PERSON>ân chia môi trường (dev, staging, prod)

### ✅ **Smart Detection**
- Tự động phát hiện containers theo symbol
- Hiển thị selection UI khi có nhiều containers
- Hỗ trợ truy cập trực tiếp bằng container name

### ✅ **Backward Compatibility**
- Containers cũ vẫn hoạt động bình thường
- Không cần migration bắt buộc
- Hỗ trợ cả format cũ và mới

## 🚀 Getting Started

### **1. Container Naming Convention**

#### **New Format (with Profile)**
```bash
{profile}-{symbol}usdt
```

**Examples:**
- `main-btcusdt` - Profile "main" trading BTC
- `test-btcusdt` - Profile "test" trading BTC  
- `backup-ethusdt` - Profile "backup" trading ETH
- `scalp-solusdt` - Profile "scalp" trading SOL

#### **Legacy Format (Backward Compatible)**
```bash
{symbol}usdt
```

**Examples:**
- `btcusdt` - Legacy BTC container
- `ethusdt` - Legacy ETH container

### **2. Config File Naming**

#### **New Format**
```bash
{profile}-{symbol}.config.json
```

**Examples:**
- `main-btc.config.json`
- `test-eth.config.json`
- `backup-sol.config.json`

#### **Legacy Format**
```bash
{symbol}.config.json
```

**Examples:**
- `btc.config.json`
- `eth.config.json`

## 💻 Command Usage

### **Bot.sh Commands**

#### **Create Bot with Profile**
```bash
# New format with profile
./bot.sh start btc --profile main --amount 100

# Legacy format (no profile)
./bot.sh start eth --amount 50
```

#### **Smart Detection Commands**
```bash
# Smart detection - shows selection if multiple containers
./bot.sh status btc
./bot.sh logs btc 100
./bot.sh stop btc
./bot.sh restart btc

# Direct access - specific container
./bot.sh status main-btc
./bot.sh logs main-btc 100
./bot.sh stop main-btc
./bot.sh restart main-btc
```

### **Telegram Bot Commands**

#### **Create Bot**
```
/createbot
```
- Wizard sẽ hỏi profile name
- Để trống để sử dụng legacy format

#### **List Bots**
```
/list
```
- Hiển thị bots được nhóm theo profile
- Phân biệt legacy và new format

#### **Smart Detection**
```
/status btc          # Shows selection if multiple
/logs btc 100        # Shows selection if multiple
/stop btc            # Shows confirmation with selection

/status main-btc     # Direct access
/logs main-btc 100   # Direct access
/stop main-btc       # Direct confirmation
```

## 🎯 Use Cases

### **1. Multi-Account Trading**
```bash
# Primary account
./bot.sh start btc --profile main --amount 1000

# Backup account  
./bot.sh start btc --profile backup --amount 500

# Test account
./bot.sh start btc --profile test --amount 100
```

**Result:**
- `main-btcusdt` - Primary trading
- `backup-btcusdt` - Backup trading
- `test-btcusdt` - Paper trading

### **2. Strategy Diversification**
```bash
# Scalping strategy
./bot.sh start btc --profile scalp --amount 200

# Swing trading
./bot.sh start btc --profile swing --amount 800

# DCA strategy
./bot.sh start btc --profile dca --amount 1000
```

**Result:**
- `scalp-btcusdt` - Short-term trades
- `swing-btcusdt` - Medium-term trades  
- `dca-btcusdt` - Long-term accumulation

### **3. Environment Separation**
```bash
# Development
./bot.sh start btc --profile dev --amount 10

# Staging
./bot.sh start btc --profile staging --amount 50

# Production
./bot.sh start btc --profile prod --amount 1000
```

**Result:**
- `dev-btcusdt` - Development testing
- `staging-btcusdt` - Pre-production validation
- `prod-btcusdt` - Live trading

## 🔍 Smart Detection Behavior

### **Single Container Found**
```bash
$ ./bot.sh status btc
📊 Found 1 container: main-btcusdt
[Shows status directly]
```

### **Multiple Containers Found**
```bash
$ ./bot.sh status btc
📊 Found 3 containers for BTC:
  1. main-btcusdt
  2. test-btcusdt  
  3. backup-btcusdt

💡 Use specific container name: ./bot.sh status <container_name>
   Example: ./bot.sh status main-btcusdt
```

### **No Containers Found**
```bash
$ ./bot.sh status doge
❌ No containers found for symbol: DOGE
```

### **Direct Container Access**
```bash
$ ./bot.sh status main-btcusdt
📊 Status main-btcusdt
[Shows status directly]
```

## 📱 Telegram Bot UI

### **List Command with Profiles**
```
📊 **Active Trading Bots (3)**

🔑 **Profile: main**
├─ 🟢 main-btcusdt (BTC) - Running
└─ 🟢 main-ethusdt (ETH) - Running

🔑 **Profile: test**  
├─ 🟡 test-btcusdt (BTC) - Stopped
└─ 🟢 test-solusdt (SOL) - Running

📦 **Legacy Bots**
└─ 🟢 hyperusdt (HYPER) - Running
```

### **Smart Detection Selection**
```
📊 **Found 2 bots for BTC:**

🟢 main-btcusdt (main)
🟡 test-btcusdt (test)

Choose bot for logs:
[📋 Logs main-btcusdt] [📋 Logs test-btcusdt]
[❌ Cancel]
```

## ⚠️ Important Notes

### **Profile Name Rules**
- Alphanumeric characters, dash, underscore only
- Maximum 50 characters
- No reserved names (docker, container, bot, etc.)
- Case sensitive

### **Migration**
- Old containers continue working
- No forced migration required
- Can gradually migrate to new format
- Backup recommended before migration

### **Limitations**
- Profile names must be unique per symbol
- Cannot change profile name after creation
- Container names must follow Docker naming rules

## 🔧 Troubleshooting

### **Common Issues**

#### **"Container not found"**
```bash
# Check exact container name
docker ps -a --format "{{.Names}}" | grep usdt

# Use correct format
./bot.sh status main-btcusdt  # Not main-btc
```

#### **"Multiple containers found"**
```bash
# Use specific container name
./bot.sh logs main-btcusdt 100

# Or use Telegram bot for selection UI
/logs btc
```

#### **"Profile validation failed"**
```bash
# Check profile name rules
./bot.sh start btc --profile "valid-name-123"  # ✅ Valid
./bot.sh start btc --profile "invalid name!"   # ❌ Invalid
```

## 📞 Support

- **Documentation:** [README.md](../README.md)
- **Migration Guide:** [MIGRATION_GUIDE.md](MIGRATION_GUIDE.md)
- **Issues:** GitHub Issues
- **Telegram:** Bot support commands

---

**🎉 Happy Trading with Multi-Profile Support!**
