# AutoTrader Project Overview

## Purpose
AutoTrader là một professional crypto trading bot với chiến lược DCA (Dollar Cost Averaging) và phân tích kỹ thuật tiên tiến. Bot hỗ trợ multi-profile trading, smart detection containers, và tích hợp Telegram bot để quản lý từ xa.

## Key Features
- **Multi-Profile Support**: Chạy nhiều bot cùng symbol với profiles khác nhau
- **Smart Detection**: Tự động phát hiện containers với selection UI
- **DCA Strategy**: Enhanced Dollar Cost Averaging với 7 loại strategies
- **Technical Analysis**: RSI, EMA, Bollinger Bands, MACD
- **Risk Management**: Stop Loss, Take Profit, position sizing
- **Telegram Integration**: Quản lý bot qua Telegram commands
- **Docker Support**: Containerized deployment
- **Auto Update**: Tự động update từ GitHub Gist

## Architecture
Project sử dụng Clean Architecture với các layers:
- **Domain (src/core)**: Business logic, models, strategies
- **Application (src/application)**: Orchestration, managers, events
- **Infrastructure (src/infrastructure)**: External services (exchange, telegram, config)
- **Presentation (src/presentation)**: CLI interfaces

## Tech Stack
- **Language**: Python 3.8+
- **Trading**: CCXT library for exchange integration
- **Analysis**: Pandas, NumPy, TA-Lib for technical analysis
- **Telegram**: python-telegram-bot for bot functionality
- **Containerization**: Docker with multi-stage builds
- **Configuration**: JSON configs with environment variable support
- **Testing**: Pytest with async support

## Main Components
1. **Trading Engine**: Core trading logic và strategy execution
2. **Telegram Bot**: Remote management interface
3. **Risk Management**: Position sizing, stop loss, take profit
4. **Multi-Profile System**: Credential management và container isolation
5. **Docker Integration**: Containerized deployment và management