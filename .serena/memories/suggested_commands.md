# Suggested Commands for AutoTrader Development

## Essential Development Commands

### Setup và Environment
```bash
# Complete environment setup
./bot.sh setup

# Setup Telegram authorization
./bot.sh setup-auth

# Check system status
./bot.sh check

# Build Docker images locally
./bot.sh build
```

### Docker Management
```bash
# List Docker images
./bot.sh images

# Fix Docker image issues
./bot.sh fix-images

# Update images from registry
./bot.sh update-images
```

### Bot Management
```bash
# Start Telegram bot
./bot.sh telegram

# Start trading bot with profile
./bot.sh start btc --profile main --amount 100

# List all containers
./bot.sh list

# Get container status
./bot.sh status btc

# View logs
./bot.sh logs btc 100

# Stop container
./bot.sh stop btc

# Restart container
./bot.sh restart btc
```

### Credential Management
```bash
# List credential profiles
./bot.sh list-credentials

# Store new credentials
./bot.sh store-credentials main "api_key" "api_secret" "Main Account"

# Load credentials
./bot.sh load-credentials main

# View account assets
./bot.sh assets main
```

### Testing Commands
```bash
# Run Python tests
python tests/test_telegram_commands.py
python tests/test_handlers.py
python tests/test_self_testing_system.py

# Run shell tests
bash tests/test_bot.sh
bash tests/test_createbot.sh

# Test Docker functionality
bash tests/test-docker.sh
```

### Development Utilities
```bash
# Use local Docker images for development
export LOCAL=true && ./bot.sh telegram

# Run with Python directly (no Docker)
python main.py --test
python main.py --dashboard

# Upgrade script from GitHub
./bot.sh upgrade

# Install as global command
./bot.sh bind
```

### System Commands (Darwin/macOS)
```bash
# Standard Unix commands
ls -la
cd <directory>
grep -r "pattern" src/
find . -name "*.py" -type f
git status
git log --oneline
```

## Command Priority
1. **Setup**: `./bot.sh setup` - First time setup
2. **Auth**: `./bot.sh setup-auth` - Telegram authorization
3. **Build**: `./bot.sh build` - Build Docker images
4. **Start**: `./bot.sh telegram` - Start Telegram bot
5. **Test**: Run test files to verify functionality