# Code Style and Conventions

## Python Code Style

### General Conventions
- **Python Version**: 3.8+ required
- **Encoding**: UTF-8 with Vietnamese comments allowed
- **Line Length**: Max 120 characters
- **Indentation**: 4 spaces (no tabs)
- **String Quotes**: Single quotes preferred ('string')
- **Trailing Commas**: Not used in JavaScript/TypeScript, optional in Python

### Naming Conventions
- **Files**: snake_case (e.g., `trading_engine.py`)
- **Classes**: PascalCase (e.g., `TradingEngine`)
- **Functions/Methods**: snake_case (e.g., `process_signal`)
- **Variables**: snake_case (e.g., `api_key`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `DEFAULT_AMOUNT`)
- **Private Methods**: Leading underscore (e.g., `_internal_method`)

### Type Hints
- **Required**: All public methods must have type hints
- **Format**: Use typing module imports
```python
from typing import Dict, List, Optional, Tuple, Any
def process_data(data: Dict[str, Any]) -> Optional[List[str]]:
```

### Docstrings
- **Format**: Google style docstrings
- **Required**: All public classes and methods
```python
def calculate_position_size(amount: float, risk_percent: float) -> float:
    """Calculate position size based on risk management.
    
    Args:
        amount: Trading amount in USDT
        risk_percent: Risk percentage (0.01 = 1%)
        
    Returns:
        Calculated position size
        
    Raises:
        ValueError: If amount or risk_percent is invalid
    """
```

### Error Handling
- **Specific Exceptions**: Use specific exception types
- **Logging**: Use structured logging with levels
- **Graceful Degradation**: Handle errors gracefully
```python
try:
    result = risky_operation()
except SpecificException as e:
    self.logger.error(f"Operation failed: {e}")
    return default_value
```

### Async/Await
- **Async Methods**: Use async/await for I/O operations
- **Naming**: Async methods can have same names as sync versions
```python
async def fetch_market_data(symbol: str) -> Dict[str, Any]:
    async with aiohttp.ClientSession() as session:
        # async implementation
```

## File Organization

### Directory Structure
```
src/
├── core/           # Domain logic
├── application/    # Orchestration
├── infrastructure/ # External services
└── presentation/   # Interfaces
```

### Import Order
1. Standard library imports
2. Third-party imports
3. Local application imports
```python
import asyncio
import logging
from typing import Dict

import ccxt
import pandas as pd

from src.core.models import Signal
from src.infrastructure.exchange import ExchangeConnector
```

## Configuration Style

### JSON Configuration
- **Indentation**: 2 spaces
- **Keys**: snake_case
- **Structure**: Nested objects for grouping
```json
{
  "symbol": "BTC/USDT:USDT",
  "risk": {
    "max_position_size": 200.0,
    "default_stop_loss": 2.0
  }
}
```

### Environment Variables
- **Format**: UPPER_SNAKE_CASE
- **Prefixes**: Use component prefixes
```bash
TELEGRAM_BOT_TOKEN="token"
BYBIT_API_KEY="key"
BYBIT_API_SECRET="secret"
```

## Docker Conventions

### Dockerfile
- **Multi-stage builds**: Separate build and runtime stages
- **Layer optimization**: Minimize layers
- **Security**: Non-root user, minimal base images

### Container Naming
- **Format**: `profile-symbolusdt` (e.g., `main-btcusdt`)
- **Legacy**: `symbolusdt` for backward compatibility
- **Services**: `telegram-bot` for Telegram bot

## Testing Conventions

### Test Files
- **Naming**: `test_*.py` for pytest discovery
- **Structure**: Mirror source structure in tests/
- **Async Tests**: Use pytest-asyncio

### Test Methods
- **Naming**: `test_should_do_something_when_condition`
- **Structure**: Arrange-Act-Assert pattern
```python
async def test_should_create_position_when_signal_valid():
    # Arrange
    signal = create_test_signal()
    
    # Act
    result = await engine.process_signal(signal)
    
    # Assert
    assert result.success is True
```