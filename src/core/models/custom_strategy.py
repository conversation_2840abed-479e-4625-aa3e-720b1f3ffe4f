"""
Custom strategy configuration models for dynamic strategy creation
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Union, Any
from enum import Enum
import json
from datetime import datetime


class TechnicalIndicator(Enum):
    """Available technical indicators for strategy building"""
    EMA_34 = "ema_34"
    EMA_89 = "ema_89"
    EMA_120 = "ema_120"
    RSI = "rsi"
    MACD = "macd"
    BOLLINGER_UPPER = "bb_upper"
    BOLLINGER_LOWER = "bb_lower"
    BOLLINGER_MIDDLE = "bb_middle"
    VOLUME = "volume"
    PRICE = "price"
    ATR = "atr"
    STOCH_RSI = "stoch_rsi"


class ComparisonOperator(Enum):
    """Comparison operators for conditions"""
    GREATER_THAN = ">"
    LESS_THAN = "<"
    GREATER_EQUAL = ">="
    LESS_EQUAL = "<="
    EQUAL = "=="
    CROSS_ABOVE = "cross_above"
    CROSS_BELOW = "cross_below"


class LogicalOperator(Enum):
    """Logical operators for combining conditions"""
    AND = "and"
    OR = "or"


@dataclass
class IndicatorCondition:
    """Single indicator condition"""
    indicator: TechnicalIndicator
    operator: ComparisonOperator
    value: Union[float, TechnicalIndicator]  # Can compare to number or another indicator
    description: str = ""
    
    def to_dict(self) -> Dict:
        return {
            'indicator': self.indicator.value,
            'operator': self.operator.value,
            'value': self.value.value if isinstance(self.value, TechnicalIndicator) else self.value,
            'description': self.description
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'IndicatorCondition':
        indicator = TechnicalIndicator(data['indicator'])
        operator = ComparisonOperator(data['operator'])
        
        # Handle value - could be number or another indicator
        value = data['value']
        if isinstance(value, str):
            try:
                value = TechnicalIndicator(value)
            except ValueError:
                # If not a valid indicator, keep as string/number
                pass
        
        return cls(
            indicator=indicator,
            operator=operator,
            value=value,
            description=data.get('description', '')
        )


@dataclass
class ConditionGroup:
    """Group of conditions with logical operator"""
    conditions: List[IndicatorCondition]
    logical_operator: LogicalOperator = LogicalOperator.AND
    description: str = ""
    
    def to_dict(self) -> Dict:
        return {
            'conditions': [c.to_dict() for c in self.conditions],
            'logical_operator': self.logical_operator.value,
            'description': self.description
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'ConditionGroup':
        conditions = [IndicatorCondition.from_dict(c) for c in data['conditions']]
        logical_operator = LogicalOperator(data.get('logical_operator', 'and'))
        
        return cls(
            conditions=conditions,
            logical_operator=logical_operator,
            description=data.get('description', '')
        )


@dataclass
class EntryLogic:
    """Entry logic configuration"""
    long_conditions: List[ConditionGroup] = field(default_factory=list)
    short_conditions: List[ConditionGroup] = field(default_factory=list)
    entry_method: str = "limit_at_ema34"  # limit_at_ema34, market, limit_custom
    custom_limit_offset: float = 0.0  # Percentage offset for custom limit orders
    
    def to_dict(self) -> Dict:
        return {
            'long_conditions': [cg.to_dict() for cg in self.long_conditions],
            'short_conditions': [cg.to_dict() for cg in self.short_conditions],
            'entry_method': self.entry_method,
            'custom_limit_offset': self.custom_limit_offset
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'EntryLogic':
        long_conditions = [ConditionGroup.from_dict(cg) for cg in data.get('long_conditions', [])]
        short_conditions = [ConditionGroup.from_dict(cg) for cg in data.get('short_conditions', [])]
        
        return cls(
            long_conditions=long_conditions,
            short_conditions=short_conditions,
            entry_method=data.get('entry_method', 'limit_at_ema34'),
            custom_limit_offset=data.get('custom_limit_offset', 0.0)
        )


@dataclass
class DCALogic:
    """DCA (Dollar Cost Averaging) logic configuration"""
    enabled: bool = True
    triggers: List[ConditionGroup] = field(default_factory=list)
    volume_multiplier: float = 1.5  # How much to increase volume per DCA level
    max_dca_levels: int = 3
    min_price_drop: float = 2.0  # Minimum % price drop before DCA
    
    def to_dict(self) -> Dict:
        return {
            'enabled': self.enabled,
            'triggers': [t.to_dict() for t in self.triggers],
            'volume_multiplier': self.volume_multiplier,
            'max_dca_levels': self.max_dca_levels,
            'min_price_drop': self.min_price_drop
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'DCALogic':
        triggers = [ConditionGroup.from_dict(t) for t in data.get('triggers', [])]
        
        return cls(
            enabled=data.get('enabled', True),
            triggers=triggers,
            volume_multiplier=data.get('volume_multiplier', 1.5),
            max_dca_levels=data.get('max_dca_levels', 3),
            min_price_drop=data.get('min_price_drop', 2.0)
        )


@dataclass
class ExitLogic:
    """Exit logic configuration"""
    take_profit_percent: float = 3.0
    stop_loss_percent: float = 2.0
    dynamic_tp_sl: bool = True  # Adjust TP/SL based on volatility
    trailing_stop: bool = False
    trailing_stop_percent: float = 1.0
    exit_conditions: List[ConditionGroup] = field(default_factory=list)  # Custom exit conditions
    
    def to_dict(self) -> Dict:
        return {
            'take_profit_percent': self.take_profit_percent,
            'stop_loss_percent': self.stop_loss_percent,
            'dynamic_tp_sl': self.dynamic_tp_sl,
            'trailing_stop': self.trailing_stop,
            'trailing_stop_percent': self.trailing_stop_percent,
            'exit_conditions': [ec.to_dict() for ec in self.exit_conditions]
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'ExitLogic':
        exit_conditions = [ConditionGroup.from_dict(ec) for ec in data.get('exit_conditions', [])]
        
        return cls(
            take_profit_percent=data.get('take_profit_percent', 3.0),
            stop_loss_percent=data.get('stop_loss_percent', 2.0),
            dynamic_tp_sl=data.get('dynamic_tp_sl', True),
            trailing_stop=data.get('trailing_stop', False),
            trailing_stop_percent=data.get('trailing_stop_percent', 1.0),
            exit_conditions=exit_conditions
        )


@dataclass
class RiskManagement:
    """Risk management configuration"""
    max_position_size: float = 100.0  # USD
    risk_per_trade: float = 30.0  # Percentage of max position
    daily_loss_limit: float = 10.0  # Percentage
    max_open_positions: int = 1
    cooldown_minutes: int = 5
    
    def to_dict(self) -> Dict:
        return {
            'max_position_size': self.max_position_size,
            'risk_per_trade': self.risk_per_trade,
            'daily_loss_limit': self.daily_loss_limit,
            'max_open_positions': self.max_open_positions,
            'cooldown_minutes': self.cooldown_minutes
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'RiskManagement':
        return cls(
            max_position_size=data.get('max_position_size', 100.0),
            risk_per_trade=data.get('risk_per_trade', 30.0),
            daily_loss_limit=data.get('daily_loss_limit', 10.0),
            max_open_positions=data.get('max_open_positions', 1),
            cooldown_minutes=data.get('cooldown_minutes', 5)
        )


@dataclass
class CustomStrategy:
    """Complete custom strategy configuration"""
    name: str
    display_name: str
    description: str
    created_by: int  # User ID
    created_at: datetime
    updated_at: datetime
    
    # Strategy logic
    entry_logic: EntryLogic
    dca_logic: DCALogic
    exit_logic: ExitLogic
    risk_management: RiskManagement
    
    # Metadata
    version: str = "1.0"
    tags: List[str] = field(default_factory=list)
    is_public: bool = False  # Can other users see/use this strategy
    usage_count: int = 0  # How many times this strategy has been used
    
    def to_dict(self) -> Dict:
        return {
            'name': self.name,
            'display_name': self.display_name,
            'description': self.description,
            'created_by': self.created_by,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'entry_logic': self.entry_logic.to_dict(),
            'dca_logic': self.dca_logic.to_dict(),
            'exit_logic': self.exit_logic.to_dict(),
            'risk_management': self.risk_management.to_dict(),
            'version': self.version,
            'tags': self.tags,
            'is_public': self.is_public,
            'usage_count': self.usage_count
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'CustomStrategy':
        return cls(
            name=data['name'],
            display_name=data['display_name'],
            description=data['description'],
            created_by=data['created_by'],
            created_at=datetime.fromisoformat(data['created_at']),
            updated_at=datetime.fromisoformat(data['updated_at']),
            entry_logic=EntryLogic.from_dict(data['entry_logic']),
            dca_logic=DCALogic.from_dict(data['dca_logic']),
            exit_logic=ExitLogic.from_dict(data['exit_logic']),
            risk_management=RiskManagement.from_dict(data['risk_management']),
            version=data.get('version', '1.0'),
            tags=data.get('tags', []),
            is_public=data.get('is_public', False),
            usage_count=data.get('usage_count', 0)
        )
    
    def to_json(self) -> str:
        """Convert to JSON string"""
        return json.dumps(self.to_dict(), indent=2)
    
    @classmethod
    def from_json(cls, json_str: str) -> 'CustomStrategy':
        """Create from JSON string"""
        data = json.loads(json_str)
        return cls.from_dict(data)
    
    def validate(self) -> tuple[bool, str]:
        """Validate strategy configuration"""
        # Check if strategy has at least one entry condition
        if not self.entry_logic.long_conditions and not self.entry_logic.short_conditions:
            return False, "Strategy must have at least one entry condition (LONG or SHORT)"
        
        # Check risk management values
        if self.risk_management.max_position_size <= 0:
            return False, "Max position size must be greater than 0"
        
        if self.risk_management.risk_per_trade <= 0 or self.risk_management.risk_per_trade > 100:
            return False, "Risk per trade must be between 0 and 100 percent"
        
        # Check exit logic
        if self.exit_logic.take_profit_percent <= 0:
            return False, "Take profit percent must be greater than 0"
        
        if self.exit_logic.stop_loss_percent <= 0:
            return False, "Stop loss percent must be greater than 0"
        
        # Check DCA logic if enabled
        if self.dca_logic.enabled:
            if self.dca_logic.max_dca_levels <= 0:
                return False, "Max DCA levels must be greater than 0"
            
            if self.dca_logic.volume_multiplier <= 0:
                return False, "DCA volume multiplier must be greater than 0"
        
        return True, "Strategy configuration is valid"
    
    def get_summary(self) -> str:
        """Get human-readable summary of strategy"""
        summary = f"📊 **{self.display_name}**\n\n"
        summary += f"📝 {self.description}\n\n"
        
        # Entry conditions
        long_count = len(self.entry_logic.long_conditions)
        short_count = len(self.entry_logic.short_conditions)
        
        if long_count > 0:
            summary += f"📈 **LONG Entry:** {long_count} condition group(s)\n"
        if short_count > 0:
            summary += f"📉 **SHORT Entry:** {short_count} condition group(s)\n"
        
        # Exit settings
        summary += f"🎯 **Take Profit:** {self.exit_logic.take_profit_percent}%\n"
        summary += f"🛡️ **Stop Loss:** {self.exit_logic.stop_loss_percent}%\n"
        
        # DCA settings
        if self.dca_logic.enabled:
            summary += f"💰 **DCA:** Enabled ({self.dca_logic.max_dca_levels} levels)\n"
        else:
            summary += f"💰 **DCA:** Disabled\n"
        
        # Risk management
        summary += f"⚖️ **Risk:** {self.risk_management.risk_per_trade}% per trade\n"
        summary += f"💵 **Max Position:** ${self.risk_management.max_position_size}\n"
        
        return summary


class StrategyBuilder:
    """Helper class for building custom strategies step by step"""
    
    def __init__(self, user_id: int):
        self.user_id = user_id
        self.reset()
    
    def reset(self):
        """Reset builder to start fresh"""
        now = datetime.now()
        self.strategy = CustomStrategy(
            name="",
            display_name="",
            description="",
            created_by=self.user_id,
            created_at=now,
            updated_at=now,
            entry_logic=EntryLogic(),
            dca_logic=DCALogic(),
            exit_logic=ExitLogic(),
            risk_management=RiskManagement()
        )
    
    def set_basic_info(self, name: str, display_name: str, description: str):
        """Set basic strategy information"""
        self.strategy.name = name
        self.strategy.display_name = display_name
        self.strategy.description = description
        return self
    
    def add_long_condition(self, condition_group: ConditionGroup):
        """Add LONG entry condition group"""
        self.strategy.entry_logic.long_conditions.append(condition_group)
        return self
    
    def add_short_condition(self, condition_group: ConditionGroup):
        """Add SHORT entry condition group"""
        self.strategy.entry_logic.short_conditions.append(condition_group)
        return self
    
    def set_exit_settings(self, tp_percent: float, sl_percent: float, dynamic: bool = True):
        """Set exit settings"""
        self.strategy.exit_logic.take_profit_percent = tp_percent
        self.strategy.exit_logic.stop_loss_percent = sl_percent
        self.strategy.exit_logic.dynamic_tp_sl = dynamic
        return self
    
    def set_dca_settings(self, enabled: bool, max_levels: int = 3, multiplier: float = 1.5):
        """Set DCA settings"""
        self.strategy.dca_logic.enabled = enabled
        self.strategy.dca_logic.max_dca_levels = max_levels
        self.strategy.dca_logic.volume_multiplier = multiplier
        return self
    
    def set_risk_settings(self, max_position: float, risk_per_trade: float):
        """Set risk management settings"""
        self.strategy.risk_management.max_position_size = max_position
        self.strategy.risk_management.risk_per_trade = risk_per_trade
        return self
    
    def build(self) -> CustomStrategy:
        """Build and return the strategy"""
        self.strategy.updated_at = datetime.now()
        return self.strategy


# Predefined condition templates for easy building
class ConditionTemplates:
    """Predefined condition templates"""
    
    @staticmethod
    def ema_bullish_trend() -> ConditionGroup:
        """EMA bullish trend: EMA_89 > EMA_120"""
        condition = IndicatorCondition(
            indicator=TechnicalIndicator.EMA_89,
            operator=ComparisonOperator.GREATER_THAN,
            value=TechnicalIndicator.EMA_120,
            description="Bullish trend: EMA 89 above EMA 120"
        )
        return ConditionGroup(
            conditions=[condition],
            description="EMA Bullish Trend"
        )
    
    @staticmethod
    def ema_bearish_trend() -> ConditionGroup:
        """EMA bearish trend: EMA_89 < EMA_120"""
        condition = IndicatorCondition(
            indicator=TechnicalIndicator.EMA_89,
            operator=ComparisonOperator.LESS_THAN,
            value=TechnicalIndicator.EMA_120,
            description="Bearish trend: EMA 89 below EMA 120"
        )
        return ConditionGroup(
            conditions=[condition],
            description="EMA Bearish Trend"
        )
    
    @staticmethod
    def price_above_ema34() -> ConditionGroup:
        """Price above EMA 34"""
        condition = IndicatorCondition(
            indicator=TechnicalIndicator.PRICE,
            operator=ComparisonOperator.GREATER_THAN,
            value=TechnicalIndicator.EMA_34,
            description="Price above EMA 34"
        )
        return ConditionGroup(
            conditions=[condition],
            description="Price Above EMA 34"
        )
    
    @staticmethod
    def rsi_oversold(threshold: float = 30.0) -> ConditionGroup:
        """RSI oversold condition"""
        condition = IndicatorCondition(
            indicator=TechnicalIndicator.RSI,
            operator=ComparisonOperator.LESS_THAN,
            value=threshold,
            description=f"RSI below {threshold} (oversold)"
        )
        return ConditionGroup(
            conditions=[condition],
            description="RSI Oversold"
        )
    
    @staticmethod
    def rsi_overbought(threshold: float = 70.0) -> ConditionGroup:
        """RSI overbought condition"""
        condition = IndicatorCondition(
            indicator=TechnicalIndicator.RSI,
            operator=ComparisonOperator.GREATER_THAN,
            value=threshold,
            description=f"RSI above {threshold} (overbought)"
        )
        return ConditionGroup(
            conditions=[condition],
            description="RSI Overbought"
        )
    
    @staticmethod
    def bollinger_lower_touch() -> ConditionGroup:
        """Price touches Bollinger Lower Band"""
        condition = IndicatorCondition(
            indicator=TechnicalIndicator.PRICE,
            operator=ComparisonOperator.LESS_EQUAL,
            value=TechnicalIndicator.BOLLINGER_LOWER,
            description="Price at or below Bollinger Lower Band"
        )
        return ConditionGroup(
            conditions=[condition],
            description="Bollinger Lower Band Touch"
        )
    
    @staticmethod
    def bollinger_upper_touch() -> ConditionGroup:
        """Price touches Bollinger Upper Band"""
        condition = IndicatorCondition(
            indicator=TechnicalIndicator.PRICE,
            operator=ComparisonOperator.GREATER_EQUAL,
            value=TechnicalIndicator.BOLLINGER_UPPER,
            description="Price at or above Bollinger Upper Band"
        )
        return ConditionGroup(
            conditions=[condition],
            description="Bollinger Upper Band Touch"
        )