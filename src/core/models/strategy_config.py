"""
Strategy configuration models for trading bots
"""

from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional
from enum import Enum


class TradingDirection(Enum):
    """Trading direction options"""
    LONG = "LONG"
    SHORT = "SHORT"
    BOTH = "BOTH"
    AUTO = "AUTO"


class StrategyType(Enum):
    """Strategy type options"""
    CONSERVATIVE_LONG = "conservative_long"
    AGGRESSIVE_LONG = "aggressive_long"
    BALANCED_LONG_SHORT = "balanced_long_short"
    SCALPING = "scalping"
    SWING_TRADING = "swing_trading"
    CUSTOM = "custom"


@dataclass
class StrategyTemplate:
    """Strategy template configuration"""
    name: str
    display_name: str
    description: str
    direction: TradingDirection
    
    # Entry conditions
    entry_conditions: Dict[str, Any] = field(default_factory=dict)
    
    # Exit conditions
    take_profit_percent: float = 3.0
    stop_loss_percent: float = 2.0
    tp_sl_strategy: str = "volatility_based"
    
    # DCA settings
    dca_enabled: bool = True
    dca_strategies: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    
    # Risk management
    max_position_size: float = 500.0
    risk_per_trade: float = 40.0
    
    # Technical indicators
    ema_periods: List[int] = field(default_factory=lambda: [34, 89, 120])
    primary_timeframe: str = "15m"
    
    # Advanced settings
    signal_threshold: float = 0.6
    cooldown_minutes: float = 3.0


@dataclass
class StrategyConfig:
    """Main strategy configuration"""
    strategy_type: StrategyType
    direction: TradingDirection
    template: Optional[StrategyTemplate] = None
    
    # Custom overrides
    custom_settings: Dict[str, Any] = field(default_factory=dict)
    
    def get_effective_setting(self, key: str, default: Any = None) -> Any:
        """Get effective setting value (custom override or template default)"""
        if key in self.custom_settings:
            return self.custom_settings[key]
        elif self.template and hasattr(self.template, key):
            return getattr(self.template, key)
        else:
            return default


class StrategyTemplateFactory:
    """Factory for creating strategy templates"""
    
    @staticmethod
    def get_all_templates() -> Dict[str, StrategyTemplate]:
        """Get all available strategy templates"""
        return {
            StrategyType.CONSERVATIVE_LONG.value: StrategyTemplateFactory.conservative_long(),
            StrategyType.AGGRESSIVE_LONG.value: StrategyTemplateFactory.aggressive_long(),
            StrategyType.BALANCED_LONG_SHORT.value: StrategyTemplateFactory.balanced_long_short(),
            StrategyType.SCALPING.value: StrategyTemplateFactory.scalping(),
            StrategyType.SWING_TRADING.value: StrategyTemplateFactory.swing_trading(),
        }
    
    @staticmethod
    def conservative_long() -> StrategyTemplate:
        """Conservative LONG strategy template"""
        return StrategyTemplate(
            name="conservative_long",
            display_name="Conservative LONG",
            description="Safe LONG-only strategy with strict entry conditions",
            direction=TradingDirection.LONG,
            entry_conditions={
                "ema_trend_required": True,
                "price_above_ema34": True,
                "rsi_oversold_threshold": 30,
                "signal_strength_min": 0.7
            },
            take_profit_percent=2.5,
            stop_loss_percent=1.5,
            dca_strategies={
                "BB_LOWER": {"enabled": True, "amount": 50, "timeframe": "15m"},
                "EMA_89": {"enabled": False, "amount": 50, "timeframe": "1h"}
            },
            max_position_size=300.0,
            risk_per_trade=30.0,
            signal_threshold=0.7,
            cooldown_minutes=5.0
        )
    
    @staticmethod
    def aggressive_long() -> StrategyTemplate:
        """Aggressive LONG strategy template"""
        return StrategyTemplate(
            name="aggressive_long",
            display_name="Aggressive LONG",
            description="High-frequency LONG strategy with relaxed entry conditions",
            direction=TradingDirection.LONG,
            entry_conditions={
                "ema_trend_required": True,
                "price_above_ema34": True,
                "rsi_oversold_threshold": 40,
                "signal_strength_min": 0.5
            },
            take_profit_percent=4.0,
            stop_loss_percent=2.5,
            dca_strategies={
                "BB_LOWER": {"enabled": True, "amount": 75, "timeframe": "15m"},
                "EMA_89": {"enabled": True, "amount": 60, "timeframe": "1h"},
                "EMA_34": {"enabled": True, "amount": 50, "timeframe": "15m"}
            },
            max_position_size=500.0,
            risk_per_trade=50.0,
            signal_threshold=0.5,
            cooldown_minutes=2.0
        )
    
    @staticmethod
    def balanced_long_short() -> StrategyTemplate:
        """Balanced LONG/SHORT strategy template"""
        return StrategyTemplate(
            name="balanced_long_short",
            display_name="Balanced LONG/SHORT",
            description="Adaptive strategy that trades both directions based on market conditions",
            direction=TradingDirection.AUTO,
            entry_conditions={
                "ema_trend_adaptive": True,
                "price_ema34_adaptive": True,
                "rsi_adaptive_threshold": True,
                "signal_strength_min": 0.6
            },
            take_profit_percent=3.0,
            stop_loss_percent=2.0,
            dca_strategies={
                "BB_LOWER": {"enabled": True, "amount": 50, "timeframe": "15m"},
                "BB_UPPER": {"enabled": True, "amount": 50, "timeframe": "15m"},
                "EMA_89": {"enabled": True, "amount": 50, "timeframe": "1h"}
            },
            max_position_size=400.0,
            risk_per_trade=40.0,
            signal_threshold=0.6,
            cooldown_minutes=3.0
        )
    
    @staticmethod
    def scalping() -> StrategyTemplate:
        """Scalping strategy template"""
        return StrategyTemplate(
            name="scalping",
            display_name="Scalping",
            description="High-frequency scalping with quick entries and exits",
            direction=TradingDirection.BOTH,
            entry_conditions={
                "quick_signals": True,
                "price_action_focus": True,
                "signal_strength_min": 0.4
            },
            take_profit_percent=1.5,
            stop_loss_percent=1.0,
            dca_strategies={
                "BB_LOWER": {"enabled": True, "amount": 30, "timeframe": "5m"},
                "BB_UPPER": {"enabled": True, "amount": 30, "timeframe": "5m"}
            },
            max_position_size=200.0,
            risk_per_trade=25.0,
            ema_periods=[21, 55, 89],
            primary_timeframe="5m",
            signal_threshold=0.4,
            cooldown_minutes=1.0
        )
    
    @staticmethod
    def swing_trading() -> StrategyTemplate:
        """Swing trading strategy template"""
        return StrategyTemplate(
            name="swing_trading",
            display_name="Swing Trading",
            description="Medium-term swing trading with higher timeframes",
            direction=TradingDirection.AUTO,
            entry_conditions={
                "trend_following": True,
                "higher_timeframe_confirmation": True,
                "signal_strength_min": 0.8
            },
            take_profit_percent=6.0,
            stop_loss_percent=3.0,
            dca_strategies={
                "EMA_89": {"enabled": True, "amount": 100, "timeframe": "4h"},
                "EMA_120": {"enabled": True, "amount": 150, "timeframe": "4h"}
            },
            max_position_size=800.0,
            risk_per_trade=60.0,
            ema_periods=[34, 89, 200],
            primary_timeframe="1h",
            signal_threshold=0.8,
            cooldown_minutes=15.0
        )
    
    @staticmethod
    def get_template(strategy_type: StrategyType) -> Optional[StrategyTemplate]:
        """Get specific strategy template"""
        templates = StrategyTemplateFactory.get_all_templates()
        return templates.get(strategy_type.value)
    
    @staticmethod
    def get_template_by_name(name: str) -> Optional[StrategyTemplate]:
        """Get template by name"""
        templates = StrategyTemplateFactory.get_all_templates()
        return templates.get(name)
