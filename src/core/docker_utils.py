"""
Docker utilities for AutoTrader application
Centralized Docker image management and utilities
"""

import subprocess
import os
import logging
from typing import Optional

logger = logging.getLogger(__name__)


def get_trader_image() -> str:
    """
    Get the correct trader Docker image name.
    Priority: Environment variable -> Registry image -> Local image -> Default
    """
    # Environment variable override (highest priority)
    env_image = os.getenv('TRADER_IMAGE')
    if env_image:
        logger.info(f"Using TRADER_IMAGE from environment: {env_image}")
        return env_image
    
    # Registry image (preferred)
    registry_image = "ghcr.io/hoangtrung99/autotrader-trader:latest"
    
    # Local image (fallback)
    local_image = "autotrader-trader:latest"
    
    # Default fallback (should not happen)
    default_image = "autotrader:latest"
    
    try:
        # Check if registry image exists
        result = subprocess.run(
            ["docker", "image", "inspect", registry_image],
            capture_output=True,
            text=True,
            timeout=10
        )
        if result.returncode == 0:
            logger.info(f"Using registry trader image: {registry_image}")
            return registry_image
            
    except Exception as e:
        logger.warning(f"Could not check registry image: {e}")
    
    try:
        # Check if local image exists
        result = subprocess.run(
            ["docker", "image", "inspect", local_image],
            capture_output=True,
            text=True,
            timeout=10
        )
        if result.returncode == 0:
            logger.info(f"Using local trader image: {local_image}")
            return local_image
            
    except Exception as e:
        logger.warning(f"Could not check local image: {e}")
    
    # Last resort - return registry image (will fail if not available, which is expected)
    logger.warning(f"No trader images found locally, returning registry image: {registry_image}")
    return registry_image


def get_telegram_image() -> str:
    """
    Get the correct telegram Docker image name.
    Priority: Environment variable -> Registry image -> Local image -> Default
    """
    # Environment variable override (highest priority)
    env_image = os.getenv('TELEGRAM_IMAGE')
    if env_image:
        logger.info(f"Using TELEGRAM_IMAGE from environment: {env_image}")
        return env_image
    
    # Registry image (preferred)
    registry_image = "ghcr.io/hoangtrung99/autotrader-telegram:latest"
    
    # Local image (fallback)
    local_image = "autotrader-telegram:latest"
    
    try:
        # Check if registry image exists
        result = subprocess.run(
            ["docker", "image", "inspect", registry_image],
            capture_output=True,
            text=True,
            timeout=10
        )
        if result.returncode == 0:
            logger.info(f"Using registry telegram image: {registry_image}")
            return registry_image
            
    except Exception as e:
        logger.warning(f"Could not check registry image: {e}")
    
    try:
        # Check if local image exists
        result = subprocess.run(
            ["docker", "image", "inspect", local_image],
            capture_output=True,
            text=True,
            timeout=10
        )
        if result.returncode == 0:
            logger.info(f"Using local telegram image: {local_image}")
            return local_image
            
    except Exception as e:
        logger.warning(f"Could not check local image: {e}")
    
    # Last resort - return registry image
    logger.warning(f"No telegram images found locally, returning registry image: {registry_image}")
    return registry_image


def check_docker_available() -> bool:
    """
    Check if Docker is available and running.
    """
    try:
        result = subprocess.run(
            ["docker", "info"],
            capture_output=True,
            text=True,
            timeout=10
        )
        return result.returncode == 0
    except Exception:
        return False


def pull_image(image_name: str, timeout: int = 300) -> bool:
    """
    Pull a Docker image.
    
    Args:
        image_name: Name of the image to pull
        timeout: Timeout in seconds
        
    Returns:
        True if successful, False otherwise
    """
    try:
        logger.info(f"Pulling Docker image: {image_name}")
        result = subprocess.run(
            ["docker", "pull", image_name],
            capture_output=True,
            text=True,
            timeout=timeout
        )
        
        if result.returncode == 0:
            logger.info(f"Successfully pulled image: {image_name}")
            return True
        else:
            logger.error(f"Failed to pull image {image_name}: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error(f"Timeout pulling image: {image_name}")
        return False
    except Exception as e:
        logger.error(f"Error pulling image {image_name}: {e}")
        return False


def image_exists(image_name: str) -> bool:
    """
    Check if a Docker image exists locally.
    
    Args:
        image_name: Name of the image to check
        
    Returns:
        True if image exists, False otherwise
    """
    try:
        result = subprocess.run(
            ["docker", "image", "inspect", image_name],
            capture_output=True,
            text=True,
            timeout=10
        )
        return result.returncode == 0
    except Exception:
        return False


def get_image_digest(image_name: str) -> Optional[str]:
    """
    Get the digest of a Docker image.
    
    Args:
        image_name: Name of the image
        
    Returns:
        Image digest if available, None otherwise
    """
    try:
        result = subprocess.run(
            ["docker", "image", "inspect", image_name, "--format", "{{.RepoDigests}}"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            # Extract SHA256 digest from output
            output = result.stdout.strip()
            if "sha256:" in output:
                # Find the first SHA256 hash
                start = output.find("sha256:")
                if start != -1:
                    end = output.find(" ", start)
                    if end == -1:
                        end = output.find("]", start)
                    if end == -1:
                        end = len(output)
                    return output[start:end]
        
        return None
        
    except Exception as e:
        logger.warning(f"Could not get digest for {image_name}: {e}")
        return None
