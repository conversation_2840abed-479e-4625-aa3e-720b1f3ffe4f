#!/usr/bin/env python3
"""
Unified Command Processor
Centralizes command logic for both bot.sh and Telegram bot
"""

import asyncio
import json
import logging
import sys
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.utils.container_helper import ContainerHelper
from src.infrastructure.services.bot_creation_service import BotCreationService
from src.core.credential_utils import list_profiles


class UnifiedCommandProcessor:
    """Unified command processor for both CLI and Telegram"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.container_helper = ContainerHelper()
        self.bot_creation_service = BotCreationService()
    
    async def process_list_command(self, filters: Dict[str, Any] = None) -> Tuple[int, str, List[Dict]]:
        """
        Process list command - unified for both CLI and Telegram
        
        Returns:
            Tuple[int, str, List[Dict]]: (return_code, message, containers_data)
        """
        try:
            containers = self.container_helper.list_trading_containers()
            
            if not containers:
                return (0, "📭 No trading bot containers found", [])
            
            # Apply filters if provided
            if filters:
                containers = self._apply_filters(containers, filters)
            
            # Format message
            message = f"📊 Found {len(containers)} trading containers:\n"
            for container in containers:
                name = container.get('name', 'Unknown')
                status = "🟢 Running" if container.get('running', False) else "🔴 Stopped"
                message += f"  {status} {name}\n"
            
            return (0, message, containers)
            
        except Exception as e:
            self.logger.error(f"Error in process_list_command: {e}")
            return (1, f"Error listing containers: {str(e)}", [])
    
    async def process_status_command(self, symbol_or_name: str) -> Tuple[int, str, Dict]:
        """
        Process status command - unified for both CLI and Telegram
        
        Args:
            symbol_or_name: Symbol (BTC) or container name (main-btcusdt)
            
        Returns:
            Tuple[int, str, Dict]: (return_code, message, status_data)
        """
        try:
            # Try to find container by symbol or name
            container = await self._find_container(symbol_or_name)
            
            if not container:
                return (1, f"Container not found for: {symbol_or_name}", {})
            
            # Get detailed status
            status_info = self.container_helper.get_container_status(container['name'])
            
            if not status_info:
                return (1, f"Could not get status for: {container['name']}", {})
            
            # Format message
            message = f"📊 Status for {container['name']}:\n"
            message += f"  Status: {'🟢 Running' if status_info.get('running', False) else '🔴 Stopped'}\n"
            message += f"  Image: {status_info.get('image', 'Unknown')}\n"
            message += f"  Created: {status_info.get('created', 'Unknown')}\n"
            
            return (0, message, status_info)
            
        except Exception as e:
            self.logger.error(f"Error in process_status_command: {e}")
            return (1, f"Error getting status: {str(e)}", {})
    
    async def process_logs_command(self, symbol_or_name: str, lines: int = 50) -> Tuple[int, str, str]:
        """
        Process logs command - unified for both CLI and Telegram
        
        Args:
            symbol_or_name: Symbol (BTC) or container name (main-btcusdt)
            lines: Number of log lines to retrieve
            
        Returns:
            Tuple[int, str, str]: (return_code, message, logs_content)
        """
        try:
            # Try to find container by symbol or name
            container = await self._find_container(symbol_or_name)
            
            if not container:
                return (1, f"Container not found for: {symbol_or_name}", "")
            
            # Get logs
            logs = self.container_helper.get_container_logs(container['name'], lines)
            
            if logs is None:
                return (1, f"Could not get logs for: {container['name']}", "")
            
            message = f"📋 Logs for {container['name']} (last {lines} lines):"
            
            return (0, message, logs)
            
        except Exception as e:
            self.logger.error(f"Error in process_logs_command: {e}")
            return (1, f"Error getting logs: {str(e)}", "")
    
    async def process_stop_command(self, symbol_or_name: str) -> Tuple[int, str]:
        """
        Process stop command - unified for both CLI and Telegram
        
        Args:
            symbol_or_name: Symbol (BTC) or container name (main-btcusdt)
            
        Returns:
            Tuple[int, str]: (return_code, message)
        """
        try:
            # Try to find container by symbol or name
            container = await self._find_container(symbol_or_name)
            
            if not container:
                return (1, f"Container not found for: {symbol_or_name}")
            
            # Stop container
            success, message = self.container_helper.stop_container(container['name'])
            
            if success:
                return (0, f"✅ Stopped container: {container['name']}")
            else:
                return (1, f"❌ Failed to stop container: {message}")
                
        except Exception as e:
            self.logger.error(f"Error in process_stop_command: {e}")
            return (1, f"Error stopping container: {str(e)}")
    
    async def process_restart_command(self, symbol_or_name: str) -> Tuple[int, str]:
        """
        Process restart command - unified for both CLI and Telegram
        
        Args:
            symbol_or_name: Symbol (BTC) or container name (main-btcusdt)
            
        Returns:
            Tuple[int, str]: (return_code, message)
        """
        try:
            # Try to find container by symbol or name
            container = await self._find_container(symbol_or_name)
            
            if not container:
                return (1, f"Container not found for: {symbol_or_name}")
            
            # Restart container
            success, message = self.container_helper.restart_container(container['name'])
            
            if success:
                return (0, f"🔄 Restarted container: {container['name']}")
            else:
                return (1, f"❌ Failed to restart container: {message}")
                
        except Exception as e:
            self.logger.error(f"Error in process_restart_command: {e}")
            return (1, f"Error restarting container: {str(e)}")
    
    async def process_create_command(
        self, 
        symbol: str, 
        amount: float, 
        dca_amount: float = 0.0,
        profile: str = None,
        direction: str = "LONG",
        test_mode: bool = False
    ) -> Tuple[int, str]:
        """
        Process create/start command - unified for both CLI and Telegram
        
        Returns:
            Tuple[int, str]: (return_code, message)
        """
        try:
            result = await self.bot_creation_service.create_trading_bot(
                symbol=symbol,
                amount=amount,
                dca_amount=dca_amount,
                profile=profile,
                direction=direction,
                test_mode=test_mode
            )
            
            if result[0] == 0:
                return (0, result[1])  # Success message
            else:
                return (1, result[2])  # Error message
                
        except Exception as e:
            self.logger.error(f"Error in process_create_command: {e}")
            return (1, f"Error creating bot: {str(e)}")

    async def process_remove_command(self, symbol_or_name: str, force: bool = False) -> Tuple[int, str]:
        """
        Process remove command with smart detection

        Returns:
            Tuple[int, str]: (return_code, message)
        """
        try:
            # Check if input is specific container name (profile-symbol format)
            if '-' in symbol_or_name and symbol_or_name.endswith('usdt'):
                # Direct container name provided
                container_name = symbol_or_name
                print(f"🎯 Direct container name detected: {container_name}")

                # Find the specific container
                container = await self._find_container(container_name)
                if not container:
                    return (1, f"❌ Container not found: {container_name}")

                # Remove the container
                return await self._remove_single_container(container, force)

            else:
                # Smart detection by symbol - find all matching containers
                symbol = symbol_or_name.lower().replace('usdt', '').replace('/usdt:usdt', '')
                print(f"🔍 Searching for containers matching symbol: {symbol}")

                # Find containers matching the pattern
                containers = await self.container_helper.list_containers()
                matching_containers = []

                for container in containers:
                    container_name = container['name']
                    # Match patterns: symbolusdt or profile-symbolusdt
                    if (container_name == f"{symbol}usdt" or
                        container_name.endswith(f"-{symbol}usdt")):
                        matching_containers.append(container)

                if not matching_containers:
                    return (1, f"❌ No containers found for symbol: {symbol_or_name}")

                elif len(matching_containers) == 1:
                    # Single container found
                    container = matching_containers[0]
                    print(f"🎯 Found 1 container: {container['name']}")
                    return await self._remove_single_container(container, force)

                else:
                    # Multiple containers found - show options
                    message = f"📊 Found {len(matching_containers)} containers for {symbol_or_name}:\n"
                    for i, container in enumerate(matching_containers, 1):
                        status = "🟢 Running" if container['status'] == 'running' else "🔴 Stopped"
                        message += f"  {i}. {container['name']} ({status})\n"

                    message += "\n💡 Multiple containers found. Use specific container name:\n"
                    for container in matching_containers:
                        force_flag = " --force" if force else ""
                        message += f"   ./bot.sh remove {container['name']}{force_flag}\n"

                    print(message)
                    return (1, "Multiple containers found - use specific container name")

        except Exception as e:
            self.logger.error(f"Error in process_remove_command: {e}")
            return (1, f"Error removing container: {str(e)}")

    async def _remove_single_container(self, container: dict, force: bool = False) -> Tuple[int, str]:
        """Remove a single container"""
        try:
            container_name = container['name']
            container_status = container['status']

            print(f"🗑️ Removing container: {container_name}")
            print(f"📊 Status: {container_status}")

            # Stop container if running (unless force is used)
            if container_status == 'running':
                if not force:
                    print("⚠️ Container is running. Stopping first...")
                    stop_result = await self.container_helper.stop_container(container_name)
                    if not stop_result:
                        return (1, f"❌ Failed to stop container: {container_name}. Use --force to remove running container")
                    print(f"✅ Container stopped: {container_name}")
                else:
                    print("⚡ Force removing running container...")

            # Remove the container
            print(f"🗑️ Removing container: {container_name}")
            remove_result = await self.container_helper.remove_container(container_name)

            if remove_result:
                return (0, f"✅ Container removed successfully: {container_name}")
            else:
                return (1, f"❌ Failed to remove container: {container_name}")

        except Exception as e:
            self.logger.error(f"Error removing single container: {e}")
            return (1, f"Error removing container: {str(e)}")

    async def _find_container(self, symbol_or_name: str) -> Optional[Dict]:
        """Find container by symbol or exact name"""
        try:
            containers = self.container_helper.list_trading_containers()
            
            # First try exact name match
            for container in containers:
                if container['name'] == symbol_or_name:
                    return container
            
            # Then try symbol matching
            symbol = symbol_or_name.upper()
            base_symbol = symbol.lower().replace('usdt', '').replace('/', '').split(':')[0]
            
            matching_containers = []
            for container in containers:
                name = container['name']
                # Check if container matches symbol pattern
                if (name.endswith(f"-{base_symbol}usdt") or name == f"{base_symbol}usdt"):
                    matching_containers.append(container)
            
            if len(matching_containers) == 1:
                return matching_containers[0]
            elif len(matching_containers) > 1:
                # Return first running container, or first container if none running
                running_containers = [c for c in matching_containers if c.get('running', False)]
                return running_containers[0] if running_containers else matching_containers[0]
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error finding container: {e}")
            return None
    
    def _apply_filters(self, containers: List[Dict], filters: Dict[str, Any]) -> List[Dict]:
        """Apply filters to container list"""
        filtered = containers
        
        if filters.get('profile'):
            target_profile = filters['profile'].lower()
            filtered = [c for c in filtered 
                       if self._extract_profile_from_name(c.get('name', '')).lower() == target_profile]
        
        if filters.get('symbol'):
            target_symbol = filters['symbol'].upper()
            filtered = [c for c in filtered 
                       if self._extract_symbol_from_name(c.get('name', '')).upper() == target_symbol]
        
        if filters.get('status') and filters['status'] != 'all':
            if filters['status'] == 'running':
                filtered = [c for c in filtered if c.get('running', False)]
            elif filters['status'] == 'stopped':
                filtered = [c for c in filtered if not c.get('running', False)]
        
        return filtered
    
    def _extract_profile_from_name(self, name: str) -> str:
        """Extract profile from container name"""
        if '-' in name and name.endswith('usdt'):
            parts = name.split('-')
            if len(parts) >= 2:
                return '-'.join(parts[:-1])
        return 'legacy'
    
    def _extract_symbol_from_name(self, name: str) -> str:
        """Extract symbol from container name"""
        if '-' in name and name.endswith('usdt'):
            parts = name.split('-')
            return parts[-1][:-4].upper()
        elif name.endswith('usdt'):
            return name[:-4].upper()
        return name.upper()


# CLI Entry Point
async def main():
    """CLI entry point for unified command processor"""
    if len(sys.argv) < 2:
        print("Usage: python unified_command_processor.py <command> [args...]")
        sys.exit(1)
    
    processor = UnifiedCommandProcessor()
    command = sys.argv[1].lower()
    
    try:
        if command == "list":
            result = await processor.process_list_command()
            print(result[1])
            sys.exit(result[0])
        elif command == "status" and len(sys.argv) >= 3:
            result = await processor.process_status_command(sys.argv[2])
            print(result[1])
            sys.exit(result[0])
        elif command == "logs" and len(sys.argv) >= 3:
            lines = int(sys.argv[3]) if len(sys.argv) >= 4 else 50
            result = await processor.process_logs_command(sys.argv[2], lines)
            print(result[1])
            if result[0] == 0:
                print(result[2])
            sys.exit(result[0])
        elif command == "stop" and len(sys.argv) >= 3:
            result = await processor.process_stop_command(sys.argv[2])
            print(result[1])
            sys.exit(result[0])
        elif command == "restart" and len(sys.argv) >= 3:
            result = await processor.process_restart_command(sys.argv[2])
            print(result[1])
            sys.exit(result[0])
        elif command == "remove" and len(sys.argv) >= 3:
            force = "--force" in sys.argv
            result = await processor.process_remove_command(sys.argv[2], force)
            print(result[1])
            sys.exit(result[0])
        else:
            print(f"Unknown command: {command}")
            sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
