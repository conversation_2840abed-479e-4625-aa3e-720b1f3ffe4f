"""
Custom strategy storage and management service
"""

import os
import json
import logging
from typing import List, Optional, Dict, Any
from datetime import datetime
from pathlib import Path

from src.core.models.custom_strategy import CustomStrategy, StrategyBuilder


class CustomStrategyService:
    """Service for managing custom strategies"""
    
    def __init__(self, storage_path: str = None):
        self.logger = logging.getLogger(__name__)
        
        # Default storage path
        if storage_path is None:
            storage_path = os.path.expanduser("~/.autotrader/custom_strategies")
        
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(parents=True, exist_ok=True)
        
        # User-specific strategy directories
        self.user_strategies_path = self.storage_path / "user_strategies"
        self.user_strategies_path.mkdir(exist_ok=True)
        
        # Public strategies directory
        self.public_strategies_path = self.storage_path / "public_strategies"
        self.public_strategies_path.mkdir(exist_ok=True)
        
        self.logger.info(f"CustomStrategyService initialized with storage: {self.storage_path}")
    
    def _get_user_strategy_path(self, user_id: int) -> Path:
        """Get user-specific strategy directory"""
        user_path = self.user_strategies_path / str(user_id)
        user_path.mkdir(exist_ok=True)
        return user_path
    
    def _get_strategy_file_path(self, user_id: int, strategy_name: str) -> Path:
        """Get full path to strategy file"""
        user_path = self._get_user_strategy_path(user_id)
        return user_path / f"{strategy_name}.json"
    
    def save_strategy(self, strategy: CustomStrategy) -> tuple[bool, str]:
        """Save custom strategy to storage"""
        try:
            # Validate strategy first
            is_valid, validation_msg = strategy.validate()
            if not is_valid:
                return False, f"Strategy validation failed: {validation_msg}"
            
            # Update timestamp
            strategy.updated_at = datetime.now()
            
            # Get file path
            file_path = self._get_strategy_file_path(strategy.created_by, strategy.name)
            
            # Save to file
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(strategy.to_json())
            
            self.logger.info(f"Saved custom strategy '{strategy.name}' for user {strategy.created_by}")
            return True, f"Strategy '{strategy.display_name}' saved successfully"
            
        except Exception as e:
            self.logger.error(f"Error saving strategy: {e}")
            return False, f"Error saving strategy: {str(e)}"
    
    def load_strategy(self, user_id: int, strategy_name: str) -> Optional[CustomStrategy]:
        """Load custom strategy from storage"""
        try:
            file_path = self._get_strategy_file_path(user_id, strategy_name)
            
            if not file_path.exists():
                self.logger.warning(f"Strategy file not found: {file_path}")
                return None
            
            with open(file_path, 'r', encoding='utf-8') as f:
                json_data = f.read()
            
            strategy = CustomStrategy.from_json(json_data)
            self.logger.info(f"Loaded custom strategy '{strategy_name}' for user {user_id}")
            return strategy
            
        except Exception as e:
            self.logger.error(f"Error loading strategy '{strategy_name}' for user {user_id}: {e}")
            return None
    
    def list_user_strategies(self, user_id: int) -> List[Dict[str, Any]]:
        """List all strategies for a user"""
        try:
            user_path = self._get_user_strategy_path(user_id)
            strategies = []
            
            for strategy_file in user_path.glob("*.json"):
                try:
                    with open(strategy_file, 'r', encoding='utf-8') as f:
                        strategy_data = json.load(f)
                    
                    # Extract summary info
                    strategies.append({
                        'name': strategy_data['name'],
                        'display_name': strategy_data['display_name'],
                        'description': strategy_data['description'],
                        'created_at': strategy_data['created_at'],
                        'updated_at': strategy_data['updated_at'],
                        'usage_count': strategy_data.get('usage_count', 0),
                        'version': strategy_data.get('version', '1.0'),
                        'tags': strategy_data.get('tags', [])
                    })
                    
                except Exception as e:
                    self.logger.error(f"Error reading strategy file {strategy_file}: {e}")
                    continue
            
            # Sort by updated_at descending
            strategies.sort(key=lambda x: x['updated_at'], reverse=True)
            
            self.logger.info(f"Listed {len(strategies)} strategies for user {user_id}")
            return strategies
            
        except Exception as e:
            self.logger.error(f"Error listing strategies for user {user_id}: {e}")
            return []
    
    def delete_strategy(self, user_id: int, strategy_name: str) -> tuple[bool, str]:
        """Delete custom strategy"""
        try:
            file_path = self._get_strategy_file_path(user_id, strategy_name)
            
            if not file_path.exists():
                return False, f"Strategy '{strategy_name}' not found"
            
            file_path.unlink()
            self.logger.info(f"Deleted strategy '{strategy_name}' for user {user_id}")
            return True, f"Strategy '{strategy_name}' deleted successfully"
            
        except Exception as e:
            self.logger.error(f"Error deleting strategy '{strategy_name}' for user {user_id}: {e}")
            return False, f"Error deleting strategy: {str(e)}"
    
    def strategy_exists(self, user_id: int, strategy_name: str) -> bool:
        """Check if strategy exists"""
        file_path = self._get_strategy_file_path(user_id, strategy_name)
        return file_path.exists()
    
    def increment_usage_count(self, user_id: int, strategy_name: str) -> bool:
        """Increment usage count for strategy"""
        try:
            strategy = self.load_strategy(user_id, strategy_name)
            if not strategy:
                return False
            
            strategy.usage_count += 1
            strategy.updated_at = datetime.now()
            
            success, _ = self.save_strategy(strategy)
            return success
            
        except Exception as e:
            self.logger.error(f"Error incrementing usage count for '{strategy_name}': {e}")
            return False
    
    def get_strategy_summary(self, user_id: int, strategy_name: str) -> Optional[str]:
        """Get human-readable summary of strategy"""
        strategy = self.load_strategy(user_id, strategy_name)
        if not strategy:
            return None
        
        return strategy.get_summary()
    
    def export_strategy(self, user_id: int, strategy_name: str) -> Optional[str]:
        """Export strategy as JSON string"""
        strategy = self.load_strategy(user_id, strategy_name)
        if not strategy:
            return None
        
        return strategy.to_json()
    
    def import_strategy(self, user_id: int, json_data: str, new_name: str = None) -> tuple[bool, str]:
        """Import strategy from JSON string"""
        try:
            strategy = CustomStrategy.from_json(json_data)
            
            # Update ownership and name if specified
            strategy.created_by = user_id
            if new_name:
                strategy.name = new_name
            
            # Check if strategy already exists
            if self.strategy_exists(user_id, strategy.name):
                return False, f"Strategy '{strategy.name}' already exists"
            
            return self.save_strategy(strategy)
            
        except Exception as e:
            self.logger.error(f"Error importing strategy: {e}")
            return False, f"Error importing strategy: {str(e)}"
    
    def duplicate_strategy(self, user_id: int, strategy_name: str, new_name: str) -> tuple[bool, str]:
        """Duplicate existing strategy with new name"""
        try:
            original = self.load_strategy(user_id, strategy_name)
            if not original:
                return False, f"Original strategy '{strategy_name}' not found"
            
            # Check if new name already exists
            if self.strategy_exists(user_id, new_name):
                return False, f"Strategy '{new_name}' already exists"
            
            # Create duplicate
            duplicate = CustomStrategy.from_dict(original.to_dict())
            duplicate.name = new_name
            duplicate.display_name = f"{original.display_name} (Copy)"
            duplicate.created_at = datetime.now()
            duplicate.updated_at = datetime.now()
            duplicate.usage_count = 0
            
            return self.save_strategy(duplicate)
            
        except Exception as e:
            self.logger.error(f"Error duplicating strategy: {e}")
            return False, f"Error duplicating strategy: {str(e)}"
    
    def search_strategies(self, user_id: int, query: str) -> List[Dict[str, Any]]:
        """Search strategies by name, description, or tags"""
        try:
            all_strategies = self.list_user_strategies(user_id)
            query_lower = query.lower()
            
            matching_strategies = []
            for strategy in all_strategies:
                # Search in name, display_name, description, and tags
                if (query_lower in strategy['name'].lower() or
                    query_lower in strategy['display_name'].lower() or
                    query_lower in strategy['description'].lower() or
                    any(query_lower in tag.lower() for tag in strategy['tags'])):
                    matching_strategies.append(strategy)
            
            return matching_strategies
            
        except Exception as e:
            self.logger.error(f"Error searching strategies: {e}")
            return []
    
    def get_storage_stats(self) -> Dict[str, Any]:
        """Get storage statistics"""
        try:
            stats = {
                'total_users': 0,
                'total_strategies': 0,
                'storage_size_mb': 0,
                'user_stats': {}
            }
            
            # Count user directories
            user_dirs = list(self.user_strategies_path.iterdir())
            stats['total_users'] = len([d for d in user_dirs if d.is_dir()])
            
            # Count strategies and calculate size
            total_size = 0
            for user_dir in user_dirs:
                if not user_dir.is_dir():
                    continue
                
                user_id = user_dir.name
                strategy_files = list(user_dir.glob("*.json"))
                user_strategy_count = len(strategy_files)
                
                user_size = sum(f.stat().st_size for f in strategy_files)
                total_size += user_size
                
                stats['user_stats'][user_id] = {
                    'strategy_count': user_strategy_count,
                    'size_mb': round(user_size / (1024 * 1024), 2)
                }
                
                stats['total_strategies'] += user_strategy_count
            
            stats['storage_size_mb'] = round(total_size / (1024 * 1024), 2)
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Error getting storage stats: {e}")
            return {'error': str(e)}


class StrategyTemplateService:
    """Service for managing strategy templates and quick creation"""
    
    def __init__(self, strategy_service: CustomStrategyService):
        self.strategy_service = strategy_service
        self.logger = logging.getLogger(__name__)
    
    def create_conservative_long_template(self, user_id: int, name: str = "conservative_long_custom") -> CustomStrategy:
        """Create conservative LONG strategy template"""
        from src.core.models.custom_strategy import ConditionTemplates
        
        builder = StrategyBuilder(user_id)
        builder.set_basic_info(
            name=name,
            display_name="Conservative LONG (Custom)",
            description="Conservative LONG-only strategy with EMA trend following and safe risk management"
        )
        
        # Entry conditions: Bullish trend + Price above EMA34
        long_conditions = [
            ConditionTemplates.ema_bullish_trend(),
            ConditionTemplates.price_above_ema34()
        ]
        
        for condition in long_conditions:
            builder.add_long_condition(condition)
        
        # Conservative exit settings
        builder.set_exit_settings(tp_percent=2.5, sl_percent=1.5, dynamic=True)
        
        # Enable DCA with conservative settings
        builder.set_dca_settings(enabled=True, max_levels=2, multiplier=1.3)
        
        # Conservative risk settings
        builder.set_risk_settings(max_position=100.0, risk_per_trade=30.0)
        
        return builder.build()
    
    def create_aggressive_long_template(self, user_id: int, name: str = "aggressive_long_custom") -> CustomStrategy:
        """Create aggressive LONG strategy template"""
        from src.core.models.custom_strategy import ConditionTemplates
        
        builder = StrategyBuilder(user_id)
        builder.set_basic_info(
            name=name,
            display_name="Aggressive LONG (Custom)",
            description="Aggressive LONG-only strategy with higher risk/reward and more frequent entries"
        )
        
        # Entry conditions: Bullish trend + Price above EMA34
        long_conditions = [
            ConditionTemplates.ema_bullish_trend(),
            ConditionTemplates.price_above_ema34()
        ]
        
        for condition in long_conditions:
            builder.add_long_condition(condition)
        
        # Aggressive exit settings
        builder.set_exit_settings(tp_percent=4.0, sl_percent=2.5, dynamic=True)
        
        # Enable DCA with aggressive settings
        builder.set_dca_settings(enabled=True, max_levels=3, multiplier=1.5)
        
        # Aggressive risk settings
        builder.set_risk_settings(max_position=200.0, risk_per_trade=50.0)
        
        return builder.build()
    
    def create_scalping_template(self, user_id: int, name: str = "scalping_custom") -> CustomStrategy:
        """Create scalping strategy template"""
        from src.core.models.custom_strategy import (
            ConditionTemplates, IndicatorCondition, ConditionGroup, 
            TechnicalIndicator, ComparisonOperator
        )
        
        builder = StrategyBuilder(user_id)
        builder.set_basic_info(
            name=name,
            display_name="Scalping (Custom)",
            description="High-frequency scalping strategy with quick entries and exits"
        )
        
        # LONG conditions: Bullish trend + RSI not overbought
        long_conditions = [
            ConditionTemplates.ema_bullish_trend(),
            ConditionTemplates.price_above_ema34()
        ]
        
        # Add RSI condition (not overbought)
        rsi_condition = IndicatorCondition(
            indicator=TechnicalIndicator.RSI,
            operator=ComparisonOperator.LESS_THAN,
            value=65.0,
            description="RSI below 65 (not overbought)"
        )
        long_conditions.append(ConditionGroup(
            conditions=[rsi_condition],
            description="RSI Not Overbought"
        ))
        
        for condition in long_conditions:
            builder.add_long_condition(condition)
        
        # SHORT conditions: Bearish trend + RSI not oversold
        short_conditions = [
            ConditionTemplates.ema_bearish_trend()
        ]
        
        # Add RSI condition (not oversold)
        rsi_short_condition = IndicatorCondition(
            indicator=TechnicalIndicator.RSI,
            operator=ComparisonOperator.GREATER_THAN,
            value=35.0,
            description="RSI above 35 (not oversold)"
        )
        short_conditions.append(ConditionGroup(
            conditions=[rsi_short_condition],
            description="RSI Not Oversold"
        ))
        
        for condition in short_conditions:
            builder.add_short_condition(condition)
        
        # Scalping exit settings (quick profits)
        builder.set_exit_settings(tp_percent=1.5, sl_percent=1.0, dynamic=True)
        
        # Disable DCA for scalping
        builder.set_dca_settings(enabled=False)
        
        # Scalping risk settings
        builder.set_risk_settings(max_position=50.0, risk_per_trade=25.0)
        
        strategy = builder.build()
        
        # Set shorter cooldown for scalping
        strategy.risk_management.cooldown_minutes = 1
        
        return strategy
    
    def get_available_templates(self) -> List[Dict[str, str]]:
        """Get list of available strategy templates"""
        return [
            {
                'name': 'conservative_long_custom',
                'display_name': 'Conservative LONG (Custom)',
                'description': 'Safe LONG-only strategy with trend following',
                'risk_level': 'Low',
                'frequency': 'Medium'
            },
            {
                'name': 'aggressive_long_custom',
                'display_name': 'Aggressive LONG (Custom)',
                'description': 'Higher risk LONG strategy for experienced traders',
                'risk_level': 'High',
                'frequency': 'High'
            },
            {
                'name': 'scalping_custom',
                'display_name': 'Scalping (Custom)',
                'description': 'High-frequency trading with quick profits',
                'risk_level': 'Medium',
                'frequency': 'Very High'
            }
        ]
    
    def create_template_strategy(self, user_id: int, template_name: str, custom_name: str = None) -> tuple[bool, str, Optional[CustomStrategy]]:
        """Create strategy from template"""
        try:
            strategy_name = custom_name or template_name
            
            # Check if strategy already exists
            if self.strategy_service.strategy_exists(user_id, strategy_name):
                return False, f"Strategy '{strategy_name}' already exists", None
            
            # Create strategy based on template
            if template_name == 'conservative_long_custom':
                strategy = self.create_conservative_long_template(user_id, strategy_name)
            elif template_name == 'aggressive_long_custom':
                strategy = self.create_aggressive_long_template(user_id, strategy_name)
            elif template_name == 'scalping_custom':
                strategy = self.create_scalping_template(user_id, strategy_name)
            else:
                return False, f"Unknown template: {template_name}", None
            
            # Save strategy
            success, message = self.strategy_service.save_strategy(strategy)
            if success:
                return True, message, strategy
            else:
                return False, message, None
                
        except Exception as e:
            self.logger.error(f"Error creating template strategy: {e}")
            return False, f"Error creating strategy: {str(e)}", None