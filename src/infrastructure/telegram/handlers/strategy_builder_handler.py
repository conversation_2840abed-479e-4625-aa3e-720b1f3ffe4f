"""
Strategy builder handler for Telegram bot
"""

import logging
import time
from typing import Dict, Any, Optional, List
from telegram import Update
from telegram.ext import ContextTypes
from telegram.constants import ParseMode

from src.infrastructure.telegram.handlers.base_handler import BaseTelegramHandler
from src.infrastructure.telegram.auth.decorators import require_auth
from src.infrastructure.telegram.template_builders.strategy_builder_templates import StrategyBuilderTemplates
from src.infrastructure.telegram.templates import TelegramTemplates
from src.infrastructure.services.custom_strategy_service import CustomStrategyService, StrategyTemplateService
from src.core.models.custom_strategy import (
    StrategyBuilder, CustomStrategy, IndicatorCondition, ConditionGroup,
    TechnicalIndicator, ComparisonOperator, LogicalOperator, ConditionTemplates
)


class StrategyBuilderHandler(BaseTelegramHandler):
    """Handler for custom strategy builder"""
    
    def __init__(self, bot_token: str = None):
        super().__init__(bot_token or "")
        self.logger = logging.getLogger(__name__)
        
        # Initialize services
        self.strategy_service = CustomStrategyService()
        self.template_service = StrategyTemplateService(self.strategy_service)
        
        # User session storage for strategy building
        self.user_sessions: Dict[int, Dict[str, Any]] = {}
        
        self.logger.info("StrategyBuilderHandler initialized")
    
    def _get_user_session(self, user_id: int) -> Dict[str, Any]:
        """Get or create user session"""
        if user_id not in self.user_sessions:
            self.user_sessions[user_id] = {
                'builder': StrategyBuilder(user_id),
                'current_step': 'welcome',
                'temp_data': {}
            }
        return self.user_sessions[user_id]
    
    def _clear_user_session(self, user_id: int):
        """Clear user session"""
        if user_id in self.user_sessions:
            del self.user_sessions[user_id]
    
    @require_auth("USER")
    async def handle_createstrategy(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle /createstrategy command"""
        try:
            template = StrategyBuilderTemplates.strategy_builder_welcome()
            keyboard = self._create_inline_keyboard(template.keyboard) if template.keyboard else None
            
            await update.message.reply_text(
                template.content,
                reply_markup=keyboard,
                parse_mode=ParseMode.HTML
            )
            
        except Exception as e:
            self.logger.error(f"Error in handle_createstrategy: {e}")
            await self._send_error_message(update, str(e))
    
    @require_auth("USER")
    async def handle_mystrategies(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle /mystrategies command"""
        try:
            user_id = update.effective_user.id
            strategies = self.strategy_service.list_user_strategies(user_id)

            template = StrategyBuilderTemplates.my_strategies_list(strategies)
            keyboard = self._create_inline_keyboard(template.keyboard) if template.keyboard else None

            await update.message.reply_text(
                template.content,
                reply_markup=keyboard,
                parse_mode=ParseMode.HTML
            )

        except Exception as e:
            self.logger.error(f"Error in handle_mystrategies: {e}")
            await self._send_error_message(update, str(e))

    @require_auth("USER")
    async def handle_deletestrategy(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle /deletestrategy command"""
        try:
            user_id = update.effective_user.id

            # Get strategy name from command args
            if context.args:
                strategy_name = context.args[0]
                success, message = self.strategy_service.delete_strategy(user_id, strategy_name)

                if success:
                    await update.message.reply_text(
                        f"✅ **Strategy Deleted**\n\n"
                        f"Strategy '{strategy_name}' đã được xóa thành công.",
                        parse_mode=ParseMode.MARKDOWN
                    )
                else:
                    await update.message.reply_text(f"❌ {message}", parse_mode=ParseMode.MARKDOWN)
            else:
                # Show list of strategies to delete
                strategies = self.strategy_service.list_user_strategies(user_id)

                if not strategies:
                    await update.message.reply_text(
                        "❌ **Không có strategies**\n\n"
                        "Bạn chưa có custom strategy nào để xóa.\n\n"
                        "Sử dụng `/createstrategy` để tạo strategy mới.",
                        parse_mode=ParseMode.MARKDOWN
                    )
                    return

                content = "🗑️ **Delete Strategy**\n\nChọn strategy để xóa:\n\n"
                keyboard = []

                for strategy in strategies[:10]:  # Show max 10
                    content += f"• {strategy['display_name']}\n"
                    keyboard.append([{
                        "text": f"🗑️ {strategy['display_name'][:20]}",
                        "callback_data": f"delete_strategy_{strategy['name']}"
                    }])

                keyboard.append([{"text": "❌ Cancel", "callback_data": "close"}])
                keyboard_markup = self._create_inline_keyboard(keyboard)

                await update.message.reply_text(
                    content,
                    reply_markup=keyboard_markup,
                    parse_mode=ParseMode.MARKDOWN
                )

        except Exception as e:
            self.logger.error(f"Error in handle_deletestrategy: {e}")
            await self._send_error_message(update, str(e))

    @require_auth("USER")
    async def handle_duplicatestrategy(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle /duplicatestrategy command"""
        try:
            user_id = update.effective_user.id

            if not context.args:
                await update.message.reply_text(
                    "❌ **Missing Strategy Name**\n\n"
                    "Usage: `/duplicatestrategy <strategy_name>`\n\n"
                    "Example: `/duplicatestrategy my_ema_strategy`\n\n"
                    "Use `/mystrategies` to see available strategies.",
                    parse_mode=ParseMode.MARKDOWN
                )
                return

            strategy_name = context.args[0]

            # Generate new name
            new_name = f"{strategy_name}_copy"
            counter = 1
            while self.strategy_service.strategy_exists(user_id, new_name):
                new_name = f"{strategy_name}_copy_{counter}"
                counter += 1

            success, message = self.strategy_service.duplicate_strategy(user_id, strategy_name, new_name)

            if success:
                await update.message.reply_text(
                    f"✅ **Strategy Duplicated**\n\n"
                    f"Strategy '{strategy_name}' đã được duplicate thành '{new_name}'.\n\n"
                    f"Sử dụng `/mystrategies` để xem strategy mới.",
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                await update.message.reply_text(f"❌ {message}", parse_mode=ParseMode.MARKDOWN)

        except Exception as e:
            self.logger.error(f"Error in handle_duplicatestrategy: {e}")
            await self._send_error_message(update, str(e))

    @require_auth("USER")
    async def handle_exportstrategy(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle /exportstrategy command"""
        try:
            user_id = update.effective_user.id

            if not context.args:
                await update.message.reply_text(
                    "❌ **Missing Strategy Name**\n\n"
                    "Usage: `/exportstrategy <strategy_name>`\n\n"
                    "Example: `/exportstrategy my_ema_strategy`\n\n"
                    "Use `/mystrategies` to see available strategies.",
                    parse_mode=ParseMode.MARKDOWN
                )
                return

            strategy_name = context.args[0]
            json_data = self.strategy_service.export_strategy(user_id, strategy_name)

            if json_data:
                # Send as file
                import io
                file_content = io.BytesIO(json_data.encode('utf-8'))
                file_content.name = f"{strategy_name}.json"

                await update.message.reply_document(
                    document=file_content,
                    filename=f"{strategy_name}.json",
                    caption=f"📤 **Strategy Export**\n\nStrategy '{strategy_name}' exported successfully.\n\n"
                           f"You can import this strategy using `/importstrategy`.",
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                await update.message.reply_text(
                    f"❌ Strategy '{strategy_name}' không tìm thấy.",
                    parse_mode=ParseMode.MARKDOWN
                )

        except Exception as e:
            self.logger.error(f"Error in handle_exportstrategy: {e}")
            await self._send_error_message(update, str(e))

    @require_auth("USER")
    async def handle_importstrategy(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle /importstrategy command"""
        try:
            await update.message.reply_text(
                "📥 **Import Strategy**\n\n"
                "Send a JSON file containing the strategy configuration.\n\n"
                "The file should be exported from another AutoTrader bot using `/exportstrategy`.\n\n"
                "💡 Send the file as a document attachment.",
                parse_mode=ParseMode.MARKDOWN
            )

            # Set user session to expect file upload
            user_id = update.effective_user.id
            session = self._get_user_session(user_id)
            session['expecting_import'] = True

        except Exception as e:
            self.logger.error(f"Error in handle_importstrategy: {e}")
            await self._send_error_message(update, str(e))

    @require_auth("USER")
    async def handle_searchstrategies(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle /searchstrategies command"""
        try:
            user_id = update.effective_user.id

            if not context.args:
                await update.message.reply_text(
                    "🔍 **Search Strategies**\n\n"
                    "Usage: `/searchstrategies <query>`\n\n"
                    "Example: `/searchstrategies ema`\n\n"
                    "Search in strategy names, descriptions, and tags.",
                    parse_mode=ParseMode.MARKDOWN
                )
                return

            query = " ".join(context.args)
            strategies = self.strategy_service.search_strategies(user_id, query)

            if not strategies:
                await update.message.reply_text(
                    f"🔍 **Search Results**\n\n"
                    f"No strategies found for query: '{query}'\n\n"
                    f"Try different keywords or use `/mystrategies` to see all strategies.",
                    parse_mode=ParseMode.MARKDOWN
                )
                return

            content = f"🔍 **Search Results for '{query}'**\n\n"
            content += f"Found {len(strategies)} strategies:\n\n"

            keyboard = []
            for i, strategy in enumerate(strategies[:10], 1):  # Show max 10
                content += f"{i}. **{strategy['display_name']}**\n"
                content += f"   {strategy['description'][:60]}{'...' if len(strategy['description']) > 60 else ''}\n\n"

                keyboard.append([{
                    "text": f"📊 {strategy['display_name'][:20]}",
                    "callback_data": f"strategy_view_{strategy['name']}"
                }])

            if len(strategies) > 10:
                content += f"... and {len(strategies) - 10} more strategies"

            keyboard.append([{"text": "❌ Close", "callback_data": "close"}])
            keyboard_markup = self._create_inline_keyboard(keyboard)

            await update.message.reply_text(
                content,
                reply_markup=keyboard_markup,
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            self.logger.error(f"Error in handle_searchstrategies: {e}")
            await self._send_error_message(update, str(e))
    
    async def handle_builder_callback(self, query, data: str) -> None:
        """Handle strategy builder callbacks"""
        try:
            user_id = query.from_user.id
            session = self._get_user_session(user_id)

            # Debug logging
            self.logger.info(f"🔍 Strategy builder callback: user={user_id}, data='{data}'")
            
            if data == "builder_welcome":
                await self._handle_welcome(query)
            elif data == "builder_template":
                await self._handle_template_selection(query)
            elif data == "builder_scratch":
                await self._handle_scratch_start(query)
            elif data.startswith("template_"):
                template_name = data.replace("template_", "")
                await self._handle_template_create(query, template_name)
            elif data == "my_strategies":
                await self._handle_my_strategies(query)
            elif data.startswith("strategy_view_"):
                strategy_name = data.replace("strategy_view_", "")
                await self._handle_strategy_view(query, strategy_name)
            elif data.startswith("strategy_delete_"):
                strategy_name = data.replace("strategy_delete_", "")
                await self._handle_strategy_delete(query, strategy_name)
            elif data.startswith("strategy_duplicate_"):
                strategy_name = data.replace("strategy_duplicate_", "")
                await self._handle_strategy_duplicate(query, strategy_name)
            elif data.startswith("delete_strategy_"):
                strategy_name = data.replace("delete_strategy_", "")
                await self._handle_delete_strategy_callback(query, strategy_name)
            elif data.startswith("confirm_delete_"):
                strategy_name = data.replace("confirm_delete_", "")
                await self._handle_confirm_delete(query, strategy_name)
            elif data == "builder_steps":
                await self._handle_step_selection(query)
            elif data.startswith("step_"):
                step = data.replace("step_", "")
                await self._handle_step(query, step)
            elif data.startswith("entry_"):
                await self._handle_entry_callback(query, data)
            elif data.startswith("dca_"):
                await self._handle_dca_callback(query, data)
            elif data.startswith("exit_"):
                await self._handle_exit_callback(query, data)
            elif data.startswith("tp_") or data.startswith("sl_"):
                await self._handle_exit_type_callback(query, data)
            elif data == "builder_save_confirm":
                await self._handle_save_confirm(query)
            elif data == "builder_save":
                await self._handle_save_strategy(query)
            elif data == "builder_preview":
                await self._handle_preview_strategy(query)
            elif data == "builder_cancel":
                await self._handle_cancel(query)
            else:
                self.logger.warning(f"❌ Unhandled builder callback: '{data}'")
                await query.edit_message_text(f"❌ Builder callback không hợp lệ: '{data}'")
                
        except Exception as e:
            self.logger.error(f"Error in handle_builder_callback: {e}")
            await query.edit_message_text(f"❌ Lỗi: {str(e)}")
    
    async def _handle_welcome(self, query) -> None:
        """Handle welcome screen"""
        template = StrategyBuilderTemplates.strategy_builder_welcome()
        keyboard = self._create_inline_keyboard(template.keyboard) if template.keyboard else None
        
        await query.edit_message_text(
            template.content,
            reply_markup=keyboard,
            parse_mode=ParseMode.HTML
        )
    
    async def _handle_template_selection(self, query) -> None:
        """Handle template selection"""
        template = StrategyBuilderTemplates.template_selection()
        keyboard = self._create_inline_keyboard(template.keyboard) if template.keyboard else None
        
        await query.edit_message_text(
            template.content,
            reply_markup=keyboard,
            parse_mode=ParseMode.HTML
        )
    
    async def _handle_scratch_start(self, query) -> None:
        """Handle starting from scratch"""
        user_id = query.from_user.id
        session = self._get_user_session(user_id)
        session['current_step'] = 'step_selection'

        # Initialize strategy builder with basic info
        builder = session['builder']
        builder.set_basic_info(
            name=f"custom_strategy_{user_id}_{int(time.time())}",
            display_name="New Custom Strategy",
            description="Custom strategy created with Strategy Builder"
        )

        template = StrategyBuilderTemplates.strategy_step_selection()
        keyboard = self._create_inline_keyboard(template.keyboard) if template.keyboard else None

        await query.edit_message_text(
            template.content,
            reply_markup=keyboard,
            parse_mode=ParseMode.HTML
        )
    
    async def _handle_template_create(self, query, template_name: str) -> None:
        """Handle creating strategy from template"""
        try:
            user_id = query.from_user.id
            
            # Generate unique name
            base_name = template_name
            counter = 1
            strategy_name = base_name
            
            while self.strategy_service.strategy_exists(user_id, strategy_name):
                strategy_name = f"{base_name}_{counter}"
                counter += 1
            
            # Create strategy from template
            success, message, strategy = self.template_service.create_template_strategy(
                user_id, template_name, strategy_name
            )
            
            if success and strategy:
                # Show strategy summary
                template = StrategyBuilderTemplates.strategy_summary(strategy)
                keyboard = self._create_inline_keyboard(template.keyboard) if template.keyboard else None
                
                # Store strategy in session for potential save
                session = self._get_user_session(user_id)
                session['temp_strategy'] = strategy
                
                await query.edit_message_text(
                    template.content,
                    reply_markup=keyboard,
                    parse_mode=ParseMode.HTML
                )
            else:
                await query.edit_message_text(f"❌ Lỗi tạo strategy: {message}")
                
        except Exception as e:
            self.logger.error(f"Error creating template strategy: {e}")
            await query.edit_message_text(f"❌ Lỗi: {str(e)}")
    
    async def _handle_my_strategies(self, query) -> None:
        """Handle my strategies list"""
        try:
            user_id = query.from_user.id
            strategies = self.strategy_service.list_user_strategies(user_id)
            
            template = StrategyBuilderTemplates.my_strategies_list(strategies)
            keyboard = self._create_inline_keyboard(template.keyboard) if template.keyboard else None
            
            await query.edit_message_text(
                template.content,
                reply_markup=keyboard,
                parse_mode=ParseMode.HTML
            )
            
        except Exception as e:
            self.logger.error(f"Error in _handle_my_strategies: {e}")
            await query.edit_message_text(f"❌ Lỗi: {str(e)}")
    
    async def _handle_strategy_view(self, query, strategy_name: str) -> None:
        """Handle strategy detail view"""
        try:
            user_id = query.from_user.id
            strategy = self.strategy_service.load_strategy(user_id, strategy_name)
            
            if not strategy:
                await query.edit_message_text(f"❌ Strategy '{strategy_name}' không tìm thấy")
                return
            
            # Create detailed view template (simplified for now)
            content = f"""📊 **{strategy.display_name}**

📝 **Description:** {strategy.description}

📈 **Entry Conditions:**
• LONG: {len(strategy.entry_logic.long_conditions)} condition group(s)
• SHORT: {len(strategy.entry_logic.short_conditions)} condition group(s)

🎯 **Exit Settings:**
• Take Profit: {strategy.exit_logic.take_profit_percent}%
• Stop Loss: {strategy.exit_logic.stop_loss_percent}%

💰 **DCA:** {'✅ Enabled' if strategy.dca_logic.enabled else '❌ Disabled'}

⚖️ **Risk Management:**
• Max Position: ${strategy.risk_management.max_position_size}
• Risk per Trade: {strategy.risk_management.risk_per_trade}%

📅 **Created:** {strategy.created_at.strftime('%Y-%m-%d %H:%M')}
📊 **Usage Count:** {strategy.usage_count}"""
            
            keyboard = [
                [
                    {"text": "🤖 Use in Bot", "callback_data": f"strategy_use_{strategy_name}"},
                    {"text": "📋 Duplicate", "callback_data": f"strategy_duplicate_{strategy_name}"}
                ],
                [
                    {"text": "🗑️ Delete", "callback_data": f"strategy_delete_{strategy_name}"}
                ],
                [
                    {"text": "🔙 Back to List", "callback_data": "my_strategies"},
                    {"text": "❌ Close", "callback_data": "close"}
                ]
            ]
            
            keyboard_markup = self._create_inline_keyboard(keyboard)
            
            await query.edit_message_text(
                content,
                reply_markup=keyboard_markup,
                parse_mode=ParseMode.MARKDOWN
            )
            
        except Exception as e:
            self.logger.error(f"Error in _handle_strategy_view: {e}")
            await query.edit_message_text(f"❌ Lỗi: {str(e)}")
    
    async def _handle_strategy_delete(self, query, strategy_name: str) -> None:
        """Handle strategy deletion"""
        try:
            user_id = query.from_user.id
            success, message = self.strategy_service.delete_strategy(user_id, strategy_name)
            
            if success:
                await query.edit_message_text(
                    f"✅ **Strategy Deleted**\n\n"
                    f"Strategy '{strategy_name}' đã được xóa thành công.\n\n"
                    f"Sử dụng /mystrategies để xem danh sách strategies còn lại.",
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                await query.edit_message_text(f"❌ Lỗi xóa strategy: {message}")
                
        except Exception as e:
            self.logger.error(f"Error in _handle_strategy_delete: {e}")
            await query.edit_message_text(f"❌ Lỗi: {str(e)}")
    
    async def _handle_strategy_duplicate(self, query, strategy_name: str) -> None:
        """Handle strategy duplication"""
        try:
            user_id = query.from_user.id

            # Generate new name
            new_name = f"{strategy_name}_copy"
            counter = 1
            while self.strategy_service.strategy_exists(user_id, new_name):
                new_name = f"{strategy_name}_copy_{counter}"
                counter += 1

            success, message = self.strategy_service.duplicate_strategy(user_id, strategy_name, new_name)

            if success:
                await query.edit_message_text(
                    f"✅ **Strategy Duplicated**\n\n"
                    f"Strategy '{strategy_name}' đã được duplicate thành '{new_name}'.\n\n"
                    f"Sử dụng /mystrategies để xem và chỉnh sửa strategy mới.",
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                await query.edit_message_text(f"❌ Lỗi duplicate strategy: {message}")

        except Exception as e:
            self.logger.error(f"Error in _handle_strategy_duplicate: {e}")
            await query.edit_message_text(f"❌ Lỗi: {str(e)}")

    async def _handle_delete_strategy_callback(self, query, strategy_name: str) -> None:
        """Handle delete strategy callback"""
        try:
            user_id = query.from_user.id

            # Show confirmation dialog
            keyboard = [
                [
                    {"text": "✅ Yes, Delete", "callback_data": f"confirm_delete_{strategy_name}"},
                    {"text": "❌ Cancel", "callback_data": "my_strategies"}
                ]
            ]
            keyboard_markup = self._create_inline_keyboard(keyboard)

            await query.edit_message_text(
                f"⚠️ **Confirm Delete**\n\n"
                f"Are you sure you want to delete strategy '{strategy_name}'?\n\n"
                f"This action cannot be undone.",
                reply_markup=keyboard_markup,
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            self.logger.error(f"Error in _handle_delete_strategy_callback: {e}")
            await query.edit_message_text(f"❌ Lỗi: {str(e)}")

    async def _handle_confirm_delete(self, query, strategy_name: str) -> None:
        """Handle confirm delete strategy"""
        try:
            user_id = query.from_user.id
            success, message = self.strategy_service.delete_strategy(user_id, strategy_name)

            if success:
                await query.edit_message_text(
                    f"✅ **Strategy Deleted**\n\n"
                    f"Strategy '{strategy_name}' đã được xóa thành công.\n\n"
                    f"Sử dụng /mystrategies để xem strategies còn lại.",
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                await query.edit_message_text(f"❌ Lỗi xóa strategy: {message}")

        except Exception as e:
            self.logger.error(f"Error in _handle_confirm_delete: {e}")
            await query.edit_message_text(f"❌ Lỗi: {str(e)}")

    async def _handle_step_selection(self, query) -> None:
        """Handle step selection"""
        template = StrategyBuilderTemplates.strategy_step_selection()
        keyboard = self._create_inline_keyboard(template.keyboard) if template.keyboard else None

        await query.edit_message_text(
            template.content,
            reply_markup=keyboard,
            parse_mode=ParseMode.HTML
        )

    async def _handle_step(self, query, step: str) -> None:
        """Handle specific step"""
        user_id = query.from_user.id
        session = self._get_user_session(user_id)
        session['current_step'] = step

        if step == "basic_info":
            await self._handle_basic_info_step(query)
        elif step == "entry":
            await self._handle_entry_step(query)
        elif step == "dca":
            await self._handle_dca_step(query)
        elif step == "exit":
            await self._handle_exit_step(query)
        elif step == "risk":
            await self._handle_risk_step(query)
        else:
            await query.edit_message_text("❌ Step không hợp lệ")

    async def _handle_basic_info_step(self, query) -> None:
        """Handle basic info step"""
        template = StrategyBuilderTemplates.strategy_basic_info_input()

        await query.edit_message_text(
            template.content,
            parse_mode=ParseMode.HTML
        )

        # Send follow-up message asking for input
        await query.message.reply_text(
            "💬 **Nhập thông tin strategy:**\n\n"
            "Format: `strategy_name | Display Name | Description`\n\n"
            "Ví dụ: `my_ema_rsi | My EMA RSI Strategy | Custom strategy using EMA trend and RSI filter`",
            parse_mode=ParseMode.MARKDOWN
        )

    async def _handle_entry_step(self, query) -> None:
        """Handle entry conditions step"""
        template = StrategyBuilderTemplates.entry_conditions_setup()
        keyboard = self._create_inline_keyboard(template.keyboard) if template.keyboard else None

        await query.edit_message_text(
            template.content,
            reply_markup=keyboard,
            parse_mode=ParseMode.HTML
        )

    async def _handle_dca_step(self, query) -> None:
        """Handle DCA settings step"""
        template = StrategyBuilderTemplates.dca_settings_setup()
        keyboard = self._create_inline_keyboard(template.keyboard) if template.keyboard else None

        await query.edit_message_text(
            template.content,
            reply_markup=keyboard,
            parse_mode=ParseMode.HTML
        )

    async def _handle_exit_step(self, query) -> None:
        """Handle exit settings step"""
        template = StrategyBuilderTemplates.exit_settings_setup()
        keyboard = self._create_inline_keyboard(template.keyboard) if template.keyboard else None

        await query.edit_message_text(
            template.content,
            reply_markup=keyboard,
            parse_mode=ParseMode.HTML
        )

    async def _handle_risk_step(self, query) -> None:
        """Handle risk management step"""
        await query.edit_message_text(
            "🚧 **Risk Management**\n\n"
            "Risk management settings coming soon!\n\n"
            "This will include:\n"
            "• Position sizing rules\n"
            "• Maximum drawdown limits\n"
            "• Daily/weekly limits\n"
            "• Portfolio allocation",
            parse_mode=ParseMode.MARKDOWN
        )

    async def _handle_entry_callback(self, query, data: str) -> None:
        """Handle entry-related callbacks"""
        user_id = query.from_user.id
        session = self._get_user_session(user_id)

        if data == "entry_long":
            await self._handle_long_entry(query)
        elif data == "entry_short":
            await self._handle_short_entry(query)
        elif data == "entry_preview":
            await self._handle_entry_preview(query)
        elif data == "entry_reset":
            await self._handle_entry_reset(query)
        elif data.startswith("long_"):
            indicator = data.replace("long_", "")
            await self._handle_indicator_setup(query, indicator, "LONG")
        elif data.startswith("short_"):
            indicator = data.replace("short_", "")
            await self._handle_indicator_setup(query, indicator, "SHORT")
        else:
            await query.edit_message_text("❌ Entry callback không hợp lệ")

    async def _handle_long_entry(self, query) -> None:
        """Handle LONG entry conditions"""
        template = StrategyBuilderTemplates.long_entry_conditions()
        keyboard = self._create_inline_keyboard(template.keyboard) if template.keyboard else None

        await query.edit_message_text(
            template.content,
            reply_markup=keyboard,
            parse_mode=ParseMode.HTML
        )

    async def _handle_short_entry(self, query) -> None:
        """Handle SHORT entry conditions"""
        # Similar to LONG but for SHORT
        content = f"""{TelegramTemplates.bold('📉 SHORT Entry Conditions')}

{TelegramTemplates.bold('🎯 When to open SHORT positions:')}

{TelegramTemplates.bold('Current Conditions:')}
• No conditions set yet

{TelegramTemplates.bold('Add Condition:')}
Choose an indicator and set the condition for SHORT entry.

{TelegramTemplates.bold('Example Conditions:')}
• {TelegramTemplates.code('EMA_34 < EMA_89')} - Downtrend confirmation
• {TelegramTemplates.code('RSI > 70')} - Overbought condition
• {TelegramTemplates.code('Price cross_below EMA_34')} - Breakdown signal
• {TelegramTemplates.code('MACD < 0')} - Bearish momentum

{TelegramTemplates.italic('Select indicator to add condition:')}"""

        keyboard = [
            [
                {"text": "📈 EMA", "callback_data": "short_ema"},
                {"text": "📊 RSI", "callback_data": "short_rsi"},
                {"text": "📉 MACD", "callback_data": "short_macd"}
            ],
            [
                {"text": "🎯 Bollinger Bands", "callback_data": "short_bb"},
                {"text": "📊 Volume", "callback_data": "short_volume"},
                {"text": "📈 ATR", "callback_data": "short_atr"}
            ],
            [
                {"text": "🔄 Stoch RSI", "callback_data": "short_stochrsi"},
                {"text": "💰 Price", "callback_data": "short_price"}
            ],
            [
                {"text": "⬅️ Back", "callback_data": "step_entry"},
                {"text": "✅ Done", "callback_data": "entry_short_done"}
            ]
        ]

        keyboard_markup = self._create_inline_keyboard(keyboard)

        await query.edit_message_text(
            content,
            reply_markup=keyboard_markup,
            parse_mode=ParseMode.HTML
        )

    async def _handle_indicator_setup(self, query, indicator: str, direction: str) -> None:
        """Handle indicator condition setup"""
        user_id = query.from_user.id
        session = self._get_user_session(user_id)
        session['temp_data']['current_indicator'] = indicator
        session['temp_data']['current_direction'] = direction
        session['current_step'] = 'indicator_input'

        template = StrategyBuilderTemplates.indicator_condition_setup(indicator, direction)
        keyboard = self._create_inline_keyboard(template.keyboard) if template.keyboard else None

        await query.edit_message_text(
            template.content,
            reply_markup=keyboard,
            parse_mode=ParseMode.HTML
        )

        # Send follow-up message for input
        await query.message.reply_text(
            f"💬 **Enter {indicator} condition for {direction}:**\n\n"
            f"Format: `indicator operator value`\n\n"
            f"Examples:\n"
            f"• `RSI < 30`\n"
            f"• `EMA_34 > EMA_89`\n"
            f"• `Price cross_above EMA_34`\n\n"
            f"Send your condition:",
            parse_mode=ParseMode.MARKDOWN
        )

    async def _handle_exit_callback(self, query, data: str) -> None:
        """Handle exit-related callbacks"""
        if data == "exit_tp":
            await self._handle_take_profit_setup(query)
        elif data == "exit_sl":
            await self._handle_stop_loss_setup(query)
        elif data == "exit_trailing":
            await self._handle_trailing_stop_setup(query)
        elif data == "exit_preview":
            await self._handle_exit_preview(query)
        else:
            await query.edit_message_text("❌ Exit callback không hợp lệ")

    async def _handle_take_profit_setup(self, query) -> None:
        """Handle take profit setup"""
        template = StrategyBuilderTemplates.take_profit_setup()
        keyboard = self._create_inline_keyboard(template.keyboard) if template.keyboard else None

        await query.edit_message_text(
            template.content,
            reply_markup=keyboard,
            parse_mode=ParseMode.HTML
        )

    async def _handle_stop_loss_setup(self, query) -> None:
        """Handle stop loss setup"""
        template = StrategyBuilderTemplates.stop_loss_setup()
        keyboard = self._create_inline_keyboard(template.keyboard) if template.keyboard else None

        await query.edit_message_text(
            template.content,
            reply_markup=keyboard,
            parse_mode=ParseMode.HTML
        )

    async def _handle_exit_type_callback(self, query, data: str) -> None:
        """Handle exit type callbacks (tp_*, sl_*)"""
        user_id = query.from_user.id
        session = self._get_user_session(user_id)

        if data.startswith("tp_"):
            tp_type = data.replace("tp_", "")
            await self._handle_tp_type(query, tp_type)
        elif data.startswith("sl_"):
            sl_type = data.replace("sl_", "")
            await self._handle_sl_type(query, sl_type)

    async def _handle_tp_type(self, query, tp_type: str) -> None:
        """Handle take profit type selection"""
        user_id = query.from_user.id
        session = self._get_user_session(user_id)
        session['temp_data']['tp_type'] = tp_type
        session['current_step'] = 'tp_input'

        if tp_type == "fixed":
            await query.edit_message_text(
                "📈 **Fixed Take Profit**\n\n"
                "Enter the percentage for take profit:\n\n"
                "Format: `3.0` (for 3.0%)\n\n"
                "Recommended ranges:\n"
                "• Conservative: 1.5-2.5%\n"
                "• Moderate: 2.5-4.0%\n"
                "• Aggressive: 4.0-6.0%\n\n"
                "Send your percentage:",
                parse_mode=ParseMode.MARKDOWN
            )
        elif tp_type == "dynamic":
            await query.edit_message_text(
                "📈 **Dynamic Take Profit**\n\n"
                "Enter indicator condition for take profit:\n\n"
                "Examples:\n"
                "• `RSI > 70`\n"
                "• `Price > BB_Upper`\n"
                "• `MACD < 0`\n\n"
                "Send your condition:",
                parse_mode=ParseMode.MARKDOWN
            )
        elif tp_type == "trailing":
            await query.edit_message_text(
                "🔄 **Trailing Take Profit**\n\n"
                "Enter trailing distance percentage:\n\n"
                "Format: `1.5` (for 1.5% trailing distance)\n\n"
                "How it works:\n"
                "• Follows price up with fixed distance\n"
                "• Locks in profits as price moves favorably\n"
                "• Triggers when price drops by trailing distance\n\n"
                "Send your trailing distance:",
                parse_mode=ParseMode.MARKDOWN
            )

    async def _handle_sl_type(self, query, sl_type: str) -> None:
        """Handle stop loss type selection"""
        user_id = query.from_user.id
        session = self._get_user_session(user_id)
        session['temp_data']['sl_type'] = sl_type
        session['current_step'] = 'sl_input'

        if sl_type == "fixed":
            await query.edit_message_text(
                "📉 **Fixed Stop Loss**\n\n"
                "Enter the percentage for stop loss:\n\n"
                "Format: `2.0` (for 2.0%)\n\n"
                "Recommended ranges:\n"
                "• Conservative: 1.0-1.5%\n"
                "• Moderate: 1.5-2.5%\n"
                "• Aggressive: 2.5-4.0%\n\n"
                "Send your percentage:",
                parse_mode=ParseMode.MARKDOWN
            )
        elif sl_type == "dynamic":
            await query.edit_message_text(
                "📉 **Dynamic Stop Loss**\n\n"
                "Enter indicator condition for stop loss:\n\n"
                "Examples:\n"
                "• `Price < EMA_34`\n"
                "• `RSI < 20`\n"
                "• `Price < BB_Lower`\n\n"
                "Send your condition:",
                parse_mode=ParseMode.MARKDOWN
            )
        elif sl_type == "atr":
            await query.edit_message_text(
                "📊 **ATR-based Stop Loss**\n\n"
                "Enter ATR multiplier:\n\n"
                "Format: `2.0` (for 2x ATR)\n\n"
                "How it works:\n"
                "• Uses Average True Range for volatility\n"
                "• Adapts to market conditions\n"
                "• Higher multiplier = wider stop loss\n\n"
                "Recommended: 1.5-3.0\n\n"
                "Send your ATR multiplier:",
                parse_mode=ParseMode.MARKDOWN
            )

    async def handle_message_input(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle text message input for strategy building"""
        try:
            user_id = update.effective_user.id
            text = update.message.text.strip()

            # Check if user has active session
            if user_id not in self.user_sessions:
                return  # No active session

            session = self._get_user_session(user_id)
            current_step = session.get('current_step', '')

            if current_step == 'basic_info':
                await self._process_basic_info_input(update, text)
            elif current_step == 'indicator_input':
                await self._process_indicator_input(update, text)
            elif current_step == 'tp_input':
                await self._process_tp_input(update, text)
            elif current_step == 'sl_input':
                await self._process_sl_input(update, text)
            elif current_step == 'dca_input':
                await self._process_dca_input(update, text)

        except Exception as e:
            self.logger.error(f"Error in handle_message_input: {e}")
            await update.message.reply_text(f"❌ Lỗi xử lý input: {str(e)}")

    async def _process_basic_info_input(self, update: Update, text: str) -> None:
        """Process basic info input"""
        try:
            user_id = update.effective_user.id
            session = self._get_user_session(user_id)
            builder = session['builder']

            # Parse input: strategy_name | Display Name | Description
            parts = [part.strip() for part in text.split('|')]

            if len(parts) != 3:
                await update.message.reply_text(
                    "❌ **Format không đúng**\n\n"
                    "Sử dụng format: `strategy_name | Display Name | Description`\n\n"
                    "Ví dụ: `my_ema_rsi | My EMA RSI Strategy | Custom strategy using EMA trend and RSI filter`",
                    parse_mode=ParseMode.MARKDOWN
                )
                return

            strategy_name, display_name, description = parts

            # Validate strategy name
            if not strategy_name.replace('_', '').isalnum():
                await update.message.reply_text(
                    "❌ **Strategy name không hợp lệ**\n\n"
                    "Strategy name chỉ được chứa chữ cái, số và dấu gạch dưới (_)",
                    parse_mode=ParseMode.MARKDOWN
                )
                return

            # Check if strategy name exists
            if self.strategy_service.strategy_exists(user_id, strategy_name):
                await update.message.reply_text(
                    f"❌ **Strategy name đã tồn tại**\n\n"
                    f"Strategy '{strategy_name}' đã tồn tại. Vui lòng chọn tên khác.",
                    parse_mode=ParseMode.MARKDOWN
                )
                return

            # Set basic info
            builder.set_basic_info(strategy_name, display_name, description)

            # Show success and return to step selection
            await update.message.reply_text(
                f"✅ **Basic Info Updated**\n\n"
                f"**Name:** {strategy_name}\n"
                f"**Display Name:** {display_name}\n"
                f"**Description:** {description}\n\n"
                f"Returning to step selection...",
                parse_mode=ParseMode.MARKDOWN
            )

            # Return to step selection
            session['current_step'] = 'step_selection'
            template = StrategyBuilderTemplates.strategy_step_selection()
            keyboard = self._create_inline_keyboard(template.keyboard) if template.keyboard else None

            await update.message.reply_text(
                template.content,
                reply_markup=keyboard,
                parse_mode=ParseMode.HTML
            )

        except Exception as e:
            self.logger.error(f"Error in _process_basic_info_input: {e}")
            await update.message.reply_text(f"❌ Lỗi: {str(e)}")

    async def _process_indicator_input(self, update: Update, text: str) -> None:
        """Process indicator condition input"""
        try:
            user_id = update.effective_user.id
            session = self._get_user_session(user_id)
            builder = session['builder']
            temp_data = session.get('temp_data', {})

            indicator = temp_data.get('current_indicator', '')
            direction = temp_data.get('current_direction', 'LONG')

            # Parse condition (basic validation)
            if len(text.split()) < 3:
                await update.message.reply_text(
                    "❌ **Format không đúng**\n\n"
                    "Sử dụng format: `indicator operator value`\n\n"
                    "Ví dụ: `RSI < 30` hoặc `EMA_34 > EMA_89`",
                    parse_mode=ParseMode.MARKDOWN
                )
                return

            # Add condition to builder (simplified for now)
            if direction == "LONG":
                builder.add_long_condition(text, "AND")
            else:
                builder.add_short_condition(text, "AND")

            await update.message.reply_text(
                f"✅ **Condition Added**\n\n"
                f"**Direction:** {direction}\n"
                f"**Condition:** {text}\n\n"
                f"Returning to entry conditions...",
                parse_mode=ParseMode.MARKDOWN
            )

            # Return to entry step
            session['current_step'] = 'entry'
            template = StrategyBuilderTemplates.entry_conditions_setup()
            keyboard = self._create_inline_keyboard(template.keyboard) if template.keyboard else None

            await update.message.reply_text(
                template.content,
                reply_markup=keyboard,
                parse_mode=ParseMode.HTML
            )

        except Exception as e:
            self.logger.error(f"Error in _process_indicator_input: {e}")
            await update.message.reply_text(f"❌ Lỗi: {str(e)}")

    async def _process_tp_input(self, update: Update, text: str) -> None:
        """Process take profit input"""
        try:
            user_id = update.effective_user.id
            session = self._get_user_session(user_id)
            builder = session['builder']
            temp_data = session.get('temp_data', {})

            tp_type = temp_data.get('tp_type', 'fixed')

            if tp_type == "fixed" or tp_type == "trailing":
                try:
                    percentage = float(text)
                    if percentage <= 0 or percentage > 50:
                        raise ValueError("Invalid percentage range")

                    builder.set_exit_settings(take_profit_percentage=percentage)

                    await update.message.reply_text(
                        f"✅ **Take Profit Set**\n\n"
                        f"**Type:** {tp_type.title()}\n"
                        f"**Percentage:** {percentage}%\n\n"
                        f"Returning to exit settings...",
                        parse_mode=ParseMode.MARKDOWN
                    )

                except ValueError:
                    await update.message.reply_text(
                        "❌ **Invalid percentage**\n\n"
                        "Enter a valid percentage between 0.1 and 50.0\n\n"
                        "Example: `3.0` for 3.0%",
                        parse_mode=ParseMode.MARKDOWN
                    )
                    return

            elif tp_type == "dynamic":
                # Add dynamic TP condition
                builder.set_exit_settings(take_profit_condition=text)

                await update.message.reply_text(
                    f"✅ **Dynamic Take Profit Set**\n\n"
                    f"**Condition:** {text}\n\n"
                    f"Returning to exit settings...",
                    parse_mode=ParseMode.MARKDOWN
                )

            # Return to exit step
            session['current_step'] = 'exit'
            template = StrategyBuilderTemplates.exit_settings_setup()
            keyboard = self._create_inline_keyboard(template.keyboard) if template.keyboard else None

            await update.message.reply_text(
                template.content,
                reply_markup=keyboard,
                parse_mode=ParseMode.HTML
            )

        except Exception as e:
            self.logger.error(f"Error in _process_tp_input: {e}")
            await update.message.reply_text(f"❌ Lỗi: {str(e)}")

    async def _process_sl_input(self, update: Update, text: str) -> None:
        """Process stop loss input"""
        try:
            user_id = update.effective_user.id
            session = self._get_user_session(user_id)
            builder = session['builder']
            temp_data = session.get('temp_data', {})

            sl_type = temp_data.get('sl_type', 'fixed')

            if sl_type == "fixed" or sl_type == "atr":
                try:
                    value = float(text)
                    if value <= 0 or value > 50:
                        raise ValueError("Invalid value range")

                    builder.set_exit_settings(stop_loss_percentage=value)

                    await update.message.reply_text(
                        f"✅ **Stop Loss Set**\n\n"
                        f"**Type:** {sl_type.title()}\n"
                        f"**Value:** {value}{'%' if sl_type == 'fixed' else 'x ATR'}\n\n"
                        f"Returning to exit settings...",
                        parse_mode=ParseMode.MARKDOWN
                    )

                except ValueError:
                    await update.message.reply_text(
                        "❌ **Invalid value**\n\n"
                        "Enter a valid value between 0.1 and 50.0",
                        parse_mode=ParseMode.MARKDOWN
                    )
                    return

            elif sl_type == "dynamic":
                # Add dynamic SL condition
                builder.set_exit_settings(stop_loss_condition=text)

                await update.message.reply_text(
                    f"✅ **Dynamic Stop Loss Set**\n\n"
                    f"**Condition:** {text}\n\n"
                    f"Returning to exit settings...",
                    parse_mode=ParseMode.MARKDOWN
                )

            # Return to exit step
            session['current_step'] = 'exit'
            template = StrategyBuilderTemplates.exit_settings_setup()
            keyboard = self._create_inline_keyboard(template.keyboard) if template.keyboard else None

            await update.message.reply_text(
                template.content,
                reply_markup=keyboard,
                parse_mode=ParseMode.HTML
            )

        except Exception as e:
            self.logger.error(f"Error in _process_sl_input: {e}")
            await update.message.reply_text(f"❌ Lỗi: {str(e)}")

    async def _handle_save_strategy(self, query) -> None:
        """Handle save strategy"""
        try:
            user_id = query.from_user.id
            session = self._get_user_session(user_id)
            builder = session['builder']

            # Build strategy
            strategy = builder.build()

            if not strategy:
                await query.edit_message_text(
                    "❌ **Cannot Save Strategy**\n\n"
                    "Strategy is incomplete. Please configure:\n"
                    "• Basic info\n"
                    "• At least one entry condition\n"
                    "• Exit settings (TP/SL)",
                    parse_mode=ParseMode.MARKDOWN
                )
                return

            # Save strategy
            success, message = self.strategy_service.save_strategy(user_id, strategy)

            if success:
                # Clear session
                self._clear_user_session(user_id)

                await query.edit_message_text(
                    f"✅ **Strategy Saved Successfully!**\n\n"
                    f"**Name:** {strategy.name}\n"
                    f"**Display Name:** {strategy.display_name}\n"
                    f"**Description:** {strategy.description}\n\n"
                    f"You can now use this strategy in `/createbot` or view it with `/mystrategies`.",
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                await query.edit_message_text(f"❌ Lỗi save strategy: {message}")

        except Exception as e:
            self.logger.error(f"Error in _handle_save_strategy: {e}")
            await query.edit_message_text(f"❌ Lỗi: {str(e)}")

    async def _handle_preview_strategy(self, query) -> None:
        """Handle preview strategy"""
        try:
            user_id = query.from_user.id
            session = self._get_user_session(user_id)
            builder = session['builder']

            # Get current strategy state
            strategy = builder.build()

            if not strategy:
                await query.edit_message_text(
                    "❌ **No Strategy to Preview**\n\n"
                    "Please configure strategy components first.",
                    parse_mode=ParseMode.MARKDOWN
                )
                return

            # Create preview content
            content = f"""{TelegramTemplates.bold('👁️ Strategy Preview')}

{TelegramTemplates.bold('📋 Basic Info:')}
• **Name:** {strategy.name}
• **Display Name:** {strategy.display_name}
• **Description:** {strategy.description}

{TelegramTemplates.bold('🎯 Entry Conditions:')}"""

            # Add LONG conditions
            if strategy.entry_logic and strategy.entry_logic.long_conditions:
                content += f"\n**LONG Conditions:**"
                for condition in strategy.entry_logic.long_conditions.conditions:
                    content += f"\n• {condition.description}"
            else:
                content += f"\n**LONG Conditions:** Not configured"

            # Add SHORT conditions
            if strategy.entry_logic and strategy.entry_logic.short_conditions:
                content += f"\n**SHORT Conditions:**"
                for condition in strategy.entry_logic.short_conditions.conditions:
                    content += f"\n• {condition.description}"
            else:
                content += f"\n**SHORT Conditions:** Not configured"

            # Add exit settings
            content += f"""

{TelegramTemplates.bold('🎯 Exit Settings:')}"""

            if strategy.exit_logic:
                if hasattr(strategy.exit_logic, 'take_profit_percentage'):
                    content += f"\n• **Take Profit:** {strategy.exit_logic.take_profit_percentage}%"
                if hasattr(strategy.exit_logic, 'stop_loss_percentage'):
                    content += f"\n• **Stop Loss:** {strategy.exit_logic.stop_loss_percentage}%"
            else:
                content += f"\n• **Exit Settings:** Not configured"

            # Add DCA settings
            content += f"""

{TelegramTemplates.bold('📊 DCA Settings:')}"""

            if strategy.dca_logic:
                content += f"\n• **DCA configured:** Yes"
            else:
                content += f"\n• **DCA configured:** No"

            keyboard = [
                [
                    {"text": "💾 Save Strategy", "callback_data": "builder_save"},
                    {"text": "✏️ Edit More", "callback_data": "builder_steps"}
                ],
                [
                    {"text": "❌ Cancel", "callback_data": "builder_cancel"}
                ]
            ]

            keyboard_markup = self._create_inline_keyboard(keyboard)

            await query.edit_message_text(
                content,
                reply_markup=keyboard_markup,
                parse_mode=ParseMode.HTML
            )

        except Exception as e:
            self.logger.error(f"Error in _handle_preview_strategy: {e}")
            await query.edit_message_text(f"❌ Lỗi: {str(e)}")

    async def _handle_dca_callback(self, query, data: str) -> None:
        """Handle DCA-related callbacks"""
        await query.edit_message_text(
            "🚧 **DCA Settings**\n\n"
            "DCA (Dollar Cost Averaging) settings coming soon!\n\n"
            "This will include:\n"
            "• DCA trigger conditions\n"
            "• DCA amount configuration\n"
            "• Maximum DCA orders\n"
            "• DCA spacing settings",
            parse_mode=ParseMode.MARKDOWN
        )

    async def _handle_entry_preview(self, query) -> None:
        """Handle entry conditions preview"""
        user_id = query.from_user.id
        session = self._get_user_session(user_id)
        builder = session['builder']

        content = f"""{TelegramTemplates.bold('👁️ Entry Conditions Preview')}

{TelegramTemplates.bold('📈 LONG Conditions:')}"""

        # Get current strategy state
        strategy = builder.build()

        if strategy and strategy.entry_logic and strategy.entry_logic.long_conditions:
            for condition in strategy.entry_logic.long_conditions.conditions:
                content += f"\n• {condition.description}"
        else:
            content += f"\n• No LONG conditions set"

        content += f"""

{TelegramTemplates.bold('📉 SHORT Conditions:')}"""

        if strategy and strategy.entry_logic and strategy.entry_logic.short_conditions:
            for condition in strategy.entry_logic.short_conditions.conditions:
                content += f"\n• {condition.description}"
        else:
            content += f"\n• No SHORT conditions set"

        keyboard = [
            [
                {"text": "⬅️ Back", "callback_data": "step_entry"},
                {"text": "✅ Continue", "callback_data": "step_dca"}
            ]
        ]

        keyboard_markup = self._create_inline_keyboard(keyboard)

        await query.edit_message_text(
            content,
            reply_markup=keyboard_markup,
            parse_mode=ParseMode.HTML
        )

    async def _handle_exit_preview(self, query) -> None:
        """Handle exit settings preview"""
        user_id = query.from_user.id
        session = self._get_user_session(user_id)
        builder = session['builder']

        content = f"""{TelegramTemplates.bold('👁️ Exit Settings Preview')}

{TelegramTemplates.bold('💰 Current Settings:')}"""

        # Get current strategy state
        strategy = builder.build()

        if strategy and strategy.exit_logic:
            if hasattr(strategy.exit_logic, 'take_profit_percentage'):
                content += f"\n• **Take Profit:** {strategy.exit_logic.take_profit_percentage}%"
            if hasattr(strategy.exit_logic, 'stop_loss_percentage'):
                content += f"\n• **Stop Loss:** {strategy.exit_logic.stop_loss_percentage}%"
        else:
            content += f"\n• No exit settings configured"

        keyboard = [
            [
                {"text": "⬅️ Back", "callback_data": "step_exit"},
                {"text": "✅ Continue", "callback_data": "step_risk"}
            ]
        ]

        keyboard_markup = self._create_inline_keyboard(keyboard)

        await query.edit_message_text(
            content,
            reply_markup=keyboard_markup,
            parse_mode=ParseMode.HTML
        )
    
    async def _handle_save_confirm(self, query) -> None:
        """Handle save confirmation"""
        try:
            user_id = query.from_user.id
            session = self._get_user_session(user_id)
            
            if 'temp_strategy' not in session:
                await query.edit_message_text("❌ Không có strategy để lưu")
                return
            
            strategy = session['temp_strategy']
            success, message = self.strategy_service.save_strategy(strategy)
            
            if success:
                template = StrategyBuilderTemplates.strategy_saved_success(strategy.display_name)
                keyboard = self._create_inline_keyboard(template.keyboard) if template.keyboard else None
                
                await query.edit_message_text(
                    template.content,
                    reply_markup=keyboard,
                    parse_mode=ParseMode.HTML
                )
                
                # Clear session
                self._clear_user_session(user_id)
            else:
                await query.edit_message_text(f"❌ Lỗi lưu strategy: {message}")
                
        except Exception as e:
            self.logger.error(f"Error in _handle_save_confirm: {e}")
            await query.edit_message_text(f"❌ Lỗi: {str(e)}")
    
    async def _handle_cancel(self, query) -> None:
        """Handle cancel operation"""
        user_id = query.from_user.id
        self._clear_user_session(user_id)
        
        await query.edit_message_text(
            "❌ **Strategy Builder Cancelled**\n\n"
            "Quá trình tạo strategy đã được hủy.\n\n"
            "Sử dụng /createstrategy để bắt đầu lại.",
            parse_mode=ParseMode.MARKDOWN
        )
    
    async def handle_text_input(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle text input during strategy building"""
        try:
            user_id = update.effective_user.id
            
            if user_id not in self.user_sessions:
                return  # No active session
            
            session = self.user_sessions[user_id]
            current_step = session.get('current_step')
            
            if current_step == 'basic_info':
                await self._handle_basic_info_input(update, session)
            else:
                return  # Not in a text input step
                
        except Exception as e:
            self.logger.error(f"Error in handle_text_input: {e}")
            await update.message.reply_text(f"❌ Lỗi xử lý input: {str(e)}")
    
    async def _handle_basic_info_input(self, update: Update, session: Dict[str, Any]) -> None:
        """Handle basic info input"""
        try:
            text = update.message.text.strip()
            
            if text == '/cancel':
                self._clear_user_session(update.effective_user.id)
                await update.message.reply_text(
                    "❌ **Strategy Builder Cancelled**\n\n"
                    "Quá trình tạo strategy đã được hủy.",
                    parse_mode=ParseMode.MARKDOWN
                )
                return
            
            # Parse input: name | display_name | description
            parts = [part.strip() for part in text.split('|')]
            
            if len(parts) != 3:
                await update.message.reply_text(
                    "❌ **Format không đúng**\n\n"
                    "Vui lòng nhập theo format:\n"
                    "`strategy_name | Display Name | Description`\n\n"
                    "Ví dụ:\n"
                    "`my_ema_rsi | My EMA RSI Strategy | Custom strategy using EMA and RSI`",
                    parse_mode=ParseMode.MARKDOWN
                )
                return
            
            strategy_name, display_name, description = parts
            
            # Validate strategy name
            if not strategy_name.replace('_', '').isalnum():
                await update.message.reply_text(
                    "❌ **Strategy name không hợp lệ**\n\n"
                    "Strategy name chỉ được chứa chữ, số và underscore (_).\n\n"
                    "Ví dụ hợp lệ: `my_strategy`, `ema_rsi_v1`, `conservative_long`",
                    parse_mode=ParseMode.MARKDOWN
                )
                return
            
            # Check if strategy already exists
            user_id = update.effective_user.id
            if self.strategy_service.strategy_exists(user_id, strategy_name):
                await update.message.reply_text(
                    f"❌ **Strategy đã tồn tại**\n\n"
                    f"Strategy '{strategy_name}' đã tồn tại.\n\n"
                    f"Vui lòng chọn tên khác hoặc sử dụng /mystrategies để xem strategies hiện có.",
                    parse_mode=ParseMode.MARKDOWN
                )
                return
            
            # Set basic info
            builder = session['builder']
            builder.set_basic_info(strategy_name, display_name, description)
            
            # Move to next step (for now, just show a simple completion)
            strategy = builder.build()
            
            # Add some default conditions for demo
            from src.core.models.custom_strategy import ConditionTemplates
            builder.add_long_condition(ConditionTemplates.ema_bullish_trend())
            builder.add_long_condition(ConditionTemplates.price_above_ema34())
            
            strategy = builder.build()
            session['temp_strategy'] = strategy
            
            # Show summary
            template = StrategyBuilderTemplates.strategy_summary(strategy)
            keyboard = self._create_inline_keyboard(template.keyboard) if template.keyboard else None
            
            await update.message.reply_text(
                template.content,
                reply_markup=keyboard,
                parse_mode=ParseMode.HTML
            )
            
        except Exception as e:
            self.logger.error(f"Error in _handle_basic_info_input: {e}")
            await update.message.reply_text(f"❌ Lỗi: {str(e)}")
    
    def get_user_custom_strategies(self, user_id: int) -> List[Dict[str, Any]]:
        """Get user's custom strategies for bot creation"""
        try:
            strategies = self.strategy_service.list_user_strategies(user_id)
            return [
                {
                    'name': s['name'],
                    'display_name': s['display_name'],
                    'description': s['description'],
                    'type': 'custom'
                }
                for s in strategies
            ]
        except Exception as e:
            self.logger.error(f"Error getting user custom strategies: {e}")
            return []
    
    def get_strategy_for_bot(self, user_id: int, strategy_name: str) -> Optional[CustomStrategy]:
        """Get strategy for bot creation"""
        try:
            strategy = self.strategy_service.load_strategy(user_id, strategy_name)
            if strategy:
                # Increment usage count
                self.strategy_service.increment_usage_count(user_id, strategy_name)
            return strategy
        except Exception as e:
            self.logger.error(f"Error getting strategy for bot: {e}")
            return None