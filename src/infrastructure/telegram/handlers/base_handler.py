#!/usr/bin/env python3
"""Base handler for Telegram bot operations."""

import asyncio
import logging
import os
from typing import Dict, List, Optional, Any, Tuple

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, ForceReply
from telegram.ext import Application, CommandHandler, MessageHandler, CallbackQueryHandler, filters
from telegram.constants import ParseMode

from ..telegram_base import TelegramBase<PERSON>and<PERSON>, UserSessionManager, ValidationUtils
from ..templates import TelegramTemplates
from ..trading_notifications import TradingNotificationManager
from ..safe_message_sender import SafeMessageSender

# Import centralized utilities
from ....core.constants import CREDENTIALS_DIR, ensure_directories
from ....core.credential_utils import list_profiles, load_credentials, store_credentials

# Import optimized integration layers
from ..core.cli_integration import telegram_cli
from ..core.docker_manager import docker_manager


class BaseTelegramHandler(TelegramBaseHandler):
    """Base class for all Telegram handlers with common functionality."""

    def __init__(self, bot_token: str):
        super().__init__('BaseTelegramHandler')
        self.bot_token = bot_token
        self.application = None
        self.session_manager = UserSessionManager()



        # Initialize optimized integration layers
        self.cli_integration = telegram_cli
        self.docker_manager = docker_manager

        # Initialize notification manager without chat_id (will be determined dynamically)
        self.notification_manager = TradingNotificationManager(bot_token)
        self.safe_sender = SafeMessageSender(self.logger)

    async def handle_command(self, update, context) -> None:
        """Handle command - required by abstract base class"""
        pass

    def _create_inline_keyboard(self, keyboard_data: List[List[Dict[str, str]]]) -> InlineKeyboardMarkup:
        """Create inline keyboard from template data"""
        keyboard = []
        for row in keyboard_data:
            button_row = []
            for button_data in row:
                button = InlineKeyboardButton(
                    text=button_data["text"],
                    callback_data=button_data["callback_data"]
                )
                button_row.append(button)
            keyboard.append(button_row)
        return InlineKeyboardMarkup(keyboard)

    async def execute_cli_command(self, command: str, args: List[str] = None) -> Tuple[int, str, str]:
        """
        Execute CLI command using direct Python integration
        Eliminates subprocess overhead and bot.sh dependency
        """
        if args is None:
            args = []

        try:
            # Route to appropriate CLI integration
            if command in ["list", "status", "stop", "restart", "remove", "logs"]:
                return await self.cli_integration.execute_autotrader_command(command, args)
            elif command in ["list-credentials", "store-credentials", "load-credentials", "show-credentials", "delete-credentials"]:
                # Map bot.sh credential commands to CLI commands
                cmd_map = {
                    "list-credentials": "list",
                    "store-credentials": "store",
                    "load-credentials": "load",
                    "show-credentials": "show",
                    "delete-credentials": "delete"
                }
                actual_cmd = cmd_map.get(command, command)
                return await self.cli_integration.execute_credentials_command(actual_cmd, args)
            else:
                return 1, "", f"Unknown command: {command}"

        except Exception as e:
            self.logger.error(f"Error executing CLI command {command}: {e}")
            return 1, "", str(e)



    async def get_trading_containers(self) -> List[Dict[str, Any]]:
        """
        Get trading containers using direct Docker API
        Eliminates subprocess overhead
        """
        try:
            return await self.cli_integration.get_trading_containers()
        except Exception as e:
            self.logger.error(f"Error getting trading containers: {e}")
            return []

    async def get_container_status_direct(self, symbol: str) -> Optional[Dict[str, Any]]:
        """
        Get container status using direct Docker API
        """
        try:
            return await self.cli_integration.get_container_status_direct(symbol)
        except Exception as e:
            self.logger.error(f"Error getting container status for {symbol}: {e}")
            return None

    async def docker_operation(self, operation: str, container_name: str, **kwargs) -> Tuple[bool, str]:
        """
        Perform Docker operations using direct Docker API

        Args:
            operation: Docker operation (stop, restart, remove, logs)
            container_name: Container name or ID
            **kwargs: Additional operation parameters

        Returns:
            Tuple of (success, message)
        """
        try:
            if operation == "stop":
                return await self.docker_manager.stop_container(container_name, kwargs.get('timeout', 10))
            elif operation == "restart":
                return await self.docker_manager.restart_container(container_name, kwargs.get('timeout', 10))
            elif operation == "remove":
                return await self.docker_manager.remove_container(container_name, kwargs.get('force', False))
            elif operation == "logs":
                success, logs = await self.docker_manager.get_container_logs(container_name, kwargs.get('lines', 50))
                return success, logs
            else:
                return False, f"Unknown Docker operation: {operation}"

        except Exception as e:
            self.logger.error(f"Error performing Docker operation {operation} on {container_name}: {e}")
            return False, str(e)

    async def _execute_docker_command(self, docker_args: List[str]) -> Tuple[int, str, str]:
        """Execute docker command using docker socket"""
        try:
            import docker
            # Use explicit socket path for container environment
            client = docker.DockerClient(base_url='unix://var/run/docker.sock')

            # Handle different docker commands
            if len(docker_args) == 0:
                return 1, "", "No docker command specified"

            cmd = docker_args[0]

            if cmd == "ps":
                # List containers
                containers = client.containers.list(all=True)
                if "-a" in docker_args and "--format" in docker_args:
                    # Format as names only
                    names = [c.name for c in containers]
                    return 0, "\n".join(names), ""
                else:
                    # Default ps format
                    output = []
                    for c in containers:
                        status = c.status
                        output.append(f"{c.id[:12]} {c.name} {status}")
                    return 0, "\n".join(output), ""

            elif cmd == "stop":
                # Stop container
                if len(docker_args) < 2:
                    return 1, "", "Container name required"
                container_name = docker_args[1]
                try:
                    container = client.containers.get(container_name)
                    container.stop()
                    return 0, f"Stopped {container_name}", ""
                except docker.errors.NotFound:
                    return 1, "", f"Container {container_name} not found"

            elif cmd == "rm":
                # Remove container
                if len(docker_args) < 2:
                    return 1, "", "Container name required"
                container_name = docker_args[1]
                try:
                    container = client.containers.get(container_name)
                    container.remove()
                    return 0, f"Removed {container_name}", ""
                except docker.errors.NotFound:
                    return 1, "", f"Container {container_name} not found"

            elif cmd == "run":
                # This is complex, let's delegate back to bot.sh
                # But first ensure we have docker binary available
                return await self._execute_docker_via_socket(docker_args)

            else:
                return 1, "", f"Unsupported docker command: {cmd}"

        except ImportError:
            # Docker library not available, try alternative approach
            return await self._execute_docker_via_socket(docker_args)
        except Exception as e:
            self.logger.error(f"Error executing docker command: {e}")
            return 1, "", str(e)

    async def _execute_docker_via_socket(self, docker_args: List[str]) -> Tuple[int, str, str]:
        """Execute docker command via socket using curl/HTTP API"""
        try:
            # For now, return error - we'll implement this if needed
            return 1, "", "Docker command execution in container not fully implemented yet"
        except Exception as e:
            return 1, "", str(e)

    def _parse_credentials_list(self, output: str) -> List[Dict[str, str]]:
        """Parse credentials list from bot.sh output"""
        credentials = []
        lines = output.split('\n')
        
        current_profile = None
        current_display_name = None
        
        for line in lines:
            line = line.strip()
            
            # New format: 🔑 profile_name
            if line.startswith('🔑 '):
                current_profile = line[2:].strip()
                current_display_name = current_profile  # Default to profile name
                
            # Extract display name: Display Name: actual_name
            elif line.startswith('Display Name:') and current_profile:
                current_display_name = line.split(':', 1)[1].strip()
                
            # When we hit Format: or Total:, save the current credential
            elif (line.startswith('Format:') or line.startswith('📊 Total:')) and current_profile:
                credentials.append({
                    'profile': current_profile,
                    'display_name': current_display_name or current_profile
                })
                current_profile = None
                current_display_name = None
        
        # Handle case where last credential doesn't have Format line
        if current_profile:
            credentials.append({
                'profile': current_profile,
                'display_name': current_display_name or current_profile
            })

        return credentials
    
    async def _check_prerequisites(self) -> bool:
        """Check if prerequisites (credentials) are available"""
        try:
            # Use centralized credential utilities
            profiles = list_profiles()
            return len(profiles) > 0
            
        except Exception as e:
            self.logger.error(f"Prerequisites check failed with exception: {e}")
            return False
    
    def _create_inline_keyboard(self, buttons: List[List[Dict[str, str]]]) -> InlineKeyboardMarkup:
        """Create inline keyboard from button configuration"""
        keyboard = []
        for row in buttons:
            keyboard_row = []
            for button in row:
                keyboard_row.append(InlineKeyboardButton(
                    text=button['text'],
                    callback_data=button['callback_data']
                ))
            keyboard.append(keyboard_row)
        return InlineKeyboardMarkup(keyboard)

    async def _send_safe_message(self, send_func, text: str, parse_mode=None, **kwargs):
        """Send message with automatic fallback for parsing errors"""
        return await self.safe_sender.send_safe_message(send_func, text, parse_mode, **kwargs)

    async def _send_error_message(self, update: Update, error_msg: str):
        """Send enhanced error message with smart suggestions"""
        # Clean up the message
        clean_message = error_msg.replace("❌ ", "").strip()

        # Smart contextual help based on error patterns
        help_text = ""
        suggestions = []

        # Pattern-based suggestions
        if "không tìm thấy" in clean_message.lower() or "not found" in clean_message.lower():
            suggestions.extend([
                "Sử dụng `/list` để xem tất cả bots",
                "Kiểm tra tên container chính xác",
                "Thử tìm kiếm theo symbol (VD: btc thay vì main-btc)",
                "Dùng `/profiles` để xem theo profile"
            ])
        elif "sử dụng:" in clean_message.lower() or "usage:" in clean_message.lower():
            suggestions.extend([
                "Sử dụng `/help` để xem hướng dẫn đầy đủ",
                "Xem ví dụ trong từng command",
                "Dùng `/profiles` để quản lý profiles"
            ])
        elif "không hợp lệ" in clean_message.lower() or "invalid" in clean_message.lower():
            suggestions.extend([
                "Kiểm tra định dạng số (VD: 50, không phải 50$)",
                "Đảm bảo thứ tự tham số đúng",
                "Profile name chỉ chứa chữ, số, dấu gạch ngang",
                "Symbol phải là chữ hoa (VD: BTC, ETH)"
            ])
        elif "credential" in clean_message.lower() or "api" in clean_message.lower():
            suggestions.extend([
                "Sử dụng `/addcreds` để thêm API credentials",
                "Kiểm tra API key và secret từ Binance",
                "Đảm bảo API có quyền Spot Trading",
                "Không chia sẻ API key với ai khác"
            ])
        elif "symbol" in clean_message.lower():
            suggestions.extend([
                "Symbol phải là chữ hoa (BTC, ETH, SOL)",
                "Kiểm tra symbol có tồn tại trên Binance",
                "Dùng `/list` để xem symbols đang trade",
                "Thử format: BTC thay vì BTCUSDT"
            ])
        elif "profile" in clean_message.lower():
            suggestions.extend([
                "Profile name: chỉ chữ, số, dấu gạch ngang",
                "Tối đa 50 ký tự",
                "VD: main, test, backup, scalp-v1",
                "Dùng `/profiles` để xem tất cả profiles"
            ])
        elif "amount" in clean_message.lower() or "số tiền" in clean_message.lower():
            suggestions.extend([
                "Amount phải là số dương (VD: 50, 100.5)",
                "Đơn vị: USDT",
                "Tối thiểu: $10 USDT",
                "DCA amount có thể để trống (mặc định: 0)"
            ])

        # Add general suggestions if no specific ones
        if not suggestions:
            suggestions.extend([
                "Sử dụng `/help` để xem hướng dẫn",
                "Dùng `/list` để xem trạng thái bots",
                "Kiểm tra lại cú pháp command"
            ])

        # Format suggestions
        if suggestions:
            help_text = "\n\n💡 **Gợi ý:**\n"
            for suggestion in suggestions[:4]:  # Limit to 4 suggestions
                help_text += f"• {suggestion}\n"

        # Add quick actions based on context
        quick_actions = []
        if "credential" in clean_message.lower():
            quick_actions.append("🔑 `/addcreds` - Thêm API")
        if "không tìm thấy" in clean_message.lower():
            quick_actions.append("📋 `/list` - Xem bots")
        if "profile" in clean_message.lower():
            quick_actions.append("🔑 `/profiles` - Quản lý profiles")

        if quick_actions:
            help_text += f"\n🚀 **Quick Actions:** {' • '.join(quick_actions)}"

        formatted_message = f"❌ **Lỗi**\n\n{clean_message}{help_text}"

        await self._send_safe_message(
            update.message.reply_text,
            formatted_message,
            parse_mode=ParseMode.MARKDOWN
        )

    async def _send_success_message(self, update: Update, success_msg: str):
        """Send enhanced success message with celebration"""
        # Add celebration emoji for different success types
        celebration = "🎉"
        if "tạo" in success_msg.lower() or "create" in success_msg.lower():
            celebration = "🚀"
        elif "dừng" in success_msg.lower() or "stop" in success_msg.lower():
            celebration = "⏹️"
        elif "khởi động" in success_msg.lower() or "start" in success_msg.lower():
            celebration = "▶️"
        elif "restart" in success_msg.lower():
            celebration = "🔄"

        formatted_message = f"✅ **Thành công!** {celebration}\n\n{success_msg}"

        await self._send_safe_message(
            update.message.reply_text,
            formatted_message,
            parse_mode=ParseMode.MARKDOWN
        )

    async def _send_info_message(self, update: Update, info_msg: str, emoji: str = "ℹ️"):
        """Send enhanced info message with custom emoji"""
        formatted_message = f"{emoji} **Thông tin**\n\n{info_msg}"

        await self._send_safe_message(
            update.message.reply_text,
            formatted_message,
            parse_mode=ParseMode.MARKDOWN
        )

    async def _send_warning_message(self, update: Update, warning_msg: str):
        """Send warning message with enhanced formatting"""
        formatted_message = f"⚠️ **Cảnh báo**\n\n{warning_msg}\n\n💡 Hãy cẩn thận trước khi tiếp tục!"

        await self._send_safe_message(
            update.message.reply_text,
            formatted_message,
            parse_mode=ParseMode.MARKDOWN
        )
    
    def _validate_symbol(self, symbol: str) -> bool:
        """Validate trading symbol with security checks"""
        return ValidationUtils.validate_symbol(symbol)

    def _validate_amount(self, amount: str) -> bool:
        """Validate trading amount with security checks"""
        return ValidationUtils.validate_amount(amount)

    def _validate_api_key(self, api_key: str) -> bool:
        """Validate API key with security checks"""
        return ValidationUtils.validate_api_key(api_key)

    def _validate_profile_name(self, profile: str) -> bool:
        """Validate profile name with security checks"""
        return ValidationUtils.validate_profile_name(profile)

    def _sanitize_input(self, input_str: str) -> str:
        """Sanitize user input to prevent injection attacks"""
        return ValidationUtils.sanitize_input(input_str)

    def _normalize_symbol(self, symbol: str) -> str:
        """Normalize symbol to full format with security validation"""
        # Security: Validate and sanitize input first
        if not self._validate_symbol(symbol):
            raise ValueError(f"Invalid symbol: {symbol}")

        sanitized_symbol = self._sanitize_input(symbol)

        if "/" not in sanitized_symbol:
            # Simple symbol like "btc" -> "BTC/USDT:USDT"
            return f"{sanitized_symbol.upper()}/USDT:USDT"
        return sanitized_symbol

    def _get_container_name(self, symbol: str, profile: str = None) -> str:
        """Generate container name from symbol and profile with security validation"""
        # Security: Validate symbol first
        if not self._validate_symbol(symbol):
            raise ValueError(f"Invalid symbol for container name: {symbol}")

        # Extract base symbol (e.g., ETH/USDT:USDT -> eth, BTC/USDT:USDT -> btc)
        if "/" in symbol:
            # Full format like "ETH/USDT:USDT" -> "eth"
            base_symbol = symbol.split("/")[0]
        elif symbol.upper().endswith("USDT") and len(symbol) > 4:
            # Format like "ETHUSDT" -> "eth"
            base_symbol = symbol[:-4]
        else:
            # Simple format like "ETH" or "eth" -> "eth"
            base_symbol = symbol

        # Convert to lowercase and sanitize
        base_symbol = self._sanitize_input(base_symbol.lower())

        # Remove any existing 'usdt' suffix to avoid duplication
        if base_symbol.lower().endswith('usdt'):
            base_symbol = base_symbol[:-4]

        # Generate container name with profile prefix
        if profile:
            # Security: Validate and sanitize profile name
            if not self._validate_profile_name(profile):
                raise ValueError(f"Invalid profile name: {profile}")
            sanitized_profile = self._sanitize_input(profile.lower())
            container_name = f"{sanitized_profile}-{base_symbol}usdt"
        else:
            # Backward compatibility - return old format
            container_name = f"{base_symbol}usdt"

        # Security: Final validation of container name
        if not ValidationUtils.validate_container_name(container_name):
            raise ValueError(f"Invalid container name generated: {container_name}")

        return container_name

    def _validate_profile_name(self, profile: str) -> bool:
        """Validate profile name for security and format"""
        if not profile:
            return False

        # Check length
        if len(profile) > 50:
            return False

        # Check for valid characters (alphanumeric, dash, underscore)
        import re
        if not re.match(r'^[a-zA-Z0-9_-]+$', profile):
            return False

        # Check for reserved names
        reserved_names = {'docker', 'container', 'bot', 'telegram', 'system', 'admin', 'root'}
        if profile.lower() in reserved_names:
            return False

        return True

    async def _find_containers_by_symbol(self, symbol: str) -> List[Dict]:
        """Find all containers for a symbol using improved detection"""
        try:
            # Use the improved container detection from get_trading_containers
            all_containers = await self.get_trading_containers()

            if not all_containers:
                return []

            containers = []
            base_symbol = symbol.lower().replace('usdt', '').replace('/', '').split(':')[0]

            for container in all_containers:
                name = container.get('name', '')

                # Check if container matches symbol
                # New format: {profile}-{symbol}usdt
                # Old format: {symbol}usdt
                if (name.endswith(f"-{base_symbol}usdt") or
                    name == f"{base_symbol}usdt"):

                    # Extract profile from container name
                    profile = None
                    if name.endswith(f"-{base_symbol}usdt"):
                        profile = name.replace(f"-{base_symbol}usdt", "")

                    containers.append({
                        'name': name,
                        'status': container.get('status', ''),
                        'image': container.get('image', ''),
                        'symbol': symbol.upper(),
                        'profile': profile,
                        'running': container.get('running', False)
                    })

            return containers

        except Exception as e:
            self.logger.error(f"Error finding containers by symbol: {e}")
            return []

    async def _find_container_by_name(self, container_name: str) -> Dict:
        """Find specific container by exact name"""
        try:
            cmd = ["docker", "ps", "-a", "--format", "{{.Names}}\t{{.Status}}\t{{.Image}}", "--filter", f"name=^{container_name}$"]
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, _ = await process.communicate()

            if process.returncode != 0:
                return {}

            lines = stdout.decode('utf-8').strip().split('\n')
            if lines and lines[0]:
                parts = lines[0].split('\t')
                if len(parts) >= 2:
                    return {
                        'name': parts[0],
                        'status': parts[1],
                        'image': parts[2] if len(parts) > 2 else ""
                    }

            return {}

        except Exception as e:
            self.logger.error(f"Error finding container by name: {e}")
            return {}
    
    async def _get_active_containers(self) -> List[Dict[str, Any]]:
        """
        Get list of active trading containers using direct integration
        Replaces subprocess calls with direct Python API calls
        """
        try:
            # Use direct container retrieval
            containers = await self.get_trading_containers()

            # Filter for running containers and format for compatibility
            active_containers = []
            for container in containers:
                if container.get('status', '').lower() == 'running':
                    active_containers.append({
                        'name': container.get('name', ''),
                        'status': 'Running',
                        'symbol': container.get('name', '').upper(),
                        'id': container.get('id', ''),
                        'image': container.get('image', ''),
                        'created': container.get('created', '')
                    })

            return active_containers

        except Exception as e:
            self.logger.error(f"Error getting active containers: {e}")
            return []
    
    async def _format_container_status(self, containers: List[Dict[str, Any]]) -> str:
        """Format container status for display"""
        if not containers:
            return "📭 **Không có bot nào đang chạy**"
        
        message = "🤖 **Bot đang hoạt động:**\n\n"
        for container in containers:
            message += f"• **{container['symbol']}** - {container['status']}\n"
        
        return message
    
    def _get_session_data(self, user_id: int, key: str, default=None):
        """Get data from user session"""
        return self.session_manager.get_session_data(user_id, key, default)
    
    def _set_session_data(self, user_id: int, key: str, value):
        """Set data in user session"""
        self.session_manager.set_session_data(user_id, key, value)
    
    def _clear_session(self, user_id: int):
        """Clear user session"""
        self.session_manager.clear_session(user_id)
    
    def _is_in_wizard(self, user_id: int) -> bool:
        """Check if user is in a wizard"""
        return self.session_manager.is_in_wizard(user_id)
    
    def _get_wizard_step(self, user_id: int) -> Optional[str]:
        """Get current wizard step"""
        return self.session_manager.get_wizard_step(user_id)
    
    def _set_wizard_step(self, user_id: int, step: str):
        """Set wizard step"""
        self.session_manager.set_wizard_step(user_id, step)
    
    def _clear_wizard(self, user_id: int):
        """Clear wizard state"""
        self.session_manager.clear_wizard(user_id)
