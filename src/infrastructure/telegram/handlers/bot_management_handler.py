#!/usr/bin/env python3
"""Bot management handler for Telegram bot."""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Tuple

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, ForceReply
from telegram.constants import ParseMode

from .base_handler import BaseTelegramHandler
from ....core.credential_utils import list_profiles
from ..templates import TelegramTemplates
from ...services.bot_creation_service import BotCreationService
from ....core.unified_command_processor import UnifiedCommandProcessor
from ..auth import require_auth
import re
import json
from datetime import datetime, timedelta


class BotManagementHandler(BaseTelegramHandler):
    """Handler for bot management operations."""

    def __init__(self, bot_token: str):
        """Initialize bot management handler"""
        super().__init__(bot_token)
        self.bot_creation_service = BotCreationService()
        self.unified_processor = UnifiedCommandProcessor()

    async def handle_command(self, update, context) -> None:
        """Handle command - required by abstract base class"""
        pass

    def _parse_bot_list_output(self, output: str) -> List[Dict[str, Any]]:
        """Parse bot.sh list output into structured data"""
        containers = []
        lines = output.strip().split('\n')

        # Find the data section (after the header)
        data_started = False
        for line in lines:
            line = line.strip()

            # Skip header and separator lines
            if '==' in line or 'NAME' in line and 'STATUS' in line:
                data_started = True
                continue
            if '--' in line:
                continue
            if line.startswith('📈 Summary:') or line.startswith('📊 Summary:'):
                break

            if data_started and line and not line.startswith('⚠️'):
                # Parse container line: NAME STATUS CREATED
                parts = line.split()
                if len(parts) >= 3:
                    name = parts[0]
                    status_part = parts[1]

                    # Extract status (remove emoji)
                    status = 'running' if '🟢' in status_part else 'stopped'

                    # Extract created date (remaining parts)
                    created = ' '.join(parts[2:])

                    # Determine symbol from container name
                    symbol = self._extract_symbol_from_name(name)

                    container = {
                        'name': name,
                        'symbol': symbol,
                        'status': status,
                        'created': created,
                        'uptime': self._calculate_uptime(created) if status == 'running' else None
                    }

                    containers.append(container)

        return containers

    def _extract_symbol_from_name(self, container_name: str) -> str:
        """Extract trading symbol from container name"""
        # Container names follow pattern:
        # New format: {profile}-{symbol}usdt (e.g., main-btcusdt)
        # Old format: {symbol}usdt (e.g., btcusdt)

        name = container_name.lower()

        # Remove common suffixes and prefixes
        name = re.sub(r'[-_](bot|trader|trading|container)$', '', name)
        name = re.sub(r'^(bot|trader|trading)[-_]', '', name)

        # Handle new format: profile-symbolusdt
        if '-' in name and name.endswith('usdt'):
            # Extract symbol part after the last dash
            parts = name.split('-')
            if len(parts) >= 2:
                symbol_part = parts[-1]  # Get the last part
                if symbol_part.endswith('usdt'):
                    symbol = symbol_part[:-4]  # Remove 'usdt'
                else:
                    symbol = symbol_part
            else:
                symbol = name[:-4] if name.endswith('usdt') else name
        elif name.endswith('usdt'):
            # Old format: symbolusdt
            symbol = name[:-4]  # Remove 'usdt'
        else:
            symbol = name

        # Handle special cases where container name doesn't match symbol
        special_cases = { }

        if symbol in special_cases:
            return special_cases[symbol]

        return symbol.upper()

    def _extract_profile_from_name(self, container_name: str) -> Optional[str]:
        """Extract profile from container name"""
        name = container_name.lower()

        # Handle new format: profile-symbolusdt
        if '-' in name and name.endswith('usdt'):
            parts = name.split('-')
            if len(parts) >= 2:
                # Return all parts except the last one (which is symbolusdt)
                profile_parts = parts[:-1]
                return '-'.join(profile_parts)

        # Old format has no profile
        return None

    def _calculate_uptime(self, created_str: str) -> str:
        """Calculate uptime from created timestamp"""
        try:
            # Parse created timestamp (format: 2025-05-30 23:47:41)
            created = datetime.strptime(created_str, '%Y-%m-%d %H:%M:%S')
            now = datetime.now()
            uptime = now - created

            days = uptime.days
            hours, remainder = divmod(uptime.seconds, 3600)
            minutes, _ = divmod(remainder, 60)

            if days > 0:
                return f"{days}d {hours}h {minutes}m"
            elif hours > 0:
                return f"{hours}h {minutes}m"
            else:
                return f"{minutes}m"

        except Exception:
            return "Unknown"

    def _filter_trading_containers(self, containers: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Filter out non-trading containers (system, db, etc.)"""
        # List of container names/patterns to exclude
        exclude_patterns = [
            'postgres', 'redis', 'mysql', 'mongodb',
            'nginx', 'apache', 'traefik',
            'prometheus', 'grafana', 'elasticsearch',
            'rabbitmq', 'kafka', 'zookeeper',
            'docker', 'registry', 'portainer',
            'telegram-bot'  # Exclude the telegram bot itself
        ]

        filtered = []
        for container in containers:
            name = container['name'].lower()

            # Skip if matches exclude patterns
            if any(pattern in name for pattern in exclude_patterns):
                continue

            # Skip if it's clearly a system container
            if name.startswith(('system-', 'infra-', 'monitoring-')):
                continue

            filtered.append(container)

        return filtered
    
    @require_auth("USER")
    async def handle_start_bot(self, update: Update, context) -> None:
        """Handle /startbot command with enhanced profile selection"""
        # Check if this is a quick command with all parameters
        if len(context.args) >= 2:
            await self._handle_quick_startbot(update, context)
        else:
            # Show enhanced usage with profile selection option
            await self._show_startbot_usage(update)

    async def _show_startbot_usage(self, update: Update) -> None:
        """Show enhanced startbot usage with profile selection"""
        usage_msg = (
            "🚀 **Tạo Bot Trading Nhanh**\n\n"
            "**Cách 1: Command đầy đủ**\n"
            "`/startbot <symbol> <amount> [dca_amount] [profile]`\n\n"
            "**Cách 2: Wizard với profile selection**\n"
            "Sử dụng `/createbot` để có giao diện chọn profile\n\n"
            "**Tham số:**\n"
            "• `symbol` - Symbol trading (VD: BTC, ETH, SOL)\n"
            "• `amount` - Số tiền đầu tư (USDT)\n"
            "• `dca_amount` - Số tiền DCA (tùy chọn, mặc định: 0)\n"
            "• `profile` - Tên profile (tùy chọn, auto-select nếu không có)\n\n"
            "**Ví dụ:**\n"
            "• `/startbot BTC 50 25 main` - BTC với profile main\n"
            "• `/startbot ETH 100 30` - ETH với auto-select profile\n"
            "• `/startbot SOL 75` - SOL cơ bản\n\n"
            "💡 **Multi-Profile:** Chạy nhiều bot cùng symbol với profiles khác nhau!\n"
            "📦 **Container naming:** `{profile}-{symbol}usdt` (VD: main-btcusdt)"
        )

        # Add quick action buttons
        keyboard = [
            [
                {"text": "🧙‍♂️ Wizard Mode", "callback_data": "startbot_wizard"},
                {"text": "📋 View Profiles", "callback_data": "startbot_profiles"}
            ],
            [
                {"text": "📚 Examples", "callback_data": "startbot_examples"},
                {"text": "❌ Cancel", "callback_data": "startbot_cancel"}
            ]
        ]

        reply_markup = self._create_inline_keyboard(keyboard)

        await update.message.reply_text(
            usage_msg,
            reply_markup=reply_markup,
            parse_mode=ParseMode.MARKDOWN
        )

    async def _handle_quick_startbot(self, update: Update, context) -> None:
        """Handle quick startbot with all parameters provided"""
        # Parse arguments
        symbol = context.args[0].upper()

        try:
            amount = float(context.args[1])
        except (ValueError, IndexError):
            await self._send_error_message(update, f"Amount không hợp lệ: {context.args[1] if len(context.args) > 1 else 'missing'}")
            return

        # Parse DCA amount (optional)
        dca_amount = 0.0
        if len(context.args) >= 3:
            try:
                dca_amount = float(context.args[2])
            except ValueError:
                await self._send_error_message(update, f"DCA amount không hợp lệ: {context.args[2]}")
                return

        # Parse profile (optional)
        profile = None
        if len(context.args) >= 4:
            profile = context.args[3]

        # Check for test mode flag
        test_mode = False
        if len(context.args) >= 5 and context.args[4].lower() in ["test", "testmode", "test_mode"]:
            test_mode = True

        try:
            # Show processing message with enhanced info
            processing_msg = await update.message.reply_text(
                f"🔄 **Đang tạo bot...**\n\n"
                f"📊 **Thông tin:**\n"
                f"• Symbol: `{symbol}`\n"
                f"• Amount: `${amount} USDT`\n"
                f"• DCA Amount: `${dca_amount} USDT`\n"
                f"• Profile: `{profile or 'auto-select'}`\n"
                f"• Test Mode: `{test_mode}`\n\n"
                f"📦 **Container:** `{profile + '-' if profile else ''}{symbol.lower()}usdt`",
                parse_mode=ParseMode.MARKDOWN
            )

            # Create trading bot using shared service
            result = await self.bot_creation_service.create_trading_bot(
                symbol=symbol,
                amount=amount,
                dca_amount=dca_amount,
                profile=profile,
                direction="LONG",  # Default direction
                test_mode=test_mode,
                strategy_type="conservative_long"  # Default strategy
            )

            # Delete processing message
            try:
                await processing_msg.delete()
            except:
                pass

            if result[0] == 0:
                # Success with enhanced message
                success_msg = result[1] + f"\n\n🎯 **Quick Actions:**\n• `/status {symbol}` - Xem trạng thái\n• `/logs {symbol}` - Xem logs\n• `/list` - Danh sách tất cả bots"

                await self._send_safe_message(
                    update.message.reply_text,
                    success_msg,
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                # Error
                await self._send_error_message(update, result[2])

        except Exception as e:
            self.logger.error(f"Error in _handle_quick_startbot: {e}")
            await self._send_error_message(update, f"Lỗi tạo bot: {str(e)}")

    async def handle_startbot_callback(self, query, data: str) -> None:
        """Handle startbot callback actions"""
        try:
            if data == "startbot_wizard":
                await query.edit_message_text(
                    "🧙‍♂️ **Wizard Mode**\n\n"
                    "Để sử dụng wizard với profile selection, hãy dùng:\n\n"
                    "`/createbot`\n\n"
                    "Wizard sẽ hướng dẫn bạn:\n"
                    "• Chọn profile từ danh sách\n"
                    "• Nhập symbol và amount\n"
                    "• Cấu hình DCA và direction\n"
                    "• Xem preview trước khi tạo\n\n"
                    "💡 **Ưu điểm:** Giao diện thân thiện, ít lỗi nhập liệu",
                    parse_mode=ParseMode.MARKDOWN
                )
            elif data == "startbot_profiles":
                await self._show_available_profiles_for_startbot(query)
            elif data == "startbot_examples":
                await self._show_startbot_examples(query)
            elif data == "startbot_cancel":
                await query.edit_message_text(
                    "❌ **Đã hủy**\n\n"
                    "Sử dụng `/startbot` hoặc `/createbot` khi cần tạo bot mới.",
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                await query.edit_message_text("❌ Startbot callback không hợp lệ")
        except Exception as e:
            self.logger.error(f"Error in handle_startbot_callback: {e}")
            await query.edit_message_text(f"❌ Lỗi: {str(e)}")

    async def _show_available_profiles_for_startbot(self, query) -> None:
        """Show available profiles for startbot"""
        try:
            from ...core.credential_utils import list_profiles
            profiles = list_profiles()

            if not profiles:
                await query.edit_message_text(
                    "❌ **Không có profiles**\n\n"
                    "Cần thêm credentials trước:\n"
                    "`/addcreds`\n\n"
                    "Sau đó có thể sử dụng `/startbot`",
                    parse_mode=ParseMode.MARKDOWN
                )
                return

            message = "🔑 **Available Profiles**\n\n"

            for i, profile in enumerate(profiles, 1):
                profile_name = profile.get('profile', 'Unknown')
                message += f"{i}. **{profile_name}**\n"
                message += f"   📦 Container format: `{profile_name}-{{symbol}}usdt`\n"
                message += f"   🚀 Example: `/startbot BTC 50 25 {profile_name}`\n\n"

            message += "💡 **Auto-select:** Nếu không chỉ định profile, sẽ dùng profile đầu tiên"

            await query.edit_message_text(message, parse_mode=ParseMode.MARKDOWN)

        except Exception as e:
            self.logger.error(f"Error showing profiles for startbot: {e}")
            await query.edit_message_text(f"❌ Lỗi: {str(e)}")

    async def _show_startbot_examples(self, query) -> None:
        """Show startbot examples"""
        try:
            message = (
                "📚 **Startbot Examples**\n\n"
                "**1. Basic Usage:**\n"
                "`/startbot BTC 50`\n"
                "→ Container: `btcusdt` (legacy format)\n\n"
                "**2. With DCA:**\n"
                "`/startbot ETH 100 30`\n"
                "→ Container: `ethusdt`, DCA: $30\n\n"
                "**3. With Profile:**\n"
                "`/startbot SOL 75 25 main`\n"
                "→ Container: `main-solusdt`\n\n"
                "**4. Multi-Profile Same Symbol:**\n"
                "`/startbot BTC 50 0 main`\n"
                "`/startbot BTC 100 0 backup`\n"
                "→ Containers: `main-btcusdt`, `backup-btcusdt`\n\n"
                "**5. Test Mode:**\n"
                "`/startbot BTC 50 25 main test`\n"
                "→ Container: `main-btcusdt` (test mode)\n\n"
                "💡 **Tip:** Profile giúp chạy nhiều bot cùng symbol!"
            )

            await query.edit_message_text(message, parse_mode=ParseMode.MARKDOWN)

        except Exception as e:
            self.logger.error(f"Error showing startbot examples: {e}")
            await query.edit_message_text(f"❌ Lỗi: {str(e)}")

    @require_auth("USER")
    async def handle_stop_bot(self, update: Update, context) -> None:
        """Handle /stop command for stopping trading bots with smart detection"""
        if not context.args:
            await self._send_error_message(update, "Sử dụng: /stop <symbol> hoặc /stop <profile-symbol>")
            return

        input_arg = context.args[0]

        try:
            # Check if input is specific container name (profile-symbol format)
            if '-' in input_arg and input_arg.endswith('usdt'):
                # Direct container name provided
                container = await self._find_container_by_name(input_arg)
                if container:
                    await self._show_stop_confirmation(update, input_arg, container_name=input_arg)
                else:
                    await self._send_error_message(update, f"❌ Container '{input_arg}' không tìm thấy")
                return

            # Smart detection by symbol
            symbol = input_arg.upper()
            containers = await self._find_containers_by_symbol(symbol)

            if not containers:
                await self._send_error_message(update, f"❌ Không tìm thấy bot nào cho symbol {symbol}")
                return
            elif len(containers) == 1:
                # Only one container found, show confirmation
                container = containers[0]
                await self._show_stop_confirmation(update, symbol, container_name=container['name'])
            else:
                # Multiple containers found, show selection
                message = f"📊 **Tìm thấy {len(containers)} bots cho {symbol}:**\n\n"
                keyboard = []

                for container in containers:
                    profile_text = f" ({container['profile']})" if container['profile'] else " (legacy)"
                    status_emoji = "🟢" if "running" in container['status'].lower() else "🔴"
                    message += f"{status_emoji} `{container['name']}`{profile_text}\n"

                    keyboard.append([InlineKeyboardButton(
                        f"🛑 Stop {container['name']}",
                        callback_data=f"stop_confirm_{container['name']}"
                    )])

                keyboard.append([InlineKeyboardButton("❌ Hủy", callback_data="stop_cancel")])
                reply_markup = InlineKeyboardMarkup(keyboard)

                message += f"\nChọn bot cần dừng:"
                await update.message.reply_text(message, reply_markup=reply_markup, parse_mode=ParseMode.MARKDOWN)

        except Exception as e:
            self.logger.error(f"Error in handle_stop_bot: {e}")
            await self._send_error_message(update, str(e))

    async def _show_stop_confirmation(self, update: Update, symbol: str, container_name: str) -> None:
        """Show stop confirmation for specific container"""
        keyboard = [
            [
                InlineKeyboardButton("✅ Xác nhận", callback_data=f"stop_confirm_{container_name}"),
                InlineKeyboardButton("❌ Hủy", callback_data="stop_cancel")
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.message.reply_text(
            f"⚠️ **Xác nhận dừng bot**\n\n"
            f"Container: `{container_name}`\n"
            f"Symbol: {symbol}\n\n"
            f"Bạn có chắc muốn dừng bot này?",
            reply_markup=reply_markup,
            parse_mode=ParseMode.MARKDOWN
        )
    
    async def handle_stop_confirm(self, query, container_identifier: str) -> None:
        """Handle bot stop confirmation - container_identifier can be symbol or container name"""
        try:
            # Determine if identifier is container name or symbol
            if '-' in container_identifier and container_identifier.endswith('usdt'):
                # Direct container name
                container_name = container_identifier
                symbol = self._extract_symbol_from_name(container_name)
            else:
                # Symbol - need to find container
                symbol = container_identifier.upper()
                containers = await self._find_containers_by_symbol(symbol)
                if not containers:
                    await query.edit_message_text(
                        f"❌ Không tìm thấy container cho symbol {symbol}",
                        parse_mode=None
                    )
                    return
                elif len(containers) > 1:
                    await query.edit_message_text(
                        f"❌ Tìm thấy nhiều containers cho {symbol}. Vui lòng chỉ định cụ thể.",
                        parse_mode=None
                    )
                    return
                else:
                    container_name = containers[0]['name']

            # Step 1: Stop the container using direct Docker API
            stop_success, stop_message = await self._docker_operation_by_name("stop", container_name)

            if stop_success:
                # Step 2: Remove the container after stopping
                remove_success, remove_message = await self._docker_operation_by_name("remove", container_name)

                if remove_success:
                    await query.edit_message_text(
                        f"✅ **Bot đã được dừng thành công**\n\n"
                        f"Container: `{container_name}`\n"
                        f"Symbol: {symbol}",
                        parse_mode=ParseMode.MARKDOWN
                    )
                else:
                    # Stop succeeded but remove failed
                    await query.edit_message_text(
                        f"⚠️ **Bot đã được dừng nhưng không thể xóa container**\n\n"
                        f"Container: `{container_name}`\n"
                        f"Lỗi: {remove_message}",
                        parse_mode=ParseMode.MARKDOWN
                    )
            else:
                # Stop failed
                await query.edit_message_text(
                    f"❌ **Lỗi dừng bot**\n\n"
                    f"Container: `{container_name}`\n"
                    f"Lỗi: {stop_message}",
                    parse_mode=ParseMode.MARKDOWN
                )

        except Exception as e:
            self.logger.error(f"Error in handle_stop_confirm: {e}")
            await query.edit_message_text(
                f"❌ Lỗi xử lý: {str(e)}",
                parse_mode=None
            )

    async def _docker_operation_by_name(self, operation: str, container_name: str) -> Tuple[bool, str]:
        """Perform Docker operation on specific container by name"""
        try:
            if operation == "stop":
                cmd = ["docker", "stop", container_name]
            elif operation == "remove":
                cmd = ["docker", "rm", container_name]
            else:
                return False, f"Unknown operation: {operation}"

            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()

            if process.returncode == 0:
                return True, stdout.decode('utf-8').strip()
            else:
                return False, stderr.decode('utf-8').strip()

        except Exception as e:
            return False, str(e)
    
    async def _get_enhanced_bot_list(self, filters: dict = None) -> Tuple[int, str, Optional[Any], Optional[Any]]:
        """
        Get enhanced bot list data using direct integration
        Returns: (return_code, error_message, template, keyboard)
        """
        try:
            # Use unified processor for consistent container retrieval
            result = await self.unified_processor.process_list_command(filters)

            if result[0] != 0:
                return (result[0], result[1], None, None)

            containers = result[2]

            # Convert containers to the format expected by the template
            trading_containers = []
            for container in containers:
                container_name = container.get('name', 'unknown')
                # Convert container data to expected format
                trading_container = {
                    'name': container_name,
                    'symbol': self._extract_symbol_from_name(container_name),
                    'profile': self._extract_profile_from_name(container_name),
                    'status': 'running' if container.get('running', False) else 'stopped',
                    'created': container.get('created', '')
                }
                trading_containers.append(trading_container)

            # Filter to only trading containers (already filtered by get_trading_containers)
            filtered_containers = self._filter_trading_containers(trading_containers)

            # Apply additional filters if provided
            if filters:
                filtered_containers = self._apply_list_filters(filtered_containers, filters)

            # Enhance container data with additional info
            enhanced_containers = []
            for container in filtered_containers:
                enhanced = await self._enhance_container_info(container)
                enhanced_containers.append(enhanced)

            # Get overall stats (if available)
            stats = await self._get_trading_stats(enhanced_containers)

            # Use profile-grouped template
            template = TelegramTemplates.bot_list_by_profile(enhanced_containers, stats)

            # Create inline keyboard
            keyboard = self._create_inline_keyboard(template.keyboard) if template.keyboard else None

            return (0, "", template, keyboard)

        except Exception as e:
            self.logger.error(f"Error getting enhanced bot list: {e}")
            return (1, str(e), None, None)

    @require_auth("USER")
    async def handle_list_bots(self, update: Update, context) -> None:
        """Handle /list command with advanced filtering support"""
        try:
            # Parse filter arguments
            filter_args = self._parse_list_filters(context.args)

            # Get containers with filters applied
            return_code, error_msg, template, keyboard = await self._get_enhanced_bot_list(filter_args)

            if return_code != 0:
                await self._send_error_message(update, error_msg)
                return

            # Add filter info to message if filters are applied
            if filter_args:
                filter_info = self._format_filter_info(filter_args)
                template.content = f"{filter_info}\n{template.content}"

            # Send message with inline keyboard
            await update.message.reply_text(
                template.content,
                parse_mode=ParseMode.HTML,
                reply_markup=keyboard
            )

        except Exception as e:
            self.logger.error(f"Error in handle_list_bots: {e}")
            await self._send_error_message(update, str(e))

    def _parse_list_filters(self, args: list) -> dict:
        """Parse filter arguments from /list command"""
        filters = {}

        if not args:
            return filters

        i = 0
        while i < len(args):
            arg = args[i].lower()

            if arg in ['--profile', '-p'] and i + 1 < len(args):
                filters['profile'] = args[i + 1]
                i += 2
            elif arg in ['--symbol', '-s'] and i + 1 < len(args):
                filters['symbol'] = args[i + 1].upper()
                i += 2
            elif arg in ['--status', '--state'] and i + 1 < len(args):
                status = args[i + 1].lower()
                if status in ['running', 'stopped', 'all']:
                    filters['status'] = status
                i += 2
            elif arg in ['--help', '-h']:
                filters['show_help'] = True
                i += 1
            else:
                # Treat as symbol if no flag
                filters['symbol'] = arg.upper()
                i += 1

        return filters

    def _format_filter_info(self, filters: dict) -> str:
        """Format filter information for display"""
        filter_parts = []

        if filters.get('profile'):
            filter_parts.append(f"Profile: {filters['profile']}")
        if filters.get('symbol'):
            filter_parts.append(f"Symbol: {filters['symbol']}")
        if filters.get('status'):
            status_text = {"running": "Đang chạy", "stopped": "Đã dừng", "all": "Tất cả"}
            filter_parts.append(f"Status: {status_text.get(filters['status'], filters['status'])}")

        if filter_parts:
            return f"🔍 **Filters:** {' • '.join(filter_parts)}\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n"

        return ""

    def _apply_list_filters(self, containers: list, filters: dict) -> list:
        """Apply filters to container list"""
        filtered = containers

        # Filter by profile
        if filters.get('profile'):
            target_profile = filters['profile'].lower()
            filtered = [c for c in filtered
                       if (c.get('profile') or 'legacy').lower() == target_profile]

        # Filter by symbol
        if filters.get('symbol'):
            target_symbol = filters['symbol'].upper()
            filtered = [c for c in filtered
                       if c.get('symbol', '').upper() == target_symbol]

        # Filter by status
        if filters.get('status') and filters['status'] != 'all':
            if filters['status'] == 'running':
                filtered = [c for c in filtered if c.get('status') == 'running']
            elif filters['status'] == 'stopped':
                filtered = [c for c in filtered if c.get('status') != 'running']

        return filtered

    async def _enhance_container_info(self, container: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance container info with additional details"""
        enhanced = container.copy()

        try:
            # Try to get more detailed status using direct CLI
            status_result = await self.execute_cli_command("status", [container['name']])

            if status_result[0] == 0:
                # Parse status output for additional info
                status_info = self._parse_status_output(status_result[1])
                enhanced.update(status_info)

        except Exception as e:
            self.logger.debug(f"Could not enhance info for {container['name']}: {e}")

        return enhanced

    def _parse_status_output(self, output: str) -> Dict[str, Any]:
        """Parse bot status output for additional information"""
        info = {}

        try:
            # Look for common patterns in status output
            lines = output.split('\n')

            for line in lines:
                line = line.strip()

                # Extract configuration info
                if 'amount:' in line.lower():
                    amount_match = re.search(r'amount[:\s]+\$?(\d+(?:\.\d+)?)', line, re.IGNORECASE)
                    if amount_match:
                        info.setdefault('config', {})['amount'] = amount_match.group(1)

                if 'direction:' in line.lower():
                    direction_match = re.search(r'direction[:\s]+(\w+)', line, re.IGNORECASE)
                    if direction_match:
                        info.setdefault('config', {})['direction'] = direction_match.group(1)

                if 'test' in line.lower() and 'mode' in line.lower():
                    info.setdefault('config', {})['test_mode'] = True

                # Extract performance info
                if 'pnl' in line.lower() or 'profit' in line.lower():
                    # Match patterns like: $0.00, +1.23, -2.45, $+1.23, etc.
                    pnl_match = re.search(r'[\$]?([\+\-]?\d+(?:\.\d+)?)', line)
                    if pnl_match:
                        pnl_value = pnl_match.group(1)
                        # Store the numeric value without $ sign
                        info.setdefault('performance', {})['pnl'] = pnl_value

                if 'trades' in line.lower():
                    trades_match = re.search(r'(\d+)', line)
                    if trades_match:
                        info.setdefault('performance', {})['trades'] = trades_match.group(1)

        except Exception as e:
            self.logger.debug(f"Error parsing status output: {e}")

        return info

    async def _get_trading_stats(self, containers: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate overall trading statistics"""
        stats = {
            'total_pnl': 0.0,
            'total_trades': 0,
            'win_rate': 0.0
        }

        try:
            total_pnl = 0.0
            total_trades = 0

            for container in containers:
                perf = container.get('performance', {})

                if perf.get('pnl') is not None:
                    try:
                        # Remove $ sign, + sign, and any whitespace
                        pnl_str = str(perf['pnl']).replace('$', '').replace('+', '').strip()
                        # Handle empty string cases
                        if pnl_str:
                            pnl = float(pnl_str)
                            total_pnl += pnl
                    except (ValueError, TypeError) as e:
                        self.logger.debug(f"Error parsing PnL '{perf['pnl']}': {e}")
                        pass

                if perf.get('trades'):
                    try:
                        trades = int(perf['trades'])
                        total_trades += trades
                    except ValueError:
                        pass

            stats['total_pnl'] = f"${total_pnl:+.2f}" if total_pnl != 0 else "$0.00"
            stats['total_trades'] = total_trades

            # Calculate win rate based on actual data
            if total_trades > 0:
                # Count winning trades (positive PnL)
                winning_trades = 0
                for container in containers:
                    perf = container.get('performance', {})
                    if perf.get('pnl') is not None:
                        try:
                            pnl_str = str(perf['pnl']).replace('$', '').replace('+', '').strip()
                            if pnl_str and float(pnl_str) > 0:
                                winning_trades += 1
                        except (ValueError, TypeError):
                            pass

                stats['win_rate'] = (winning_trades / total_trades) * 100 if total_trades > 0 else 0.0
            else:
                stats['win_rate'] = 0.0

        except Exception as e:
            self.logger.debug(f"Error calculating stats: {e}")

        return stats



    @require_auth("USER")
    async def handle_stop_all_bots(self, update: Update, context) -> None:
        """Handle /stopall command for stopping all running bots"""
        try:
            # Use unified processor for consistent data
            result = await self.unified_processor.process_list_command()

            if result[0] != 0:
                await self._send_error_message(update, result[1])
                return

            containers = result[2]

            if not containers:
                await self._send_error_message(update, "Không thể lấy danh sách containers")
                return

            # Filter for running trading containers using consistent field
            running_containers = [c for c in containers if c.get('running', False)]

            if not running_containers:
                await update.message.reply_text(
                    TelegramTemplates.info_message(
                        "No Running Bots",
                        "There are no running trading bots to stop."
                    ),
                    parse_mode=ParseMode.HTML
                )
                return

            # Create confirmation message
            content = f"⚠️ {TelegramTemplates.bold('Confirm Stop All Bots')}\n\n"
            content += f"You are about to stop {len(running_containers)} running bot(s):\n\n"

            for container in running_containers:
                # Extract symbol from container name using existing method
                symbol = self._extract_symbol_from_name(container['name'])
                content += f"• 🟢 {TelegramTemplates.bold(symbol)} ({container['name']})\n"

            content += f"\n⚠️ {TelegramTemplates.bold('Warning:')} This will stop all active trading!\n"
            content += "Open positions will remain but bots will stop monitoring them.\n\n"
            content += "Are you sure you want to continue?"

            # Create confirmation keyboard
            keyboard = [
                [
                    InlineKeyboardButton("✅ Yes, Stop All", callback_data="stopall_confirm"),
                    InlineKeyboardButton("❌ Cancel", callback_data="stopall_cancel")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                content,
                reply_markup=reply_markup,
                parse_mode=ParseMode.HTML
            )

        except Exception as e:
            self.logger.error(f"Error in handle_stop_all_bots: {e}")
            await self._send_error_message(update, str(e))

    
    @require_auth("USER")
    async def handle_strategies(self, update: Update, context) -> None:
        """Handle /strategies command - show strategy comparison"""
        try:
            from src.infrastructure.telegram.templates import TelegramTemplates
            
            template = TelegramTemplates.strategy_comparison_table()
            keyboard = self._create_inline_keyboard(template.keyboard) if template.keyboard else None
            
            await update.message.reply_text(
                template.content,
                reply_markup=keyboard,
                parse_mode=ParseMode.HTML
            )
            
        except Exception as e:
            self.logger.error(f"Error in handle_strategies: {e}")
            await self._send_error_message(update, str(e))

    async def _execute_stop_all(self, query) -> None:
        """Execute stop all bots operation"""
        try:
            # Get running containers again using unified processor (in case status changed)
            result = await self.unified_processor.process_list_command()

            if result[0] != 0:
                await query.edit_message_text(
                    f"❌ **Error getting bot list**: {result[1]}",
                    parse_mode=ParseMode.MARKDOWN
                )
                return

            containers = result[2]

            if not containers:
                await query.edit_message_text(
                    f"❌ **Error getting bot list**",
                    parse_mode=ParseMode.MARKDOWN
                )
                return

            # Filter for running containers using consistent field
            running_containers = [c for c in containers if c.get('running', False)]

            if not running_containers:
                await query.edit_message_text(
                    "ℹ️ **No running bots found**\n\nAll bots are already stopped.",
                    parse_mode=ParseMode.MARKDOWN
                )
                return

            # Stop each bot
            stopped_count = 0
            failed_stops = []

            status_message = f"🔄 **Stopping {len(running_containers)} bots...**\n\n"
            await query.edit_message_text(status_message, parse_mode=ParseMode.MARKDOWN)

            for container in running_containers:
                try:
                    container_name = container.get('name', 'unknown')

                    # Step 1: Stop the container using direct Docker API
                    stop_success, stop_message = await self._docker_operation_by_name("stop", container_name)

                    if stop_success:
                        # Step 2: Remove the container after stopping
                        remove_success, remove_message = await self._docker_operation_by_name("remove", container_name)

                        if remove_success:
                            stopped_count += 1
                            symbol = self._extract_symbol_from_name(container_name)
                            status_message += f"✅ {symbol} stopped & removed\n"
                        else:
                            stopped_count += 1
                            symbol = self._extract_symbol_from_name(container_name)
                            status_message += f"⚠️ {symbol} stopped (remove failed)\n"
                    else:
                        symbol = self._extract_symbol_from_name(container_name)
                        failed_stops.append(symbol)
                        status_message += f"❌ {symbol} failed to stop\n"

                    # Update status message
                    await query.edit_message_text(status_message, parse_mode=ParseMode.MARKDOWN)

                except Exception as e:
                    symbol = self._extract_symbol_from_name(container_name)
                    failed_stops.append(symbol)
                    self.logger.error(f"Error stopping {container['name']}: {e}")

            # Final summary
            final_message = f"🏁 **Stop All Complete**\n\n"
            final_message += f"✅ Successfully stopped & removed: {stopped_count}\n"

            if failed_stops:
                final_message += f"❌ Failed to stop: {len(failed_stops)}\n"
                final_message += f"Failed bots: {', '.join(failed_stops)}\n\n"
                final_message += "💡 Try stopping failed bots individually with `/stop <symbol>`"
            else:
                final_message += "\n🎉 All trading bots have been stopped and removed successfully!"

            await query.edit_message_text(final_message, parse_mode=None)

        except Exception as e:
            self.logger.error(f"Error in _execute_stop_all: {e}")
            await query.edit_message_text(
                f"❌ Lỗi trong quá trình dừng tất cả bot: {str(e)}",
                parse_mode=None
            )
    
    @require_auth("USER")
    async def handle_status_bot(self, update: Update, context) -> None:
        """Handle /status command with smart detection"""
        if not context.args:
            # Show all bots status
            await self.handle_list_bots(update, context)
            return

        input_arg = context.args[0]

        try:
            # Check if input is specific container name (profile-symbol format)
            if '-' in input_arg and input_arg.endswith('usdt'):
                # Direct container name provided
                container = await self._find_container_by_name(input_arg)
                if container:
                    await self._show_container_status(update, input_arg)
                else:
                    await self._send_error_message(update, f"❌ Container '{input_arg}' không tìm thấy")
                return

            # Smart detection by symbol
            symbol = input_arg.upper()
            containers = await self._find_containers_by_symbol(symbol)

            if not containers:
                await self._send_error_message(update, f"❌ Không tìm thấy bot nào cho symbol {symbol}")
                return
            elif len(containers) == 1:
                # Only one container found, show status
                container = containers[0]
                await self._show_container_status(update, container['name'])
            else:
                # Multiple containers found, show selection
                message = f"📊 **Tìm thấy {len(containers)} bots cho {symbol}:**\n\n"
                keyboard = []

                for container in containers:
                    profile_text = f" ({container['profile']})" if container['profile'] else " (legacy)"
                    status_emoji = "🟢" if "running" in container['status'].lower() else "🔴"
                    message += f"{status_emoji} `{container['name']}`{profile_text}\n"

                    keyboard.append([InlineKeyboardButton(
                        f"📊 Status {container['name']}",
                        callback_data=f"status_{container['name']}"
                    )])

                keyboard.append([InlineKeyboardButton("❌ Hủy", callback_data="status_cancel")])
                reply_markup = InlineKeyboardMarkup(keyboard)

                message += f"\nChọn bot để xem status:"
                await update.message.reply_text(message, reply_markup=reply_markup, parse_mode=ParseMode.MARKDOWN)

        except Exception as e:
            self.logger.error(f"Error getting bot status: {e}")
            await self._send_error_message(update, str(e))

    async def _show_container_status(self, update: Update, container_name: str) -> None:
        """Show status for specific container"""
        try:
            # Use direct CLI integration
            result = await self.execute_cli_command("status", [container_name])

            if result[0] == 0:
                symbol = self._extract_symbol_from_name(container_name)
                await update.message.reply_text(
                    f"📊 **Status {container_name}**\n"
                    f"Symbol: {symbol}\n\n```\n{result[1]}\n```",
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                await self._send_error_message(update, result[2] or result[1])

        except Exception as e:
            self.logger.error(f"Error showing container status: {e}")
            await self._send_error_message(update, str(e))
    
    @require_auth("USER")
    async def handle_logs_bot(self, update: Update, context) -> None:
        """Handle /logs command for bot logs with smart detection"""
        if not context.args:
            await self._send_error_message(update, "Sử dụng: /logs <symbol> [lines] hoặc /logs <profile-symbol> [lines]")
            return

        input_arg = context.args[0]
        lines = context.args[1] if len(context.args) > 1 else "50"

        try:
            lines_int = int(lines) if lines.isdigit() else 50

            # Check if input is specific container name (profile-symbol format)
            if '-' in input_arg and input_arg.endswith('usdt'):
                # Direct container name provided
                container = await self._find_container_by_name(input_arg)
                if container:
                    await self._show_container_logs(update, input_arg, lines_int)
                else:
                    await self._send_error_message(update, f"❌ Container '{input_arg}' không tìm thấy")
                return

            # Smart detection by symbol
            symbol = input_arg.upper()
            containers = await self._find_containers_by_symbol(symbol)

            if not containers:
                await self._send_error_message(update, f"❌ Không tìm thấy bot nào cho symbol {symbol}")
                return
            elif len(containers) == 1:
                # Only one container found, show logs
                container = containers[0]
                await self._show_container_logs(update, container['name'], lines_int)
            else:
                # Multiple containers found, show selection
                message = f"📊 **Tìm thấy {len(containers)} bots cho {symbol}:**\n\n"
                keyboard = []

                for container in containers:
                    profile_text = f" ({container['profile']})" if container['profile'] else " (legacy)"
                    status_emoji = "🟢" if "running" in container['status'].lower() else "🔴"
                    message += f"{status_emoji} `{container['name']}`{profile_text}\n"

                    keyboard.append([InlineKeyboardButton(
                        f"📋 Logs {container['name']}",
                        callback_data=f"logs_{container['name']}_{lines_int}"
                    )])

                keyboard.append([InlineKeyboardButton("❌ Hủy", callback_data="logs_cancel")])
                reply_markup = InlineKeyboardMarkup(keyboard)

                message += f"\nChọn bot để xem logs:"
                await update.message.reply_text(message, reply_markup=reply_markup, parse_mode=ParseMode.MARKDOWN)

        except Exception as e:
            self.logger.error(f"Error getting bot logs: {e}")
            await self._send_error_message(update, str(e))

    async def _show_container_logs(self, update: Update, container_name: str, lines: int) -> None:
        """Show logs for specific container"""
        try:
            success, logs_content = await self._docker_logs_by_name(container_name, lines)

            if success and logs_content:
                # Truncate if too long for Telegram (leave room for header and code formatting)
                output = logs_content
                max_length = 3800  # Leave room for header and code block formatting
                if len(output) > max_length:
                    output = output[-max_length:] + "\n\n... (truncated)"

                # Send as code block for better formatting
                await update.message.reply_text(
                    f"📋 **Logs {container_name}** ({lines} dòng cuối):\n\n```\n{output}\n```",
                    parse_mode=ParseMode.MARKDOWN
                )
            elif success:
                await update.message.reply_text(f"📋 **Logs {container_name}:** Không có logs")
            else:
                await self._send_error_message(update, logs_content or "Failed to get logs")

        except Exception as e:
            self.logger.error(f"Error showing container logs: {e}")
            await self._send_error_message(update, str(e))

    async def _docker_logs_by_name(self, container_name: str, lines: int) -> Tuple[bool, str]:
        """Get Docker logs for specific container by name"""
        try:
            cmd = ["docker", "logs", "--tail", str(lines), container_name]

            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()

            if process.returncode == 0:
                # Combine stdout and stderr for complete logs
                logs = stdout.decode('utf-8') + stderr.decode('utf-8')
                return True, logs.strip()
            else:
                return False, stderr.decode('utf-8').strip()

        except Exception as e:
            return False, str(e)
    
    @require_auth("USER")
    async def handle_restart_bot(self, update: Update, context) -> None:
        """Handle /restart command with smart detection"""
        if not context.args:
            await self._send_error_message(update, "Sử dụng: /restart <symbol> hoặc /restart <profile-symbol>")
            return

        input_arg = context.args[0]

        try:
            # Check if input is specific container name (profile-symbol format)
            if '-' in input_arg and input_arg.endswith('usdt'):
                # Direct container name provided
                container = await self._find_container_by_name(input_arg)
                if container:
                    await self._show_restart_confirmation(update, input_arg, container_name=input_arg)
                else:
                    await self._send_error_message(update, f"❌ Container '{input_arg}' không tìm thấy")
                return

            # Smart detection by symbol
            symbol = input_arg.upper()
            containers = await self._find_containers_by_symbol(symbol)

            if not containers:
                await self._send_error_message(update, f"❌ Không tìm thấy bot nào cho symbol {symbol}")
                return
            elif len(containers) == 1:
                # Only one container found, show confirmation
                container = containers[0]
                await self._show_restart_confirmation(update, symbol, container_name=container['name'])
            else:
                # Multiple containers found, show selection
                message = f"📊 **Tìm thấy {len(containers)} bots cho {symbol}:**\n\n"
                keyboard = []

                for container in containers:
                    profile_text = f" ({container['profile']})" if container['profile'] else " (legacy)"
                    status_emoji = "🟢" if "running" in container['status'].lower() else "🔴"
                    message += f"{status_emoji} `{container['name']}`{profile_text}\n"

                    keyboard.append([InlineKeyboardButton(
                        f"🔄 Restart {container['name']}",
                        callback_data=f"restart_confirm_{container['name']}"
                    )])

                keyboard.append([InlineKeyboardButton("❌ Hủy", callback_data="restart_cancel")])
                reply_markup = InlineKeyboardMarkup(keyboard)

                message += f"\nChọn bot cần restart:"
                await update.message.reply_text(message, reply_markup=reply_markup, parse_mode=ParseMode.MARKDOWN)

        except Exception as e:
            self.logger.error(f"Error restarting bot: {e}")
            await self._send_error_message(update, str(e))

    async def _show_restart_confirmation(self, update: Update, symbol: str, container_name: str) -> None:
        """Show restart confirmation for specific container"""
        keyboard = [
            [
                InlineKeyboardButton("✅ Xác nhận", callback_data=f"restart_confirm_{container_name}"),
                InlineKeyboardButton("❌ Hủy", callback_data="restart_cancel")
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.message.reply_text(
            f"🔄 **Xác nhận restart bot**\n\n"
            f"Container: `{container_name}`\n"
            f"Symbol: {symbol}\n\n"
            f"Bạn có chắc muốn restart bot này?",
            reply_markup=reply_markup,
            parse_mode=ParseMode.MARKDOWN
        )

    @require_auth("USER")
    async def handle_profiles(self, update: Update, context) -> None:
        """Handle /profiles command for profile management"""
        try:
            containers = await self.get_trading_containers()

            if not containers:
                await self._send_info_message(
                    update,
                    "📭 **Chưa có bot nào**\n\n"
                    "Tạo bot đầu tiên với `/createbot` hoặc `/startbot`\n\n"
                    "💡 **Multi-Profile Support:**\n"
                    "• Chạy nhiều bot cùng symbol với profiles khác nhau\n"
                    "• VD: main-btcusdt, test-btcusdt, backup-btcusdt\n"
                    "• Smart detection tự động phát hiện containers",
                    "🔑"
                )
                return

            # Group containers by profile
            profiles = {}
            for container in containers:
                profile = self._extract_profile_from_name(container.get('name', '')) or 'Legacy'
                if profile not in profiles:
                    profiles[profile] = []
                profiles[profile].append(container)

            # Create profile management message
            message = "🔑 **Profile Management Dashboard**\n"
            message += "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n"

            message += f"📊 **Tổng quan:**\n"
            message += f"├─ 🔑 {len(profiles)} profiles\n"
            message += f"├─ 🤖 {len(containers)} total bots\n"
            message += f"└─ 🟢 {sum(1 for c in containers if c.get('running', False))} đang chạy\n\n"

            # Show each profile
            for profile_name, profile_containers in profiles.items():
                running_count = sum(1 for c in profile_containers if c.get('running', False))
                stopped_count = len(profile_containers) - running_count

                profile_emoji = "🔑" if profile_name != 'Legacy' else "📦"
                message += f"{profile_emoji} **{profile_name}**\n"
                message += f"├─ 📊 {len(profile_containers)} bots\n"
                message += f"├─ 🟢 {running_count} chạy • 🔴 {stopped_count} dừng\n"
                message += f"└─ 📈 Symbols: "

                symbols = list(set(self._extract_symbol_from_name(c.get('name', '')) for c in profile_containers))
                message += ", ".join(symbols) + "\n\n"

            # Create management keyboard
            keyboard = []

            # Profile actions row
            keyboard.append([
                {"text": "📊 Profile Stats", "callback_data": "profile_stats"},
                {"text": "🔄 Refresh", "callback_data": "profile_refresh"}
            ])

            # Management actions
            if len(profiles) > 1:
                keyboard.append([
                    {"text": "🔀 Compare Profiles", "callback_data": "profile_compare"},
                    {"text": "📋 Export Config", "callback_data": "profile_export"}
                ])

            # Quick actions
            keyboard.append([
                {"text": "🚀 Create New Bot", "callback_data": "bot_create_quick"},
                {"text": "📚 Profile Guide", "callback_data": "profile_help"}
            ])

            reply_markup = self._create_inline_keyboard(keyboard)

            await update.message.reply_text(
                message,
                reply_markup=reply_markup,
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            self.logger.error(f"Error in handle_profiles: {e}")
            await self._send_error_message(update, str(e))

    async def handle_stop_callback(self, query, data: str) -> None:
        """Handle stop bot callback"""
        try:
            if data.startswith("stop_confirm_"):
                symbol = data.replace("stop_confirm_", "")

                # Validate symbol
                if not symbol or len(symbol.strip()) == 0:
                    await query.edit_message_text("❌ Symbol không hợp lệ")
                    return

                await self.handle_stop_confirm(query, symbol.strip())
            elif data == "stop_cancel":
                await query.edit_message_text(
                    "❌ Đã hủy dừng bot",
                    parse_mode=None
                )
            else:
                await query.edit_message_text("❌ Callback không hợp lệ")
        except Exception as e:
            self.logger.error(f"Error in handle_stop_callback: {e}")
            await query.edit_message_text(f"❌ Lỗi: {str(e)}")

    async def handle_logs_callback(self, query, data: str) -> None:
        """Handle logs bot callback"""
        try:
            if data.startswith("logs_") and data != "logs_cancel":
                # Parse: logs_{container_name}_{lines}
                parts = data.split("_", 2)  # Split into max 3 parts
                if len(parts) >= 3:
                    container_name = parts[1]
                    lines = int(parts[2]) if parts[2].isdigit() else 50

                    # Show logs for the selected container
                    success, logs_content = await self._docker_logs_by_name(container_name, lines)

                    if success and logs_content:
                        # Truncate if too long for Telegram
                        output = logs_content
                        max_length = 3800
                        if len(output) > max_length:
                            output = output[-max_length:] + "\n\n... (truncated)"

                        await query.edit_message_text(
                            f"📋 **Logs {container_name}** ({lines} dòng cuối):\n\n```\n{output}\n```",
                            parse_mode=ParseMode.MARKDOWN
                        )
                    elif success:
                        await query.edit_message_text(f"📋 **Logs {container_name}:** Không có logs")
                    else:
                        await query.edit_message_text(f"❌ Lỗi lấy logs: {logs_content}")
                else:
                    await query.edit_message_text("❌ Callback format không hợp lệ")
            elif data == "logs_cancel":
                await query.edit_message_text(
                    "❌ Đã hủy xem logs",
                    parse_mode=None
                )
            else:
                await query.edit_message_text("❌ Callback không hợp lệ")
        except Exception as e:
            self.logger.error(f"Error in handle_logs_callback: {e}")
            await query.edit_message_text(f"❌ Lỗi: {str(e)}")

    async def handle_status_callback(self, query, data: str) -> None:
        """Handle status bot callback"""
        try:
            if data.startswith("status_") and data != "status_cancel":
                # Parse: status_{container_name}
                container_name = data[7:]  # Remove "status_" prefix

                # Show status for the selected container
                result = await self.execute_cli_command("status", [container_name])

                if result[0] == 0:
                    symbol = self._extract_symbol_from_name(container_name)
                    await query.edit_message_text(
                        f"📊 **Status {container_name}**\n"
                        f"Symbol: {symbol}\n\n```\n{result[1]}\n```",
                        parse_mode=ParseMode.MARKDOWN
                    )
                else:
                    await query.edit_message_text(f"❌ Lỗi lấy status: {result[2] or result[1]}")
            elif data == "status_cancel":
                await query.edit_message_text(
                    "❌ Đã hủy xem status",
                    parse_mode=None
                )
            else:
                await query.edit_message_text("❌ Callback không hợp lệ")
        except Exception as e:
            self.logger.error(f"Error in handle_status_callback: {e}")
            await query.edit_message_text(f"❌ Lỗi: {str(e)}")

    async def handle_restart_callback(self, query, data: str) -> None:
        """Handle restart bot callback"""
        try:
            if data.startswith("restart_confirm_"):
                container_name = data.replace("restart_confirm_", "")

                # Validate container name
                if not container_name or len(container_name.strip()) == 0:
                    await query.edit_message_text("❌ Container name không hợp lệ")
                    return

                # Use direct CLI integration for restart
                result = await self.execute_cli_command("restart", [container_name])
                symbol = self._extract_symbol_from_name(container_name)

                if result[0] == 0:
                    await query.edit_message_text(
                        f"✅ **Bot đã được restart thành công**\n\n"
                        f"Container: `{container_name}`\n"
                        f"Symbol: {symbol}",
                        parse_mode=ParseMode.MARKDOWN
                    )
                else:
                    await query.edit_message_text(
                        f"❌ **Lỗi restart bot**\n\n"
                        f"Container: `{container_name}`\n"
                        f"Lỗi: {result[2] or result[1]}",
                        parse_mode=ParseMode.MARKDOWN
                    )
            elif data == "restart_cancel":
                await query.edit_message_text(
                    "❌ Đã hủy restart bot",
                    parse_mode=None
                )
            else:
                await query.edit_message_text("❌ Callback không hợp lệ")
        except Exception as e:
            self.logger.error(f"Error in handle_restart_callback: {e}")
            await query.edit_message_text(f"❌ Lỗi: {str(e)}")

    async def handle_stopall_callback(self, query, data: str) -> None:
        """Handle stop all bots callback"""
        try:
            if data == "stopall_confirm":
                await self._execute_stop_all(query)
            elif data == "stopall_cancel":
                await query.edit_message_text(
                    "❌ Đã hủy dừng tất cả bot",
                    parse_mode=None
                )
            else:
                await query.edit_message_text("❌ Callback không hợp lệ")
        except Exception as e:
            self.logger.error(f"Error in handle_stopall_callback: {e}")
            await query.edit_message_text(f"❌ Lỗi: {str(e)}")

    async def handle_list_callback(self, query) -> None:
        """Handle bot list refresh callback - uses same logic as /list command"""
        try:
            # Use shared logic to avoid code duplication
            return_code, error_msg, template, keyboard = await self._get_enhanced_bot_list()

            if return_code != 0:
                await query.edit_message_text(
                    f"❌ Không thể lấy danh sách bot: {error_msg}",
                    parse_mode=ParseMode.HTML
                )
                return

            # Edit message with proper HTML parsing
            await query.edit_message_text(
                template.content,
                parse_mode=ParseMode.HTML,  # Fixed: Use HTML parsing instead of None
                reply_markup=keyboard
            )

        except Exception as e:
            self.logger.error(f"Error in handle_list_callback: {e}")
            await query.edit_message_text(f"❌ Lỗi: {str(e)}", parse_mode=ParseMode.HTML)

    async def handle_startall_callback(self, query) -> None:
        """Handle start all bots callback"""
        try:
            # Get list of containers using unified processor
            result = await self.unified_processor.process_list_command()

            if result[0] != 0:
                await query.edit_message_text(
                    f"❌ Không thể lấy danh sách bot: {result[1]}",
                    parse_mode=None
                )
                return

            containers = result[2]

            if not containers:
                await query.edit_message_text(
                    f"❌ Không thể lấy danh sách bot",
                    parse_mode=None
                )
                return

            stopped_containers = [c for c in containers if not c.get('running', False)]

            if not stopped_containers:
                await query.edit_message_text(
                    "ℹ️ Tất cả bot đã đang chạy",
                    parse_mode=None
                )
                return

            # Start all stopped containers
            success_count = 0
            for container in stopped_containers:
                symbol = container.get('symbol', 'unknown')
                try:
                    # Use direct CLI integration for start
                    start_result = await self.execute_cli_command("start", [symbol])
                    if start_result[0] == 0:
                        success_count += 1
                except Exception as e:
                    self.logger.error(f"Error starting {symbol}: {e}")

            await query.edit_message_text(
                f"✅ Đã khởi động {success_count}/{len(stopped_containers)} bot",
                parse_mode=None
            )

        except Exception as e:
            self.logger.error(f"Error in handle_startall_callback: {e}")
            await query.edit_message_text(f"❌ Lỗi: {str(e)}")

    async def handle_clean_stopped_callback(self, query) -> None:
        """Handle clean stopped bots callback"""
        try:
            # Get list of containers using direct integration
            containers = await self.get_trading_containers()

            if not containers:
                await query.edit_message_text(
                    f"❌ Không thể lấy danh sách bot",
                    parse_mode=None
                )
                return

            stopped_containers = [c for c in containers if c.get('status', '').lower() != 'running']

            if not stopped_containers:
                await query.edit_message_text(
                    "ℹ️ Không có bot nào đang dừng để dọn dẹp",
                    parse_mode=None
                )
                return

            # Remove all stopped containers
            success_count = 0
            failed_containers = []

            for container in stopped_containers:
                container_name = container.get('name', 'unknown')
                try:
                    # Use direct Docker API for remove
                    remove_success, remove_message = await self._docker_operation_by_name("remove", container_name)
                    if remove_success:
                        success_count += 1
                    else:
                        failed_containers.append(container_name)
                except Exception as e:
                    self.logger.error(f"Error removing {container_name}: {e}")
                    failed_containers.append(container_name)

            # Prepare response message
            if success_count > 0:
                message = f"🧹 Đã dọn dẹp {success_count} bot đã dừng"
                if failed_containers:
                    message += f"\n⚠️ Không thể xóa: {', '.join(failed_containers)}"
            else:
                message = f"❌ Không thể dọn dẹp bot nào"
                if failed_containers:
                    message += f"\nLỗi: {', '.join(failed_containers)}"

            await query.edit_message_text(
                message,
                parse_mode=None
            )

        except Exception as e:
            self.logger.error(f"Error in handle_clean_stopped_callback: {e}")
            await query.edit_message_text(f"❌ Lỗi: {str(e)}")

    async def handle_quick_actions_callback(self, query, data: str) -> None:
        """Handle quick action callbacks"""
        try:
            if data == "bot_create_quick":
                await query.edit_message_text(
                    "🚀 **Tạo Bot Nhanh**\n\n"
                    "Sử dụng lệnh sau để tạo bot:\n"
                    "`/createbot` - Wizard tạo bot\n"
                    "`/startbot <symbol> <amount>` - Tạo nhanh\n\n"
                    "**Ví dụ:**\n"
                    "• `/startbot BTC 50` - Bot BTC với $50\n"
                    "• `/startbot ETH 100 30 main` - Bot ETH với profile main\n\n"
                    "💡 **Mẹo:** Dùng profile để chạy nhiều bot cùng symbol!",
                    parse_mode=ParseMode.MARKDOWN
                )
            elif data == "bot_logs_quick":
                await query.edit_message_text(
                    "📋 **Xem Logs Nhanh**\n\n"
                    "Sử dụng lệnh sau:\n"
                    "`/logs <symbol>` - Smart detection\n"
                    "`/logs <container_name>` - Trực tiếp\n\n"
                    "**Ví dụ:**\n"
                    "• `/logs btc` - Hiển thị selection nếu có nhiều\n"
                    "• `/logs main-btc` - Trực tiếp container main-btc\n"
                    "• `/logs btc 100` - 100 dòng logs cuối",
                    parse_mode=ParseMode.MARKDOWN
                )
            elif data == "bot_status_all":
                await self._show_status_all(query)
            elif data == "bot_restart_all":
                await self._show_restart_all_confirmation(query)
            elif data == "bot_help":
                await query.edit_message_text(
                    "📚 **Trợ Giúp Nhanh**\n\n"
                    "**Commands chính:**\n"
                    "• `/help` - Hướng dẫn đầy đủ\n"
                    "• `/list` - Danh sách bots\n"
                    "• `/createbot` - Tạo bot mới\n"
                    "• `/addcreds` - Thêm API credentials\n\n"
                    "**Multi-Profile:**\n"
                    "• Chạy nhiều bot cùng symbol\n"
                    "• Smart detection tự động\n"
                    "• VD: main-btc, test-btc, backup-btc\n\n"
                    "💡 Dùng `/help` để xem hướng dẫn chi tiết!",
                    parse_mode=ParseMode.MARKDOWN
                )
            elif data == "bot_add_creds":
                await query.edit_message_text(
                    "🔑 **Thêm API Credentials**\n\n"
                    "Sử dụng lệnh:\n"
                    "`/addcreds` - Thêm credentials mới\n\n"
                    "**Cần thiết:**\n"
                    "• API Key từ Binance\n"
                    "• API Secret từ Binance\n"
                    "• Permissions: Spot Trading\n\n"
                    "⚠️ **Lưu ý:** Chỉ sử dụng API key có quyền Spot Trading!",
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                await query.edit_message_text("❌ Quick action không hợp lệ")
        except Exception as e:
            self.logger.error(f"Error in handle_quick_actions_callback: {e}")
            await query.edit_message_text(f"❌ Lỗi: {str(e)}")

    async def _show_status_all(self, query) -> None:
        """Show status for all containers"""
        try:
            # Use unified processor for consistent data
            result = await self.unified_processor.process_list_command()

            if result[0] != 0:
                await query.edit_message_text(f"❌ Không thể lấy danh sách bot: {result[1]}")
                return

            containers = result[2]

            if not containers:
                await query.edit_message_text("📭 Không có bot nào để hiển thị status")
                return

            message = "📊 **Status Tất Cả Bots**\n\n"

            for container in containers:
                name = container.get('name', 'Unknown')
                status = "🟢 Đang chạy" if container.get('running', False) else "🔴 Đã dừng"
                symbol = container.get('symbol', 'Unknown')
                profile = container.get('profile', '')

                profile_text = f" ({profile})" if profile else " (legacy)"
                message += f"{status} **{symbol}**{profile_text}\n"
                message += f"   📦 `{name}`\n\n"

            await query.edit_message_text(message, parse_mode=ParseMode.MARKDOWN)

        except Exception as e:
            self.logger.error(f"Error in _show_status_all: {e}")
            await query.edit_message_text(f"❌ Lỗi: {str(e)}")

    async def _show_restart_all_confirmation(self, query) -> None:
        """Show restart all confirmation"""
        try:
            containers = await self.get_trading_containers()

            if not containers:
                await query.edit_message_text("📭 Không có bot nào để restart")
                return

            running_count = sum(1 for c in containers if c.get('running', False))

            message = f"🔄 **Restart Tất Cả Bots**\n\n"
            message += f"Sẽ restart {len(containers)} bots ({running_count} đang chạy)\n\n"
            message += "⚠️ **Cảnh báo:** Tất cả bots sẽ bị khởi động lại!\n\n"
            message += "Bạn có chắc muốn tiếp tục?"

            keyboard = [
                [
                    InlineKeyboardButton("✅ Restart All", callback_data="restart_all_confirm"),
                    InlineKeyboardButton("❌ Hủy", callback_data="restart_all_cancel")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(message, reply_markup=reply_markup, parse_mode=ParseMode.MARKDOWN)

        except Exception as e:
            self.logger.error(f"Error in _show_restart_all_confirmation: {e}")
            await query.edit_message_text(f"❌ Lỗi: {str(e)}")

    def _create_inline_keyboard(self, keyboard_data):
        """Create inline keyboard from template data"""
        if not keyboard_data:
            return None

        keyboard = []
        for row in keyboard_data:
            button_row = []
            for button in row:
                button_row.append(InlineKeyboardButton(
                    text=button['text'],
                    callback_data=button['callback_data']
                ))
            keyboard.append(button_row)

        return InlineKeyboardMarkup(keyboard)

    async def _send_unauthorized_message(self, update: Update) -> None:
        """Send unauthorized access message to user"""
        try:
            rejection_msg = self.auth_service.get_rejection_message()

            # Add user info for admin reference
            user = update.effective_user
            user_info = f"\n\n🔍 **User Info:**\n" \
                       f"• ID: `{user.id}`\n" \
                       f"• Username: @{user.username or 'N/A'}\n" \
                       f"• Name: {user.first_name or ''} {user.last_name or ''}".strip()

            full_message = rejection_msg + user_info

            if update.message:
                await update.message.reply_text(full_message, parse_mode=ParseMode.MARKDOWN)
            elif update.callback_query:
                await update.callback_query.answer(rejection_msg, show_alert=True)

        except Exception as e:
            self.logger.error(f"Error sending unauthorized message: {e}")

    # Additional callback handlers for new functionality

    async def handle_status_all_callback(self, query) -> None:
        """Handle status all bots callback"""
        try:
            await self._show_status_all(query)
        except Exception as e:
            self.logger.error(f"Error in status_all callback: {e}")
            await query.edit_message_text("❌ Error getting status for all bots")

    async def handle_restart_all_confirm(self, query) -> None:
        """Handle restart all confirmation"""
        try:
            containers = await self.container_helper.list_containers()
            running_containers = [c for c in containers if c['status'] == 'running']

            if not running_containers:
                await query.edit_message_text(
                    "🔄 **No Running Bots**\n\nNo bots are currently running to restart.",
                    parse_mode=ParseMode.MARKDOWN
                )
                return

            # Execute restart all
            success_count = 0
            failed_containers = []

            for container in running_containers:
                try:
                    # Use unified processor for restart
                    result = await self.unified_processor.process_restart_command(container['name'])
                    if result[0] == 0:  # Success
                        success_count += 1
                    else:
                        failed_containers.append(container['name'])
                except Exception as e:
                    self.logger.error(f"Error restarting {container['name']}: {e}")
                    failed_containers.append(container['name'])

            # Show results
            message = f"🔄 **Restart All Complete**\n\n"
            message += f"✅ Successfully restarted: {success_count} bots\n"

            if failed_containers:
                message += f"❌ Failed to restart: {len(failed_containers)} bots\n"
                message += f"Failed containers: {', '.join(failed_containers[:3])}"
                if len(failed_containers) > 3:
                    message += f" and {len(failed_containers) - 3} more"

            await query.edit_message_text(message, parse_mode=ParseMode.MARKDOWN)

        except Exception as e:
            self.logger.error(f"Error in restart_all_confirm: {e}")
            await query.edit_message_text("❌ Error restarting all bots")

    async def handle_profiles_callback(self, query) -> None:
        """Handle profiles callback - refresh profiles view"""
        try:
            await self.handle_profiles(query, None)
        except Exception as e:
            self.logger.error(f"Error in profiles callback: {e}")
            await query.edit_message_text("❌ Error refreshing profiles")

    # Individual bot action handlers

    async def handle_start_individual(self, query, container_name: str) -> None:
        """Handle start individual bot"""
        try:
            result = await self.unified_processor.process_restart_command(container_name)
            if result[0] == 0:
                await query.edit_message_text(
                    f"✅ **Bot Started**\n\n`{container_name}` has been started successfully.",
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                await query.edit_message_text(
                    f"❌ **Start Failed**\n\n{result[1]}",
                    parse_mode=ParseMode.MARKDOWN
                )
        except Exception as e:
            self.logger.error(f"Error starting individual bot {container_name}: {e}")
            await query.edit_message_text(f"❌ Error starting {container_name}")

    async def handle_stop_individual(self, query, container_name: str) -> None:
        """Handle stop individual bot"""
        try:
            result = await self.unified_processor.process_stop_command(container_name)
            if result[0] == 0:
                await query.edit_message_text(
                    f"⏹️ **Bot Stopped**\n\n`{container_name}` has been stopped successfully.",
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                await query.edit_message_text(
                    f"❌ **Stop Failed**\n\n{result[1]}",
                    parse_mode=ParseMode.MARKDOWN
                )
        except Exception as e:
            self.logger.error(f"Error stopping individual bot {container_name}: {e}")
            await query.edit_message_text(f"❌ Error stopping {container_name}")

    async def handle_restart_individual(self, query, container_name: str) -> None:
        """Handle restart individual bot"""
        try:
            result = await self.unified_processor.process_restart_command(container_name)
            if result[0] == 0:
                await query.edit_message_text(
                    f"🔄 **Bot Restarted**\n\n`{container_name}` has been restarted successfully.",
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                await query.edit_message_text(
                    f"❌ **Restart Failed**\n\n{result[1]}",
                    parse_mode=ParseMode.MARKDOWN
                )
        except Exception as e:
            self.logger.error(f"Error restarting individual bot {container_name}: {e}")
            await query.edit_message_text(f"❌ Error restarting {container_name}")

    async def handle_logs_individual(self, query, container_name: str) -> None:
        """Handle logs individual bot"""
        try:
            result = await self.unified_processor.process_logs_command(container_name, 50)
            if result[0] == 0:
                logs_content = result[2] if len(result) > 2 else "No logs available"
                # Truncate logs if too long for Telegram
                if len(logs_content) > 3000:
                    logs_content = logs_content[-3000:] + "\n\n... (truncated)"

                message = f"📋 **Logs for {container_name}**\n\n```\n{logs_content}\n```"
                await query.edit_message_text(message, parse_mode=ParseMode.MARKDOWN)
            else:
                await query.edit_message_text(
                    f"❌ **Logs Failed**\n\n{result[1]}",
                    parse_mode=ParseMode.MARKDOWN
                )
        except Exception as e:
            self.logger.error(f"Error getting logs for individual bot {container_name}: {e}")
            await query.edit_message_text(f"❌ Error getting logs for {container_name}")

    async def handle_remove_individual(self, query, container_name: str) -> None:
        """Handle remove individual bot"""
        try:
            # Show confirmation first
            message = f"🗑️ **Remove Bot Confirmation**\n\n"
            message += f"Are you sure you want to remove `{container_name}`?\n\n"
            message += "⚠️ **Warning:** This action cannot be undone!"

            keyboard = [
                [{"text": "✅ Yes, Remove", "callback_data": f"remove_confirm_{container_name}"},
                 {"text": "❌ Cancel", "callback_data": "close"}]
            ]

            reply_markup = self._create_inline_keyboard(keyboard)
            await query.edit_message_text(message, parse_mode=ParseMode.MARKDOWN, reply_markup=reply_markup)

        except Exception as e:
            self.logger.error(f"Error showing remove confirmation for {container_name}: {e}")
            await query.edit_message_text(f"❌ Error preparing remove for {container_name}")

    async def handle_status_individual(self, query, container_name: str) -> None:
        """Handle status individual bot"""
        try:
            result = await self.unified_processor.process_status_command(container_name)
            if result[0] == 0:
                await query.edit_message_text(
                    f"📊 **Status for {container_name}**\n\n{result[1]}",
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                await query.edit_message_text(
                    f"❌ **Status Failed**\n\n{result[1]}",
                    parse_mode=ParseMode.MARKDOWN
                )
        except Exception as e:
            self.logger.error(f"Error getting status for individual bot {container_name}: {e}")
            await query.edit_message_text(f"❌ Error getting status for {container_name}")

    async def handle_logs_command(self, query, container_name: str, lines: int = 50) -> None:
        """Handle logs command for callback usage"""
        try:
            await self.handle_logs_individual(query, container_name)
        except Exception as e:
            self.logger.error(f"Error in logs command: {e}")
            await query.edit_message_text("❌ Error getting logs")

    
    async def handle_strategy_callback(self, query, data: str) -> None:
        """Handle strategy-related callbacks"""
        try:
            if data == "strategy_compare":
                from src.infrastructure.telegram.templates import TelegramTemplates
                template = TelegramTemplates.strategy_comparison_table()
                keyboard = self._create_inline_keyboard(template.keyboard) if template.keyboard else None
                
                await query.edit_message_text(
                    template.content,
                    reply_markup=keyboard,
                    parse_mode=ParseMode.HTML
                )
            elif data == "strategy_guide":
                from src.infrastructure.telegram.templates import TelegramTemplates
                template = TelegramTemplates.strategy_guide()
                keyboard = self._create_inline_keyboard(template.keyboard) if template.keyboard else None
                
                await query.edit_message_text(
                    template.content,
                    reply_markup=keyboard,
                    parse_mode=ParseMode.HTML
                )
            elif data == "bot_create":
                await query.edit_message_text(
                    "🤖 **Tạo Bot Trading**\n\n"
                    "Sử dụng lệnh sau để tạo bot:\n"
                    "`/createbot` - Wizard tạo bot với strategy selection\n"
                    "`/startbot <symbol> <amount>` - Tạo nhanh với default strategy\n\n"
                    "**Ví dụ:**\n"
                    "• `/createbot` - Wizard đầy đủ\n"
                    "• `/startbot BTC 50` - Bot BTC với Conservative LONG strategy\n\n"
                    "💡 **Mẹo:** Dùng `/createbot` để chọn strategy phù hợp!",
                    parse_mode=ParseMode.MARKDOWN
                )
            elif data == "close":
                await query.edit_message_text(
                    "✅ Đã đóng",
                    parse_mode=None
                )
            else:
                await query.edit_message_text("❌ Strategy callback không hợp lệ")
        except Exception as e:
            self.logger.error(f"Error in handle_strategy_callback: {e}")
            await query.edit_message_text(f"❌ Lỗi: {str(e)}")

