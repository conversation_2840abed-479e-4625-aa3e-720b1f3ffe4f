"""Self-test handler for automated testing"""
import logging
import async<PERSON>
from typing import Dict, Any, List
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from telegram.constants import ParseMode

from src.infrastructure.telegram.handlers.base_handler import BaseTelegramHandler
from src.infrastructure.telegram.auth.decorators import require_auth


class SelfTestHandler(BaseTelegramHandler):
    """Handler for self-testing bot functionality"""
    
    def __init__(self, bot_token: str, auth_service):
        super().__init__(bot_token)
        self.auth_service = auth_service
        self.logger = logging.getLogger(__name__)
        self.test_results: Dict[str, Any] = {}
        
    @require_auth("ADMIN")
    async def handle_selftest(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle /selftest command"""
        try:
            await update.message.reply_text(
                "🧪 **Self Test Menu**\n\n"
                "Choose what to test:",
                reply_markup=self._create_test_menu(),
                parse_mode=ParseMode.MARKDOWN
            )
        except Exception as e:
            self.logger.error(f"Error in handle_selftest: {e}")
            await update.message.reply_text(f"❌ Error: {str(e)}")
    
    def _create_test_menu(self) -> InlineKeyboardMarkup:
        """Create test menu keyboard"""
        keyboard = [
            [
                InlineKeyboardButton("🔧 Strategy Builder", callback_data="test_strategy_builder"),
                InlineKeyboardButton("🤖 Bot Commands", callback_data="test_bot_commands")
            ],
            [
                InlineKeyboardButton("📊 Callback Routing", callback_data="test_callbacks"),
                InlineKeyboardButton("💾 Data Storage", callback_data="test_storage")
            ],
            [
                InlineKeyboardButton("🔄 All Tests", callback_data="test_all"),
                InlineKeyboardButton("📋 Test Results", callback_data="test_results")
            ],
            [
                InlineKeyboardButton("❌ Close", callback_data="close")
            ]
        ]
        return InlineKeyboardMarkup(keyboard)
    
    async def handle_test_callback(self, query, data: str) -> None:
        """Handle test callbacks"""
        try:
            if data == "test_strategy_builder":
                await self._test_strategy_builder(query)
            elif data == "test_bot_commands":
                await self._test_bot_commands(query)
            elif data == "test_callbacks":
                await self._test_callback_routing(query)
            elif data == "test_storage":
                await self._test_data_storage(query)
            elif data == "test_all":
                await self._run_all_tests(query)
            elif data == "test_results":
                await self._show_test_results(query)
            else:
                await query.edit_message_text("❌ Unknown test callback")
                
        except Exception as e:
            self.logger.error(f"Error in handle_test_callback: {e}")
            await query.edit_message_text(f"❌ Test error: {str(e)}")
    
    async def _test_strategy_builder(self, query) -> None:
        """Test strategy builder functionality"""
        await query.edit_message_text(
            "🧪 **Testing Strategy Builder...**\n\n"
            "Testing:\n"
            "• /createstrategy command\n"
            "• Step selection callbacks\n"
            "• Template system\n"
            "• Strategy saving\n\n"
            "⏳ Running tests...",
            parse_mode=ParseMode.MARKDOWN
        )
        
        # Simulate strategy builder tests
        test_results = []
        
        # Test 1: Strategy builder templates
        try:
            from src.infrastructure.telegram.template_builders.strategy_builder_templates import StrategyBuilderTemplates
            template = StrategyBuilderTemplates.strategy_step_selection()
            test_results.append("✅ Strategy templates loading")
        except Exception as e:
            test_results.append(f"❌ Strategy templates: {str(e)}")
        
        # Test 2: Strategy service
        try:
            from src.infrastructure.services.custom_strategy_service import CustomStrategyService
            service = CustomStrategyService()
            test_results.append("✅ Strategy service initialization")
        except Exception as e:
            test_results.append(f"❌ Strategy service: {str(e)}")
        
        # Test 3: Strategy builder
        try:
            from src.core.models.custom_strategy import StrategyBuilder
            builder = StrategyBuilder(12345)
            builder.set_basic_info("test", "Test Strategy", "Test description")
            test_results.append("✅ Strategy builder functionality")
        except Exception as e:
            test_results.append(f"❌ Strategy builder: {str(e)}")
        
        # Show results
        result_text = "🧪 **Strategy Builder Test Results**\n\n" + "\n".join(test_results)
        
        keyboard = [
            [InlineKeyboardButton("🔄 Test Callbacks", callback_data="test_strategy_callbacks")],
            [InlineKeyboardButton("⬅️ Back to Menu", callback_data="test_menu")]
        ]
        
        await query.edit_message_text(
            result_text,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode=ParseMode.MARKDOWN
        )
    
    async def _test_callback_routing(self, query) -> None:
        """Test callback routing"""
        await query.edit_message_text(
            "🧪 **Testing Callback Routing...**\n\n"
            "Testing callback data routing:\n"
            "• step_* callbacks\n"
            "• builder_* callbacks\n"
            "• strategy_* callbacks\n\n"
            "⏳ Running tests...",
            parse_mode=ParseMode.MARKDOWN
        )
        
        # Test callback routing logic
        test_callbacks = [
            "step_basic_info",
            "step_entry", 
            "step_dca",
            "step_exit",
            "builder_save",
            "builder_preview",
            "strategy_view_test"
        ]
        
        results = []
        for callback_data in test_callbacks:
            # Test routing logic
            should_route = (
                callback_data.startswith("builder_") or 
                callback_data.startswith("template_") or 
                callback_data == "my_strategies" or 
                callback_data.startswith("delete_strategy_") or 
                callback_data.startswith("confirm_delete_") or 
                callback_data.startswith("strategy_") or 
                callback_data.startswith("step_") or 
                callback_data.startswith("entry_") or 
                callback_data.startswith("dca_") or 
                callback_data.startswith("exit_") or 
                callback_data.startswith("tp_") or 
                callback_data.startswith("sl_")
            )
            
            if should_route:
                results.append(f"✅ {callback_data} → builder_callback")
            else:
                results.append(f"❌ {callback_data} → not routed")
        
        result_text = "🧪 **Callback Routing Test Results**\n\n" + "\n".join(results)
        
        keyboard = [
            [InlineKeyboardButton("🧪 Test Live Callbacks", callback_data="test_live_callbacks")],
            [InlineKeyboardButton("⬅️ Back to Menu", callback_data="test_menu")]
        ]
        
        await query.edit_message_text(
            result_text,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode=ParseMode.MARKDOWN
        )
    
    async def _test_bot_commands(self, query) -> None:
        """Test bot commands"""
        await query.edit_message_text(
            "🧪 **Testing Bot Commands...**\n\n"
            "Available commands to test:\n"
            "• /createstrategy\n"
            "• /mystrategies\n"
            "• /createbot\n"
            "• /listbots\n\n"
            "Click a command to simulate:",
            reply_markup=self._create_command_test_menu(),
            parse_mode=ParseMode.MARKDOWN
        )
    
    def _create_command_test_menu(self) -> InlineKeyboardMarkup:
        """Create command test menu"""
        keyboard = [
            [
                InlineKeyboardButton("/createstrategy", callback_data="simulate_createstrategy"),
                InlineKeyboardButton("/mystrategies", callback_data="simulate_mystrategies")
            ],
            [
                InlineKeyboardButton("/createbot", callback_data="simulate_createbot"),
                InlineKeyboardButton("/listbots", callback_data="simulate_listbots")
            ],
            [
                InlineKeyboardButton("⬅️ Back to Menu", callback_data="test_menu")
            ]
        ]
        return InlineKeyboardMarkup(keyboard)
    
    async def _run_all_tests(self, query) -> None:
        """Run all tests"""
        await query.edit_message_text(
            "🧪 **Running All Tests...**\n\n"
            "⏳ This may take a moment...",
            parse_mode=ParseMode.MARKDOWN
        )
        
        all_results = []
        
        # Test 1: Imports
        try:
            from src.infrastructure.telegram.handlers.strategy_builder_handler import StrategyBuilderHandler
            all_results.append("✅ Strategy Builder Handler import")
        except Exception as e:
            all_results.append(f"❌ Strategy Builder Handler: {str(e)}")
        
        # Test 2: Templates
        try:
            from src.infrastructure.telegram.template_builders.strategy_builder_templates import StrategyBuilderTemplates
            template = StrategyBuilderTemplates.strategy_builder_welcome()
            all_results.append("✅ Strategy Builder Templates")
        except Exception as e:
            all_results.append(f"❌ Strategy Builder Templates: {str(e)}")
        
        # Test 3: Services
        try:
            from src.infrastructure.services.custom_strategy_service import CustomStrategyService
            service = CustomStrategyService()
            all_results.append("✅ Custom Strategy Service")
        except Exception as e:
            all_results.append(f"❌ Custom Strategy Service: {str(e)}")
        
        # Test 4: Models
        try:
            from src.core.models.custom_strategy import StrategyBuilder, CustomStrategy
            builder = StrategyBuilder(12345)
            all_results.append("✅ Strategy Models")
        except Exception as e:
            all_results.append(f"❌ Strategy Models: {str(e)}")
        
        result_text = "🧪 **Complete Test Results**\n\n" + "\n".join(all_results)
        
        keyboard = [
            [InlineKeyboardButton("📋 Save Results", callback_data="save_test_results")],
            [InlineKeyboardButton("⬅️ Back to Menu", callback_data="test_menu")]
        ]
        
        await query.edit_message_text(
            result_text,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode=ParseMode.MARKDOWN
        )
        
        # Store results
        self.test_results['all_tests'] = all_results
    
    async def _show_test_results(self, query) -> None:
        """Show stored test results"""
        if not self.test_results:
            await query.edit_message_text(
                "📋 **No Test Results**\n\n"
                "Run some tests first to see results.",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("⬅️ Back to Menu", callback_data="test_menu")]
                ]),
                parse_mode=ParseMode.MARKDOWN
            )
            return
        
        result_text = "📋 **Stored Test Results**\n\n"
        for test_name, results in self.test_results.items():
            result_text += f"**{test_name}:**\n"
            if isinstance(results, list):
                result_text += "\n".join(results) + "\n\n"
            else:
                result_text += str(results) + "\n\n"
        
        keyboard = [
            [InlineKeyboardButton("🗑️ Clear Results", callback_data="clear_test_results")],
            [InlineKeyboardButton("⬅️ Back to Menu", callback_data="test_menu")]
        ]
        
        await query.edit_message_text(
            result_text,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode=ParseMode.MARKDOWN
        )

    async def _test_data_storage(self, query) -> None:
        """Test data storage functionality"""
        await query.edit_message_text(
            "🧪 **Testing Data Storage...**\n\n"
            "Testing:\n"
            "• Strategy storage\n"
            "• File system access\n"
            "• JSON serialization\n\n"
            "⏳ Running tests...",
            parse_mode=ParseMode.MARKDOWN
        )

        results = []

        # Test storage directory
        try:
            import os
            storage_path = "/root/.autotrader/custom_strategies"
            if os.path.exists(storage_path):
                results.append("✅ Storage directory exists")
            else:
                results.append("❌ Storage directory missing")
        except Exception as e:
            results.append(f"❌ Storage test: {str(e)}")

        # Test JSON operations
        try:
            import json
            test_data = {"test": "data", "number": 123}
            json_str = json.dumps(test_data)
            parsed = json.loads(json_str)
            results.append("✅ JSON serialization")
        except Exception as e:
            results.append(f"❌ JSON test: {str(e)}")

        result_text = "🧪 **Data Storage Test Results**\n\n" + "\n".join(results)

        keyboard = [
            [InlineKeyboardButton("⬅️ Back to Menu", callback_data="test_menu")]
        ]

        await query.edit_message_text(
            result_text,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode=ParseMode.MARKDOWN
        )

    async def handle_simulate_command(self, query, command: str) -> None:
        """Simulate command execution"""
        try:
            if command == "simulate_createstrategy":
                await self._simulate_createstrategy(query)
            elif command == "simulate_mystrategies":
                await self._simulate_mystrategies(query)
            elif command == "test_live_callbacks":
                await self._test_live_callbacks(query)
            elif command == "test_strategy_callbacks":
                await self._test_strategy_callbacks(query)
            elif command == "test_menu":
                await query.edit_message_text(
                    "🧪 **Self Test Menu**\n\n"
                    "Choose what to test:",
                    reply_markup=self._create_test_menu(),
                    parse_mode=ParseMode.MARKDOWN
                )
            elif command == "clear_test_results":
                self.test_results.clear()
                await query.edit_message_text(
                    "✅ **Test Results Cleared**",
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton("⬅️ Back to Menu", callback_data="test_menu")]
                    ]),
                    parse_mode=ParseMode.MARKDOWN
                )
        except Exception as e:
            self.logger.error(f"Error in simulate command: {e}")
            await query.edit_message_text(f"❌ Simulation error: {str(e)}")

    async def _simulate_createstrategy(self, query) -> None:
        """Simulate /createstrategy command"""
        await query.edit_message_text(
            "🧪 **Simulating /createstrategy**\n\n"
            "This would show the strategy builder welcome screen.\n\n"
            "Testing template loading...",
            parse_mode=ParseMode.MARKDOWN
        )

        try:
            from src.infrastructure.telegram.template_builders.strategy_builder_templates import StrategyBuilderTemplates
            template = StrategyBuilderTemplates.strategy_builder_welcome()

            # Show actual strategy builder interface
            keyboard = []
            if template.keyboard:
                for row in template.keyboard:
                    keyboard_row = []
                    for button in row:
                        keyboard_row.append(InlineKeyboardButton(
                            button["text"],
                            callback_data=f"test_{button['callback_data']}"
                        ))
                    keyboard.append(keyboard_row)

            keyboard.append([InlineKeyboardButton("⬅️ Back to Tests", callback_data="test_menu")])

            await query.edit_message_text(
                f"✅ **Strategy Builder Simulation**\n\n{template.content}",
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode=ParseMode.HTML
            )

        except Exception as e:
            await query.edit_message_text(
                f"❌ **Simulation Failed**\n\n"
                f"Error: {str(e)}\n\n"
                f"This indicates an issue with strategy builder templates.",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("⬅️ Back to Tests", callback_data="test_menu")]
                ]),
                parse_mode=ParseMode.MARKDOWN
            )

    async def _simulate_mystrategies(self, query) -> None:
        """Simulate /mystrategies command"""
        await query.edit_message_text(
            "🧪 **Simulating /mystrategies**\n\n"
            "This would show user's saved strategies.\n\n"
            "Testing strategy service...",
            parse_mode=ParseMode.MARKDOWN
        )

        try:
            from src.infrastructure.services.custom_strategy_service import CustomStrategyService
            service = CustomStrategyService()

            # Test listing strategies
            user_id = query.from_user.id
            strategies = service.list_user_strategies(user_id)

            result_text = f"✅ **My Strategies Simulation**\n\n"
            if strategies:
                result_text += f"Found {len(strategies)} strategies:\n"
                for strategy in strategies[:3]:  # Show first 3
                    result_text += f"• {strategy.get('display_name', 'Unknown')}\n"
            else:
                result_text += "No strategies found (this is normal for testing)."

            await query.edit_message_text(
                result_text,
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("⬅️ Back to Tests", callback_data="test_menu")]
                ]),
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            await query.edit_message_text(
                f"❌ **Simulation Failed**\n\n"
                f"Error: {str(e)}\n\n"
                f"This indicates an issue with strategy service.",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("⬅️ Back to Tests", callback_data="test_menu")]
                ]),
                parse_mode=ParseMode.MARKDOWN
            )

    async def _test_live_callbacks(self, query) -> None:
        """Test live callback buttons"""
        keyboard = [
            [
                InlineKeyboardButton("📝 Test step_basic_info", callback_data="test_step_basic_info"),
                InlineKeyboardButton("🎯 Test step_entry", callback_data="test_step_entry")
            ],
            [
                InlineKeyboardButton("📊 Test step_dca", callback_data="test_step_dca"),
                InlineKeyboardButton("🎯 Test step_exit", callback_data="test_step_exit")
            ],
            [
                InlineKeyboardButton("💾 Test builder_save", callback_data="test_builder_save"),
                InlineKeyboardButton("👁️ Test builder_preview", callback_data="test_builder_preview")
            ],
            [
                InlineKeyboardButton("⬅️ Back to Tests", callback_data="test_menu")
            ]
        ]

        await query.edit_message_text(
            "🧪 **Live Callback Test**\n\n"
            "Click any button below to test callback routing.\n"
            "These buttons use the actual callback data that would be sent to the strategy builder.\n\n"
            "Watch the logs to see if callbacks are routed correctly!",
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode=ParseMode.MARKDOWN
        )

    async def _test_strategy_callbacks(self, query) -> None:
        """Test strategy-specific callbacks"""
        keyboard = [
            [
                InlineKeyboardButton("🔧 From Scratch", callback_data="test_builder_scratch"),
                InlineKeyboardButton("📋 Templates", callback_data="test_builder_template")
            ],
            [
                InlineKeyboardButton("📊 My Strategies", callback_data="test_my_strategies"),
                InlineKeyboardButton("❌ Cancel", callback_data="test_builder_cancel")
            ],
            [
                InlineKeyboardButton("⬅️ Back to Tests", callback_data="test_menu")
            ]
        ]

        await query.edit_message_text(
            "🧪 **Strategy Callback Test**\n\n"
            "Test strategy builder specific callbacks:",
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode=ParseMode.MARKDOWN
        )

    async def handle_test_callback_result(self, query, data: str) -> None:
        """Handle test callback results"""
        callback_data = data.replace("test_", "")

        await query.edit_message_text(
            f"✅ **Callback Test Result**\n\n"
            f"**Callback Data:** `{callback_data}`\n"
            f"**Status:** Received successfully\n"
            f"**Routing:** This callback would be routed to strategy builder\n\n"
            f"If you see this message, the callback routing is working!",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("🔄 Test More", callback_data="test_live_callbacks")],
                [InlineKeyboardButton("⬅️ Back to Tests", callback_data="test_menu")]
            ]),
            parse_mode=ParseMode.MARKDOWN
        )
