"""
Assets Handler for Telegram Bot
Handles /assets command to view account assets
"""

import logging
import asyncio
import subprocess
import json
from typing import Optional
from telegram import Update
from telegram.ext import ContextTypes

from .base_handler import BaseTelegramHandler
from ..auth import require_auth


class Assets<PERSON>andler(BaseTelegramHandler):
    """Handler for assets-related commands"""

    def __init__(self, bot_token: str):
        super().__init__(bot_token)
        self.logger = logging.getLogger('AssetsHandler')

    async def handle_command(self, update, context) -> None:
        """Handle command - required by abstract base class"""
        # This method is required by the abstract base class
        # Actual command handling is done by specific methods like handle_assets
        pass
    
    @require_auth("USER")
    async def handle_assets(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle /assets <profile> command"""
        try:
            # Parse arguments
            args = context.args
            if not args:
                await update.message.reply_text(
                    "❌ <PERSON>hi<PERSON><PERSON> tham số profile\n\n"
                    "📝 Cách sử dụng: `/assets <profile>`\n"
                    "💡 Sử dụng `/listcreds` để xem danh sách profiles có sẵn",
                    parse_mode=None
                )
                return
            
            profile = args[0]
            
            # Send loading message
            loading_msg = await update.message.reply_text(
                f"🔍 Đang lấy thông tin assets cho profile: {profile}...",
                parse_mode=None
            )
            
            try:
                # Get assets
                assets_info = await self._get_profile_assets(profile)
                
                # Format and send response
                response = self._format_assets_response(assets_info)
                
                # Edit the loading message with results
                await loading_msg.edit_text(response, parse_mode=None)
                
            except Exception as e:
                error_msg = f"❌ Lỗi khi lấy assets: {str(e)}"
                await loading_msg.edit_text(error_msg, parse_mode=None)
                
        except Exception as e:
            self.logger.error(f"Error in handle_assets: {e}", exc_info=True)
            await self._send_error_message(update, f"❌ Lỗi hệ thống: {str(e)}")
    
    async def _get_profile_assets(self, profile: str) -> dict:
        """Get assets information for a profile using CLI integration"""
        try:
            # Use CLI integration from BaseTelegramHandler
            if hasattr(self, 'cli_integration') and self.cli_integration:
                # Use direct CLI integration for assets command
                self.logger.info(f"Using CLI integration for assets command with profile: {profile}")
                result = await self.cli_integration.execute_assets_command(profile)

                if result[0] == 0:  # Success
                    output = result[1]  # stdout
                    return self._parse_assets_output(profile, output)
                else:
                    error_msg = result[2] or result[1]  # stderr or stdout
                    raise Exception(f"Assets command failed: {error_msg}")
            else:
                # Fallback: run docker command directly
                self.logger.warning("CLI integration not available, falling back to Docker")
                return await self._run_assets_via_docker(profile)

        except Exception as e:
            self.logger.error(f"Error getting assets for profile {profile}: {e}")
            raise

    async def _run_assets_via_docker(self, profile: str) -> dict:
        """Run assets command via Docker using shared volumes"""
        # When running from Telegram bot container, we need to use the same volumes
        # that were mounted to the Telegram container

        # Use the same volume mounts as the Telegram container
        # These should be mounted from the host machine
        cmd = ['docker', 'run', '--rm', '--user', 'root',
               '--volumes-from', 'telegram-bot',  # Use volumes from telegram bot container
               'ghcr.io/hoangtrung99/autotrader-trader:latest',
               'python3', 'src/cli/assets_cli.py', 'show', profile]

        self.logger.info(f"Running Docker command: {' '.join(cmd)}")

        process = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )

        stdout, stderr = await process.communicate()

        if process.returncode == 0:
            output = stdout.decode('utf-8')
            return self._parse_assets_output(profile, output)
        else:
            error_msg = stderr.decode('utf-8')
            self.logger.error(f"Docker command failed with stderr: {error_msg}")

            # If volumes-from failed, try alternative approach
            if "No such container" in error_msg:
                return await self._run_assets_via_direct_call(profile)

            raise Exception(f"Docker command failed: {error_msg}")

    async def _run_assets_via_direct_call(self, profile: str) -> dict:
        """Fallback: Run assets command directly using Python imports"""
        try:
            # Import and run assets CLI directly
            import sys
            import os
            from io import StringIO
            from contextlib import redirect_stdout, redirect_stderr

            # Add src to path
            sys.path.insert(0, '/app/src')

            # Import assets CLI
            from cli.assets_cli import AssetsCli

            # Capture output
            stdout_capture = StringIO()
            stderr_capture = StringIO()

            # Run assets command
            assets_cli = AssetsCli()

            with redirect_stdout(stdout_capture), redirect_stderr(stderr_capture):
                await assets_cli.show_assets_command(profile)

            output = stdout_capture.getvalue()
            if output:
                return self._parse_assets_output(profile, output)
            else:
                error_output = stderr_capture.getvalue()
                raise Exception(f"Assets command failed: {error_output}")

        except Exception as e:
            self.logger.error(f"Direct call failed: {e}")
            raise Exception(f"Failed to get assets via direct call: {e}")

    def _parse_assets_output(self, profile: str, output: str) -> dict:
        """Parse CLI output to extract assets information"""
        try:
            # Extract balance information from output
            lines = output.split('\n')
            account_info = {
                'total_balance': 0,
                'free_balance': 0,
                'used_balance': 0
            }

            for line in lines:
                if 'Total Balance:' in line:
                    # Extract number from line like "Total Balance: $1234.56 USDT"
                    parts = line.split('$')
                    if len(parts) > 1:
                        amount_str = parts[1].split()[0]
                        try:
                            account_info['total_balance'] = float(amount_str)
                        except ValueError:
                            pass
                elif 'Available:' in line or 'Free Balance:' in line:
                    parts = line.split('$')
                    if len(parts) > 1:
                        amount_str = parts[1].split()[0]
                        try:
                            account_info['free_balance'] = float(amount_str)
                        except ValueError:
                            pass
                elif 'Used:' in line or 'Used Balance:' in line:
                    parts = line.split('$')
                    if len(parts) > 1:
                        amount_str = parts[1].split()[0]
                        try:
                            account_info['used_balance'] = float(amount_str)
                        except ValueError:
                            pass

            return {
                'profile': profile,
                'account_info': account_info,
                'raw_output': output,
                'success': True
            }

        except Exception as e:
            self.logger.error(f"Error parsing assets output: {e}")
            return {
                'profile': profile,
                'account_info': {'total_balance': 0, 'free_balance': 0, 'used_balance': 0},
                'raw_output': output,
                'success': False,
                'error': str(e)
            }
    
    def _format_assets_response(self, assets_info: dict) -> str:
        """Format assets information for Telegram display"""
        try:
            if not assets_info.get('success'):
                error_msg = assets_info.get('error', 'Unknown error')
                return f"❌ Không thể lấy thông tin assets: {error_msg}"

            profile = assets_info['profile']
            account_info = assets_info['account_info']

            # If we have raw output and it looks good, use it directly
            raw_output = assets_info.get('raw_output', '')
            if raw_output and '✅' in raw_output:
                # Convert CLI output to Telegram-friendly format
                telegram_output = raw_output.replace('=', '-')
                return f"💰 Assets cho Profile: {profile}\n\n{telegram_output}\n\n💡 Sử dụng /listcreds để xem tất cả profiles"

            # Fallback: build response from parsed data
            lines = [
                f"💰 Assets - Profile: {profile}",
                "-" * 30,
                "",
                "📊 Tổng quan tài khoản:",
                f"💵 Tổng số dư: ${account_info.get('total_balance', 0):.2f} USDT",
                f"💸 Có thể sử dụng: ${account_info.get('free_balance', 0):.2f} USDT",
                f"🔒 Đang sử dụng: ${account_info.get('used_balance', 0):.2f} USDT",
                "",
                "📈 Chi tiết số dư:",
                f"• Số dư khả dụng: ${account_info.get('free_balance', 0):.4f}",
                f"• Số dư đang dùng: ${account_info.get('used_balance', 0):.4f}",
                f"• Tổng số dư: ${account_info.get('total_balance', 0):.4f}",
                "",
                "✅ Lấy thông tin assets thành công",
                "",
                "💡 Sử dụng /listcreds để xem tất cả profiles"
            ]

            return "\n".join(lines)

        except Exception as e:
            self.logger.error(f"Error formatting assets response: {e}")
            return f"❌ Lỗi định dạng thông tin: {str(e)}"
    
    async def _send_error_message(self, update: Update, message: str) -> None:
        """Send error message to user"""
        try:
            await update.message.reply_text(message, parse_mode=None)
        except Exception as e:
            self.logger.error(f"Error sending error message: {e}")
