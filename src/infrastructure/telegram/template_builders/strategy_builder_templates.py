"""
Telegram templates for custom strategy builder interface
"""

from typing import List, Dict, Any, Optional
from src.infrastructure.telegram.templates import MessageTemplate, TelegramTemplates
from src.core.models.custom_strategy import (
    TechnicalIndicator, ComparisonOperator, LogicalOperator,
    CustomStrategy, IndicatorCondition, ConditionGroup
)


class StrategyBuilderTemplates:
    """Templates for strategy builder interface"""
    
    @staticmethod
    def strategy_builder_welcome() -> MessageTemplate:
        """Welcome message for strategy builder"""
        content = f"""{TelegramTemplates.bold('🎯 Custom Strategy Builder')}

{TelegramTemplates.bold('Tạo Strategy Trading Tùy Chỉnh')}

Với Strategy Builder, bạn có thể:
• 📊 Chọn technical indicators
• ⚙️ Thiết lập entry conditions
• 💰 Cấu hình DCA triggers
• 🎯 Tùy chỉnh TP/SL settings
• ⚖️ Quản lý risk management

{TelegramTemplates.bold('Bắt đầu từ:')}
• {TelegramTemplates.code('Template')} - Sử dụng template có sẵn
• {TelegramTemplates.code('Scratch')} - Tạo từ đầu hoàn toàn

💡 {TelegramTemplates.italic('Tip: Nếu bạn mới bắt đầu, hãy chọn Template để học cách hoạt động!')}"""

        keyboard = [
            [
                {"text": "📋 From Template", "callback_data": "builder_template"},
                {"text": "🔧 From Scratch", "callback_data": "builder_scratch"}
            ],
            [
                {"text": "📚 Strategy Guide", "callback_data": "builder_guide"},
                {"text": "❌ Cancel", "callback_data": "close"}
            ]
        ]

        return MessageTemplate(
            title="Strategy Builder Welcome",
            content=content,
            keyboard=keyboard
        )

    @staticmethod
    def template_selection() -> MessageTemplate:
        """Template selection interface"""
        content = f"""{TelegramTemplates.bold('📋 Strategy Templates')}

Chọn template để bắt đầu:

🛡️ {TelegramTemplates.bold('Conservative LONG')}
• Direction: LONG only
• Risk: Low (30% capital)
• TP/SL: 2.5% / 1.5%
• Best for: Beginners

⚡ {TelegramTemplates.bold('Aggressive LONG')}
• Direction: LONG only
• Risk: High (50% capital)
• TP/SL: 4.0% / 2.5%
• Best for: Experienced traders

🏃 {TelegramTemplates.bold('Scalping')}
• Direction: Both LONG/SHORT
• Risk: Medium (25% capital)
• TP/SL: 1.5% / 1.0%
• Best for: Active monitoring

💡 {TelegramTemplates.italic('Templates có thể được customize sau khi tạo!')}"""

        keyboard = [
            [
                {"text": "🛡️ Conservative LONG", "callback_data": "template_conservative_long_custom"},
                {"text": "⚡ Aggressive LONG", "callback_data": "template_aggressive_long_custom"}
            ],
            [
                {"text": "🏃 Scalping", "callback_data": "template_scalping_custom"}
            ],
            [
                {"text": "🔙 Back", "callback_data": "builder_welcome"},
                {"text": "❌ Cancel", "callback_data": "close"}
            ]
        ]

        return MessageTemplate(
            title="Template Selection",
            content=content,
            keyboard=keyboard
        )

    @staticmethod
    def strategy_step_selection() -> MessageTemplate:
        """Strategy building step selection"""
        content = f"""{TelegramTemplates.bold('🔧 Strategy Builder - Step Selection')}

{TelegramTemplates.bold('📋 Choose what to configure:')}

1️⃣ {TelegramTemplates.bold('Basic Info')} - Name, description, tags
2️⃣ {TelegramTemplates.bold('Entry Conditions')} - When to open positions
3️⃣ {TelegramTemplates.bold('DCA Settings')} - Dollar Cost Averaging triggers
4️⃣ {TelegramTemplates.bold('Exit Settings')} - Take Profit & Stop Loss
5️⃣ {TelegramTemplates.bold('Risk Management')} - Position sizing, limits

{TelegramTemplates.italic('Select a step to configure:')}"""

        keyboard = [
            [
                {"text": "📝 Basic Info", "callback_data": "step_basic_info"},
                {"text": "🎯 Entry Conditions", "callback_data": "step_entry"}
            ],
            [
                {"text": "📊 DCA Settings", "callback_data": "step_dca"},
                {"text": "🎯 Exit Settings", "callback_data": "step_exit"}
            ],
            [
                {"text": "⚖️ Risk Management", "callback_data": "step_risk"},
                {"text": "💾 Save Strategy", "callback_data": "builder_save"}
            ],
            [
                {"text": "👁️ Preview", "callback_data": "builder_preview"},
                {"text": "❌ Cancel", "callback_data": "builder_cancel"}
            ]
        ]

        return MessageTemplate(
            title="Strategy Step Selection",
            content=content,
            keyboard=keyboard
        )

    @staticmethod
    def strategy_basic_info_input() -> MessageTemplate:
        """Basic info input for custom strategy"""
        content = f"""{TelegramTemplates.bold('🔧 Create Custom Strategy')}

{TelegramTemplates.bold('Step 1: Basic Information')}

Nhập thông tin cơ bản cho strategy:

{TelegramTemplates.code('Format:')}
{TelegramTemplates.code('strategy_name | Display Name | Description')}

{TelegramTemplates.bold('Ví dụ:')}
{TelegramTemplates.code('my_ema_strategy | My EMA Strategy | Custom EMA-based LONG strategy with RSI filter')}

{TelegramTemplates.bold('Lưu ý:')}
• Strategy name: chỉ chữ, số, underscore
• Display name: tên hiển thị thân thiện
• Description: mô tả ngắn gọn về strategy

💡 Gửi {TelegramTemplates.code('/cancel')} để hủy"""

        return MessageTemplate(
            title="Strategy Basic Info",
            content=content,
            keyboard=None
        )

    @staticmethod
    def entry_conditions_setup() -> MessageTemplate:
        """Entry conditions setup"""
        content = f"""{TelegramTemplates.bold('🎯 Entry Conditions Setup')}

{TelegramTemplates.bold('📊 Configure when to open positions:')}

{TelegramTemplates.bold('LONG Entry Conditions:')}
• When should the bot open LONG positions?
• Combine multiple indicators for better accuracy

{TelegramTemplates.bold('SHORT Entry Conditions:')}
• When should the bot open SHORT positions?
• Set different conditions for SHORT trades

{TelegramTemplates.bold('Available Indicators:')}
📈 {TelegramTemplates.code('EMA')} - Exponential Moving Average (34, 89, 120)
📊 {TelegramTemplates.code('RSI')} - Relative Strength Index
📉 {TelegramTemplates.code('MACD')} - Moving Average Convergence Divergence
🎯 {TelegramTemplates.code('BB')} - Bollinger Bands (Upper, Middle, Lower)
📊 {TelegramTemplates.code('Volume')} - Trading Volume
📈 {TelegramTemplates.code('ATR')} - Average True Range
🔄 {TelegramTemplates.code('StochRSI')} - Stochastic RSI
💰 {TelegramTemplates.code('Price')} - Current Price

{TelegramTemplates.italic('Choose what to configure:')}"""

        keyboard = [
            [
                {"text": "📈 LONG Conditions", "callback_data": "entry_long"},
                {"text": "📉 SHORT Conditions", "callback_data": "entry_short"}
            ],
            [
                {"text": "👁️ Preview Conditions", "callback_data": "entry_preview"},
                {"text": "🔄 Reset All", "callback_data": "entry_reset"}
            ],
            [
                {"text": "⬅️ Back to Steps", "callback_data": "builder_steps"},
                {"text": "➡️ Next: DCA Settings", "callback_data": "step_dca"}
            ]
        ]

        return MessageTemplate(
            title="Entry Conditions Setup",
            content=content,
            keyboard=keyboard
        )

    @staticmethod
    def long_entry_conditions() -> MessageTemplate:
        """LONG entry conditions setup"""
        content = f"""{TelegramTemplates.bold('📈 LONG Entry Conditions')}

{TelegramTemplates.bold('🎯 When to open LONG positions:')}

{TelegramTemplates.bold('Current Conditions:')}
• No conditions set yet

{TelegramTemplates.bold('Add Condition:')}
Choose an indicator and set the condition for LONG entry.

{TelegramTemplates.bold('Example Conditions:')}
• {TelegramTemplates.code('EMA_34 > EMA_89')} - Uptrend confirmation
• {TelegramTemplates.code('RSI < 30')} - Oversold condition
• {TelegramTemplates.code('Price cross_above EMA_34')} - Breakout signal
• {TelegramTemplates.code('MACD > 0')} - Bullish momentum

{TelegramTemplates.italic('Select indicator to add condition:')}"""

        keyboard = [
            [
                {"text": "📈 EMA", "callback_data": "long_ema"},
                {"text": "📊 RSI", "callback_data": "long_rsi"},
                {"text": "📉 MACD", "callback_data": "long_macd"}
            ],
            [
                {"text": "🎯 Bollinger Bands", "callback_data": "long_bb"},
                {"text": "📊 Volume", "callback_data": "long_volume"},
                {"text": "📈 ATR", "callback_data": "long_atr"}
            ],
            [
                {"text": "🔄 Stoch RSI", "callback_data": "long_stochrsi"},
                {"text": "💰 Price", "callback_data": "long_price"}
            ],
            [
                {"text": "⬅️ Back", "callback_data": "step_entry"},
                {"text": "✅ Done", "callback_data": "entry_long_done"}
            ]
        ]

        return MessageTemplate(
            title="LONG Entry Conditions",
            content=content,
            keyboard=keyboard
        )

    @staticmethod
    def dca_settings_setup() -> MessageTemplate:
        """DCA settings setup"""
        content = f"""{TelegramTemplates.bold('📊 DCA Settings Setup')}

{TelegramTemplates.bold('💰 Dollar Cost Averaging Configuration:')}

DCA allows you to add to positions when conditions are favorable.

{TelegramTemplates.bold('DCA Triggers:')}
• When should the bot add to existing positions?
• Use different indicators than entry conditions

{TelegramTemplates.bold('DCA Amount:')}
• How much to add each time (% of initial position)
• Maximum number of DCA orders

{TelegramTemplates.bold('Current DCA Settings:')}
• No DCA triggers set yet
• DCA Amount: Not configured
• Max DCA Orders: Not configured

{TelegramTemplates.italic('Choose what to configure:')}"""

        keyboard = [
            [
                {"text": "🎯 DCA Triggers", "callback_data": "dca_triggers"},
                {"text": "💰 DCA Amount", "callback_data": "dca_amount"}
            ],
            [
                {"text": "🔢 Max DCA Orders", "callback_data": "dca_max"},
                {"text": "👁️ Preview DCA", "callback_data": "dca_preview"}
            ],
            [
                {"text": "⬅️ Back to Steps", "callback_data": "builder_steps"},
                {"text": "➡️ Next: Exit Settings", "callback_data": "step_exit"}
            ]
        ]

        return MessageTemplate(
            title="DCA Settings Setup",
            content=content,
            keyboard=keyboard
        )

    @staticmethod
    def exit_settings_setup() -> MessageTemplate:
        """Exit settings setup"""
        content = f"""{TelegramTemplates.bold('🎯 Exit Settings Setup')}

{TelegramTemplates.bold('💰 Take Profit & Stop Loss Configuration:')}

{TelegramTemplates.bold('Take Profit (TP):')}
• Fixed percentage: e.g., 3.0%
• Dynamic based on indicators: e.g., when RSI > 70
• Trailing stop: Follow price with distance

{TelegramTemplates.bold('Stop Loss (SL):')}
• Fixed percentage: e.g., 2.0%
• Dynamic based on indicators: e.g., when price < EMA_34
• ATR-based: Multiple of Average True Range

{TelegramTemplates.bold('Current Exit Settings:')}
• Take Profit: Not configured
• Stop Loss: Not configured
• Trailing Stop: Disabled

{TelegramTemplates.italic('Choose what to configure:')}"""

        keyboard = [
            [
                {"text": "📈 Take Profit", "callback_data": "exit_tp"},
                {"text": "📉 Stop Loss", "callback_data": "exit_sl"}
            ],
            [
                {"text": "🔄 Trailing Stop", "callback_data": "exit_trailing"},
                {"text": "👁️ Preview Exit", "callback_data": "exit_preview"}
            ],
            [
                {"text": "⬅️ Back to Steps", "callback_data": "builder_steps"},
                {"text": "➡️ Next: Risk Management", "callback_data": "step_risk"}
            ]
        ]

        return MessageTemplate(
            title="Exit Settings Setup",
            content=content,
            keyboard=keyboard
        )

    @staticmethod
    def indicator_condition_setup(indicator: str, direction: str = "LONG") -> MessageTemplate:
        """Indicator condition setup"""
        indicator_info = {
            "EMA": {
                "name": "Exponential Moving Average",
                "options": ["EMA_34", "EMA_89", "EMA_120"],
                "operators": [">", "<", ">=", "<=", "cross_above", "cross_below"],
                "examples": ["EMA_34 > EMA_89", "Price cross_above EMA_34"]
            },
            "RSI": {
                "name": "Relative Strength Index",
                "options": ["RSI"],
                "operators": [">", "<", ">=", "<="],
                "examples": ["RSI < 30", "RSI > 70"]
            },
            "MACD": {
                "name": "MACD",
                "options": ["MACD", "MACD_Signal", "MACD_Histogram"],
                "operators": [">", "<", ">=", "<=", "cross_above", "cross_below"],
                "examples": ["MACD > 0", "MACD cross_above MACD_Signal"]
            },
            "BB": {
                "name": "Bollinger Bands",
                "options": ["BB_Upper", "BB_Middle", "BB_Lower"],
                "operators": [">", "<", ">=", "<=", "cross_above", "cross_below"],
                "examples": ["Price < BB_Lower", "Price cross_above BB_Upper"]
            },
            "Volume": {
                "name": "Trading Volume",
                "options": ["Volume", "Volume_SMA"],
                "operators": [">", "<", ">=", "<="],
                "examples": ["Volume > Volume_SMA * 1.5"]
            },
            "Price": {
                "name": "Current Price",
                "options": ["Price"],
                "operators": [">", "<", ">=", "<=", "cross_above", "cross_below"],
                "examples": ["Price > EMA_34", "Price cross_below EMA_89"]
            }
        }

        info = indicator_info.get(indicator, indicator_info["EMA"])

        content = f"""{TelegramTemplates.bold(f'📊 {info["name"]} Condition')}

{TelegramTemplates.bold(f'🎯 Setting {direction} condition for {indicator}:')}

{TelegramTemplates.bold('Available Options:')}"""

        for option in info["options"]:
            content += f"\n• {TelegramTemplates.code(option)}"

        content += f"""

{TelegramTemplates.bold('Operators:')}"""

        for op in info["operators"]:
            content += f"\n• {TelegramTemplates.code(op)}"

        content += f"""

{TelegramTemplates.bold('Examples:')}"""

        for example in info["examples"]:
            content += f"\n• {TelegramTemplates.code(example)}"

        content += f"""

{TelegramTemplates.italic('Send your condition in format:')}
{TelegramTemplates.code('indicator operator value')}

{TelegramTemplates.italic('Example:')} {TelegramTemplates.code('RSI < 30')}"""

        keyboard = [
            [
                {"text": "📝 Quick Examples", "callback_data": f"{indicator.lower()}_examples"},
                {"text": "❓ Help", "callback_data": f"{indicator.lower()}_help"}
            ],
            [
                {"text": "⬅️ Back", "callback_data": f"entry_{direction.lower()}"},
                {"text": "❌ Cancel", "callback_data": "step_entry"}
            ]
        ]

        return MessageTemplate(
            title=f"{indicator} Condition Setup",
            content=content,
            keyboard=keyboard
        )

    @staticmethod
    def take_profit_setup() -> MessageTemplate:
        """Take profit setup"""
        content = f"""{TelegramTemplates.bold('📈 Take Profit Setup')}

{TelegramTemplates.bold('💰 Configure when to take profits:')}

{TelegramTemplates.bold('1. Fixed Percentage:')}
• Simple percentage gain: e.g., 3.0%
• Best for: Consistent profit taking

{TelegramTemplates.bold('2. Dynamic (Indicator-based):')}
• Based on technical indicators
• Examples: RSI > 70, Price > BB_Upper
• Best for: Market condition adaptation

{TelegramTemplates.bold('3. Trailing Stop:')}
• Follow price with fixed distance
• Lock in profits as price moves favorably
• Best for: Trend following

{TelegramTemplates.italic('Choose Take Profit type:')}"""

        keyboard = [
            [
                {"text": "📊 Fixed %", "callback_data": "tp_fixed"},
                {"text": "📈 Dynamic", "callback_data": "tp_dynamic"}
            ],
            [
                {"text": "🔄 Trailing", "callback_data": "tp_trailing"},
                {"text": "🔀 Combination", "callback_data": "tp_combo"}
            ],
            [
                {"text": "⬅️ Back", "callback_data": "step_exit"},
                {"text": "❌ Cancel", "callback_data": "builder_steps"}
            ]
        ]

        return MessageTemplate(
            title="Take Profit Setup",
            content=content,
            keyboard=keyboard
        )

    @staticmethod
    def stop_loss_setup() -> MessageTemplate:
        """Stop loss setup"""
        content = f"""{TelegramTemplates.bold('📉 Stop Loss Setup')}

{TelegramTemplates.bold('🛡️ Configure when to cut losses:')}

{TelegramTemplates.bold('1. Fixed Percentage:')}
• Simple percentage loss: e.g., 2.0%
• Best for: Risk management

{TelegramTemplates.bold('2. Dynamic (Indicator-based):')}
• Based on technical indicators
• Examples: Price < EMA_34, RSI < 20
• Best for: Market condition adaptation

{TelegramTemplates.bold('3. ATR-based:')}
• Multiple of Average True Range
• Adapts to market volatility
• Best for: Volatile markets

{TelegramTemplates.italic('Choose Stop Loss type:')}"""

        keyboard = [
            [
                {"text": "📊 Fixed %", "callback_data": "sl_fixed"},
                {"text": "📈 Dynamic", "callback_data": "sl_dynamic"}
            ],
            [
                {"text": "📊 ATR-based", "callback_data": "sl_atr"},
                {"text": "🔀 Combination", "callback_data": "sl_combo"}
            ],
            [
                {"text": "⬅️ Back", "callback_data": "step_exit"},
                {"text": "❌ Cancel", "callback_data": "builder_steps"}
            ]
        ]

        return MessageTemplate(
            title="Stop Loss Setup",
            content=content,
            keyboard=keyboard
        )

    @staticmethod
    def indicator_selection(step: str = "entry") -> MessageTemplate:
        """Indicator selection interface"""
        step_name = {
            "entry": "Entry Conditions",
            "dca": "DCA Triggers",
            "exit": "Exit Conditions"
        }.get(step, "Conditions")

        content = f"""{TelegramTemplates.bold(f'📊 Technical Indicators - {step_name}')}

Chọn technical indicators để sử dụng:

{TelegramTemplates.bold('Trend Indicators:')}
• EMA 34, 89, 120 - Moving averages
• Price - Current market price

{TelegramTemplates.bold('Momentum Indicators:')}
• RSI - Relative Strength Index
• MACD - Moving Average Convergence Divergence
• Stochastic RSI - Stochastic RSI

{TelegramTemplates.bold('Volatility Indicators:')}
• Bollinger Bands (Upper, Middle, Lower)
• ATR - Average True Range

{TelegramTemplates.bold('Volume Indicators:')}
• Volume - Trading volume

Chọn indicator để thêm condition:"""

        keyboard = [
            [
                {"text": "📈 EMA 34", "callback_data": f"indicator_{step}_ema_34"},
                {"text": "📈 EMA 89", "callback_data": f"indicator_{step}_ema_89"},
                {"text": "📈 EMA 120", "callback_data": f"indicator_{step}_ema_120"}
            ],
            [
                {"text": "💰 Price", "callback_data": f"indicator_{step}_price"},
                {"text": "📊 RSI", "callback_data": f"indicator_{step}_rsi"}
            ],
            [
                {"text": "📈 MACD", "callback_data": f"indicator_{step}_macd"},
                {"text": "📊 Stoch RSI", "callback_data": f"indicator_{step}_stoch_rsi"}
            ],
            [
                {"text": "🔵 BB Upper", "callback_data": f"indicator_{step}_bb_upper"},
                {"text": "🔵 BB Middle", "callback_data": f"indicator_{step}_bb_middle"},
                {"text": "🔵 BB Lower", "callback_data": f"indicator_{step}_bb_lower"}
            ],
            [
                {"text": "📊 Volume", "callback_data": f"indicator_{step}_volume"},
                {"text": "📊 ATR", "callback_data": f"indicator_{step}_atr"}
            ],
            [
                {"text": "🔙 Back", "callback_data": f"builder_{step}_back"},
                {"text": "❌ Cancel", "callback_data": "close"}
            ]
        ]

        return MessageTemplate(
            title=f"Indicator Selection - {step_name}",
            content=content,
            keyboard=keyboard
        )

    @staticmethod
    def condition_setup(indicator: str, step: str = "entry") -> MessageTemplate:
        """Condition setup interface"""
        indicator_display = {
            "ema_34": "EMA 34",
            "ema_89": "EMA 89",
            "ema_120": "EMA 120",
            "price": "Price",
            "rsi": "RSI",
            "macd": "MACD",
            "bb_upper": "Bollinger Upper",
            "bb_middle": "Bollinger Middle",
            "bb_lower": "Bollinger Lower",
            "volume": "Volume",
            "atr": "ATR",
            "stoch_rsi": "Stochastic RSI"
        }.get(indicator, indicator.upper())

        content = f"""{TelegramTemplates.bold(f'⚙️ Setup Condition - {indicator_display}')}

Thiết lập điều kiện cho {TelegramTemplates.bold(indicator_display)}:

{TelegramTemplates.bold('Comparison Operators:')}
• {TelegramTemplates.code('>')} Greater than
• {TelegramTemplates.code('<')} Less than
• {TelegramTemplates.code('>=')} Greater than or equal
• {TelegramTemplates.code('<=')} Less than or equal
• {TelegramTemplates.code('==')} Equal to
• {TelegramTemplates.code('cross_above')} Cross above
• {TelegramTemplates.code('cross_below')} Cross below

Chọn comparison operator:"""

        keyboard = [
            [
                {"text": "> Greater", "callback_data": f"operator_{step}_{indicator}_>"},
                {"text": "< Less", "callback_data": f"operator_{step}_{indicator}_<"}
            ],
            [
                {"text": ">= Greater Equal", "callback_data": f"operator_{step}_{indicator}_>="},
                {"text": "<= Less Equal", "callback_data": f"operator_{step}_{indicator}_<="}
            ],
            [
                {"text": "== Equal", "callback_data": f"operator_{step}_{indicator}_=="}
            ],
            [
                {"text": "📈 Cross Above", "callback_data": f"operator_{step}_{indicator}_cross_above"},
                {"text": "📉 Cross Below", "callback_data": f"operator_{step}_{indicator}_cross_below"}
            ],
            [
                {"text": "🔙 Back", "callback_data": f"indicator_select_{step}"},
                {"text": "❌ Cancel", "callback_data": "close"}
            ]
        ]

        return MessageTemplate(
            title=f"Condition Setup - {indicator_display}",
            content=content,
            keyboard=keyboard
        )

    @staticmethod
    def value_input_prompt(indicator: str, operator: str, step: str = "entry") -> MessageTemplate:
        """Value input prompt"""
        indicator_display = {
            "ema_34": "EMA 34",
            "ema_89": "EMA 89",
            "ema_120": "EMA 120",
            "price": "Price",
            "rsi": "RSI",
            "macd": "MACD",
            "bb_upper": "Bollinger Upper",
            "bb_middle": "Bollinger Middle",
            "bb_lower": "Bollinger Lower",
            "volume": "Volume",
            "atr": "ATR",
            "stoch_rsi": "Stochastic RSI"
        }.get(indicator, indicator.upper())

        operator_display = {
            ">": "greater than",
            "<": "less than",
            ">=": "greater than or equal to",
            "<=": "less than or equal to",
            "==": "equal to",
            "cross_above": "crosses above",
            "cross_below": "crosses below"
        }.get(operator, operator)

        content = f"""{TelegramTemplates.bold(f'💯 Set Value - {indicator_display}')}

Condition: {TelegramTemplates.bold(f'{indicator_display} {operator_display} ?')}

{TelegramTemplates.bold('Nhập giá trị:')}

{TelegramTemplates.bold('Options:')}
• {TelegramTemplates.code('Number')} - Specific value (e.g., 50, 70, 0.5)
• {TelegramTemplates.code('Indicator')} - Another indicator (e.g., ema_89, price)

{TelegramTemplates.bold('Examples:')}
• {TelegramTemplates.code('70')} - RSI greater than 70
• {TelegramTemplates.code('ema_120')} - EMA 89 greater than EMA 120
• {TelegramTemplates.code('0.001')} - MACD greater than 0.001

💡 Gửi {TelegramTemplates.code('/cancel')} để hủy"""

        return MessageTemplate(
            title=f"Value Input - {indicator_display}",
            content=content,
            keyboard=None
        )

    @staticmethod
    def strategy_summary(strategy: CustomStrategy) -> MessageTemplate:
        """Strategy summary and confirmation"""
        content = f"""{TelegramTemplates.bold('📋 Strategy Summary')}

{TelegramTemplates.bold('Basic Info:')}
• Name: {TelegramTemplates.code(strategy.name)}
• Display: {TelegramTemplates.bold(strategy.display_name)}
• Description: {strategy.description}

{TelegramTemplates.bold('Entry Conditions:')}"""

        # Add LONG conditions
        if strategy.entry_logic.long_conditions:
            content += f"\n📈 {TelegramTemplates.bold('LONG Entry:')} {len(strategy.entry_logic.long_conditions)} condition group(s)"
            for i, group in enumerate(strategy.entry_logic.long_conditions, 1):
                content += f"\n  {i}. {group.description} ({len(group.conditions)} conditions)"

        # Add SHORT conditions
        if strategy.entry_logic.short_conditions:
            content += f"\n📉 {TelegramTemplates.bold('SHORT Entry:')} {len(strategy.entry_logic.short_conditions)} condition group(s)"
            for i, group in enumerate(strategy.entry_logic.short_conditions, 1):
                content += f"\n  {i}. {group.description} ({len(group.conditions)} conditions)"

        # Exit settings
        content += f"""

{TelegramTemplates.bold('Exit Settings:')}
• Take Profit: {strategy.exit_logic.take_profit_percent}%
• Stop Loss: {strategy.exit_logic.stop_loss_percent}%
• Dynamic TP/SL: {'✅' if strategy.exit_logic.dynamic_tp_sl else '❌'}"""

        # DCA settings
        if strategy.dca_logic.enabled:
            content += f"""

{TelegramTemplates.bold('DCA Settings:')}
• Enabled: ✅
• Max Levels: {strategy.dca_logic.max_dca_levels}
• Volume Multiplier: {strategy.dca_logic.volume_multiplier}x
• Triggers: {len(strategy.dca_logic.triggers)} condition group(s)"""
        else:
            content += f"\n\n{TelegramTemplates.bold('DCA:')} ❌ Disabled"

        # Risk management
        content += f"""

{TelegramTemplates.bold('Risk Management:')}
• Max Position: ${strategy.risk_management.max_position_size}
• Risk per Trade: {strategy.risk_management.risk_per_trade}%
• Daily Loss Limit: {strategy.risk_management.daily_loss_limit}%
• Cooldown: {strategy.risk_management.cooldown_minutes} minutes"""

        keyboard = [
            [
                {"text": "✅ Save Strategy", "callback_data": "builder_save_confirm"},
                {"text": "✏️ Edit", "callback_data": "builder_edit"}
            ],
            [
                {"text": "🧪 Test Strategy", "callback_data": "builder_test"},
                {"text": "❌ Cancel", "callback_data": "builder_cancel"}
            ]
        ]

        return MessageTemplate(
            title="Strategy Summary",
            content=content,
            keyboard=keyboard
        )

    @staticmethod
    def strategy_saved_success(strategy_name: str) -> MessageTemplate:
        """Strategy saved successfully"""
        content = f"""{TelegramTemplates.bold('✅ Strategy Saved Successfully!')}

Strategy {TelegramTemplates.bold(strategy_name)} đã được lưu thành công!

{TelegramTemplates.bold('Next Steps:')}
• 🤖 Tạo bot với strategy này: {TelegramTemplates.code('/createbot')}
• 📋 Xem tất cả strategies: {TelegramTemplates.code('/mystrategies')}
• ✏️ Chỉnh sửa strategy: {TelegramTemplates.code('/editstrategy')}
• 🧪 Test strategy: {TelegramTemplates.code('/teststrategy')}

💡 {TelegramTemplates.italic('Strategy có thể được sử dụng ngay trong bot creation wizard!')}"""

        keyboard = [
            [
                {"text": "🤖 Create Bot", "callback_data": "bot_create"},
                {"text": "📋 My Strategies", "callback_data": "my_strategies"}
            ],
            [
                {"text": "🔧 Create Another", "callback_data": "builder_welcome"},
                {"text": "❌ Close", "callback_data": "close"}
            ]
        ]

        return MessageTemplate(
            title="Strategy Saved",
            content=content,
            keyboard=keyboard
        )

    @staticmethod
    def my_strategies_list(strategies: List[Dict[str, Any]]) -> MessageTemplate:
        """List user's custom strategies"""
        if not strategies:
            content = f"""{TelegramTemplates.bold('📋 My Custom Strategies')}

{TelegramTemplates.italic('Bạn chưa có custom strategy nào.')}

Tạo strategy đầu tiên với {TelegramTemplates.code('/createstrategy')}!

{TelegramTemplates.bold('Benefits của Custom Strategies:')}
• 🎯 Tùy chỉnh hoàn toàn theo phong cách trading
• 📊 Sử dụng technical indicators yêu thích
• ⚖️ Kiểm soát risk management chi tiết
• 🔄 Tái sử dụng cho nhiều bots khác nhau"""

            keyboard = [
                [
                    {"text": "🔧 Create Strategy", "callback_data": "builder_welcome"}
                ],
                [
                    {"text": "❌ Close", "callback_data": "close"}
                ]
            ]
        else:
            content = f"""{TelegramTemplates.bold('📋 My Custom Strategies')}

{TelegramTemplates.bold(f'Total: {len(strategies)} strategies')}

"""

            keyboard = []
            for i, strategy in enumerate(strategies[:10], 1):  # Show max 10
                usage_text = f" ({strategy['usage_count']} uses)" if strategy['usage_count'] > 0 else ""
                content += f"{i}. {TelegramTemplates.bold(strategy['display_name'])}{usage_text}\n"
                content += f"   {strategy['description'][:50]}{'...' if len(strategy['description']) > 50 else ''}\n"
                updated_date = strategy["updated_at"][:10]
                content += f"   {TelegramTemplates.italic(f'Updated: {updated_date}')} \n\n"

                keyboard.append([
                    {"text": f"📊 {strategy['display_name'][:20]}", "callback_data": f"strategy_view_{strategy['name']}"},
                    {"text": "✏️ Edit", "callback_data": f"strategy_edit_{strategy['name']}"},
                    {"text": "🗑️ Delete", "callback_data": f"strategy_delete_{strategy['name']}"}
                ])

            if len(strategies) > 10:
                content += f"\n{TelegramTemplates.italic(f'... and {len(strategies) - 10} more strategies')}"

            keyboard.extend([
                [
                    {"text": "🔧 Create New", "callback_data": "builder_welcome"},
                    {"text": "🔍 Search", "callback_data": "strategy_search"}
                ],
                [
                    {"text": "❌ Close", "callback_data": "close"}
                ]
            ])

        return MessageTemplate(
            title="My Strategies",
            content=content,
            keyboard=keyboard
        )

    @staticmethod
    def strategy_detail_view(strategy: CustomStrategy) -> MessageTemplate:
        """Detailed view of a strategy"""
        content = strategy.get_summary()

        # Add technical details
        content += f"""

{TelegramTemplates.bold('📊 Technical Details:')}"""

        # LONG conditions detail
        if strategy.entry_logic.long_conditions:
            content += f"\n\n{TelegramTemplates.bold('📈 LONG Entry Conditions:')}"
            for i, group in enumerate(strategy.entry_logic.long_conditions, 1):
                content += f"\n{i}. {TelegramTemplates.bold(group.description)}"
                for j, condition in enumerate(group.conditions, 1):
                    value_str = condition.value.value if hasattr(condition.value, 'value') else str(condition.value)
                    content += f"\n   {j}. {condition.indicator.value} {condition.operator.value} {value_str}"
                if len(group.conditions) > 1:
                    content += f"\n   Logic: {group.logical_operator.value.upper()}"

        # SHORT conditions detail
        if strategy.entry_logic.short_conditions:
            content += f"\n\n{TelegramTemplates.bold('📉 SHORT Entry Conditions:')}"
            for i, group in enumerate(strategy.entry_logic.short_conditions, 1):
                content += f"\n{i}. {TelegramTemplates.bold(group.description)}"
                for j, condition in enumerate(group.conditions, 1):
                    value_str = condition.value.value if hasattr(condition.value, 'value') else str(condition.value)
                    content += f"\n   {j}. {condition.indicator.value} {condition.operator.value} {value_str}"
                if len(group.conditions) > 1:
                    content += f"\n   Logic: {group.logical_operator.value.upper()}"

        # DCA conditions detail
        if strategy.dca_logic.enabled and strategy.dca_logic.triggers:
            content += f"\n\n{TelegramTemplates.bold('💰 DCA Triggers:')}"
            for i, group in enumerate(strategy.dca_logic.triggers, 1):
                content += f"\n{i}. {TelegramTemplates.bold(group.description)}"
                for j, condition in enumerate(group.conditions, 1):
                    value_str = condition.value.value if hasattr(condition.value, 'value') else str(condition.value)
                    content += f"\n   {j}. {condition.indicator.value} {condition.operator.value} {value_str}"

        # Metadata
        content += f"""

{TelegramTemplates.bold('📝 Metadata:')}
• Created: {strategy.created_at.strftime('%Y-%m-%d %H:%M')}
• Updated: {strategy.updated_at.strftime('%Y-%m-%d %H:%M')}
• Version: {strategy.version}
• Usage Count: {strategy.usage_count}"""

        if strategy.tags:
            content += f"\n• Tags: {', '.join(strategy.tags)}"

        keyboard = [
            [
                {"text": "🤖 Use in Bot", "callback_data": f"strategy_use_{strategy.name}"},
                {"text": "✏️ Edit", "callback_data": f"strategy_edit_{strategy.name}"}
            ],
            [
                {"text": "📋 Duplicate", "callback_data": f"strategy_duplicate_{strategy.name}"},
                {"text": "📤 Export", "callback_data": f"strategy_export_{strategy.name}"}
            ],
            [
                {"text": "🧪 Test", "callback_data": f"strategy_test_{strategy.name}"},
                {"text": "🗑️ Delete", "callback_data": f"strategy_delete_{strategy.name}"}
            ],
            [
                {"text": "🔙 Back to List", "callback_data": "my_strategies"},
                {"text": "❌ Close", "callback_data": "close"}
            ]
        ]

        return MessageTemplate(
            title=f"Strategy: {strategy.display_name}",
            content=content,
            keyboard=keyboard
        )