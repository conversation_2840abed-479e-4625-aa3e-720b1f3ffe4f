"""Telegram API client for sending and editing messages"""
import json
import logging
import asyncio
import aiohttp
from typing import Dict, List, Optional, Any, Union
from telegram.constants import ParseMode
from telegram import InlineKeyboardMarkup, InlineKeyboardButton

from src.infrastructure.telegram.templates import MessageTemplate, TelegramTemplates


class TelegramAPIClient:
    """Telegram API client with HTML formatting support"""

    def __init__(self, bot_token: str):
        self.bot_token = bot_token
        self.base_url = f"https://api.telegram.org/bot{bot_token}"
        self.logger = logging.getLogger(self.__class__.__name__)
        self.session = None

    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()

    async def _make_request(self, method: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Make HTTP request to Telegram API"""
        url = f"{self.base_url}/{method}"

        if not self.session:
            self.session = aiohttp.ClientSession()

        try:
            async with self.session.post(url, json=data) as response:
                result = await response.json()

                if not result.get('ok'):
                    error_desc = result.get('description', 'Unknown error')
                    self.logger.error(f"Telegram API error: {error_desc}")
                    raise Exception(f"Telegram API error: {error_desc}")

                return result

        except aiohttp.ClientError as e:
            self.logger.error(f"HTTP request failed: {e}")
            raise Exception(f"HTTP request failed: {e}")

    def _sanitize_html_text(self, text: str) -> str:
        """Sanitize HTML text while preserving intended formatting tags"""
        # Escape HTML special characters first
        clean_text = text.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')

        # Restore our intended HTML tags
        html_tags = {
            '&lt;b&gt;': '<b>',
            '&lt;/b&gt;': '</b>',
            '&lt;i&gt;': '<i>',
            '&lt;/i&gt;': '</i>',
            '&lt;code&gt;': '<code>',
            '&lt;/code&gt;': '</code>',
            '&lt;pre&gt;': '<pre>',
            '&lt;/pre&gt;': '</pre>',
            '&lt;u&gt;': '<u>',
            '&lt;/u&gt;': '</u>',
            '&lt;s&gt;': '<s>',
            '&lt;/s&gt;': '</s>',
        }

        for escaped, tag in html_tags.items():
            clean_text = clean_text.replace(escaped, tag)

        return clean_text

    def _create_keyboard_markup(self, keyboard: List[List[Dict[str, str]]]) -> Dict[str, Any]:
        """Create inline keyboard markup from keyboard definition"""
        if not keyboard:
            return None

        inline_keyboard = []
        for row in keyboard:
            button_row = []
            for button in row:
                button_data = {"text": button["text"]}

                if "callback_data" in button:
                    button_data["callback_data"] = button["callback_data"]
                elif "url" in button:
                    button_data["url"] = button["url"]

                button_row.append(button_data)
            inline_keyboard.append(button_row)

        return {"inline_keyboard": inline_keyboard}

    async def send_message(self,
                          chat_id: str,
                          text: str = "",
                          parse_mode: str = "HTML",
                          keyboard: List[List[Dict[str, str]]] = None,
                          disable_notification: bool = False) -> Dict[str, Any]:
        """Send message with HTML formatting support"""

        if not chat_id:
            raise ValueError("chat_id is required")

        # Sanitize text for HTML parsing
        clean_text = self._sanitize_html_text(text) if parse_mode == "HTML" else text

        data = {
            "chat_id": chat_id,
            "text": clean_text,
            "parse_mode": parse_mode,
            "disable_notification": disable_notification
        }

        # Add inline keyboard if provided
        reply_markup = self._create_keyboard_markup(keyboard)
        if reply_markup:
            data["reply_markup"] = reply_markup

        try:
            result = await self._make_request("sendMessage", data)
            self.logger.info(f"Message sent successfully to {chat_id}")
            return result

        except Exception as e:
            # Fallback to plain text if HTML parsing fails
            if parse_mode == "HTML":
                self.logger.warning(f"HTML parsing failed, retrying with plain text: {e}")
                # Remove HTML tags and use plain text (no parse_mode)
                plain_text = TelegramTemplates.strip_html_tags(text)
                return await self.send_message(chat_id, plain_text, None, keyboard, disable_notification)
            else:
                self.logger.error(f"Failed to send message: {e}")
                raise

    async def edit_message(self,
                          chat_id: str,
                          message_id: int,
                          text: str = "",
                          parse_mode: str = "HTML",
                          keyboard: List[List[Dict[str, str]]] = None) -> Dict[str, Any]:
        """Edit message with HTML formatting support"""

        if not chat_id or not message_id:
            raise ValueError("chat_id and message_id are required")

        # Sanitize text for HTML parsing
        clean_text = self._sanitize_html_text(text) if parse_mode == "HTML" else text

        data = {
            "chat_id": chat_id,
            "message_id": message_id,
            "text": clean_text,
            "parse_mode": parse_mode
        }

        # Add inline keyboard if provided
        reply_markup = self._create_keyboard_markup(keyboard)
        if reply_markup:
            data["reply_markup"] = reply_markup

        try:
            result = await self._make_request("editMessageText", data)
            self.logger.info(f"Message {message_id} edited successfully")
            return result

        except Exception as e:
            # Fallback to plain text if HTML parsing fails
            if parse_mode == "HTML":
                self.logger.warning(f"HTML edit failed, retrying with plain text: {e}")
                # Remove HTML tags and use plain text (no parse_mode)
                plain_text = TelegramTemplates.strip_html_tags(text)
                return await self.edit_message(chat_id, message_id, plain_text, None, keyboard)
            else:
                self.logger.error(f"Failed to edit message: {e}")
                raise

    async def send_template(self,
                           template: MessageTemplate,
                           chat_id: str,
                           disable_notification: bool = False) -> Dict[str, Any]:
        """Send message using template"""
        return await self.send_message(
            chat_id=chat_id,
            text=template.content,
            parse_mode=template.parse_mode,
            keyboard=template.keyboard,
            disable_notification=disable_notification
        )

    async def edit_template(self,
                           template: MessageTemplate,
                           chat_id: str = None,
                           message_id: int = None) -> Dict[str, Any]:
        """Edit message using template"""
        return await self.edit_message(
            chat_id=chat_id,
            message_id=message_id,
            text=template.content,
            parse_mode=template.parse_mode,
            keyboard=template.keyboard
        )

    async def answer_callback_query(self,
                                  callback_query_id: str,
                                  text: str = None,
                                  show_alert: bool = False) -> Dict[str, Any]:
        """Answer callback query"""
        data = {"callback_query_id": callback_query_id}

        if text:
            data["text"] = text
        if show_alert:
            data["show_alert"] = show_alert

        return await self._make_request("answerCallbackQuery", data)

    async def delete_message(self,
                           chat_id: str = None,
                           message_id: int = None) -> Dict[str, Any]:
        """Delete message"""
        if chat_id is None:
            chat_id = self.default_chat_id

        if not chat_id or not message_id:
            raise ValueError("chat_id and message_id are required")
        data = {
            "chat_id": chat_id,
            "message_id": message_id
        }

        return await self._make_request("deleteMessage", data)

    async def get_chat(self, chat_id: str = None) -> Dict[str, Any]:
        """Get chat information"""
        if chat_id is None:
            chat_id = self.default_chat_id

        if not chat_id:
            raise ValueError("chat_id is required")

        data = {"chat_id": chat_id}
        return await self._make_request("getChat", data)

    async def get_updates(self,
                         offset: int = None,
                         limit: int = 100,
                         timeout: int = 0) -> Dict[str, Any]:
        """Get updates"""
        data = {"limit": limit, "timeout": timeout}

        if offset:
            data["offset"] = offset

        return await self._make_request("getUpdates", data)


class TelegramMessageSender:
    """Higher-level interface for sending formatted messages"""

    def __init__(self, api_client: TelegramAPIClient):
        self.api_client = api_client
        self.logger = logging.getLogger(self.__class__.__name__)

    async def send_error(self, error: str, command: str = "", chat_id: str = None) -> Dict[str, Any]:
        """Send error message"""
        if not chat_id:
            raise ValueError("chat_id is required")
        message = TelegramTemplates.error_message(error, command)
        return await self.api_client.send_message(chat_id, message)

    async def send_success(self, message: str, details: str = "", chat_id: str = None) -> Dict[str, Any]:
        """Send success message"""
        if not chat_id:
            raise ValueError("chat_id is required")
        content = TelegramTemplates.success_message(message, details)
        return await self.api_client.send_message(chat_id, content)

    async def send_info(self, title: str, content: str, chat_id: str = None) -> Dict[str, Any]:
        """Send info message"""
        if not chat_id:
            raise ValueError("chat_id is required")
        message = TelegramTemplates.info_message(title, content)
        return await self.api_client.send_message(chat_id, message)

    async def send_warning(self, message: str, chat_id: str = None) -> Dict[str, Any]:
        """Send warning message"""
        if not chat_id:
            raise ValueError("chat_id is required")
        content = TelegramTemplates.warning_message(message)
        return await self.api_client.send_message(chat_id, content)

    async def send_credentials_list(self, profiles: List[Dict[str, Any]], chat_id: str) -> Dict[str, Any]:
        """Send credentials list"""
        template = TelegramTemplates.credentials_list(profiles)
        return await self.api_client.send_template(template, chat_id)

    async def send_bot_list(self, containers: List[Dict[str, Any]], chat_id: str) -> Dict[str, Any]:
        """Send bot list"""
        template = TelegramTemplates.bot_list(containers)
        return await self.api_client.send_template(template, chat_id)

    async def send_bot_status(self, container_name: str, status_info: Dict[str, Any], chat_id: str) -> Dict[str, Any]:
        """Send bot status"""
        template = TelegramTemplates.bot_status(container_name, status_info)
        return await self.api_client.send_template(template, chat_id)

    async def send_bot_logs(self, container_name: str, logs: str, chat_id: str) -> Dict[str, Any]:
        """Send bot logs"""
        template = TelegramTemplates.bot_logs(container_name, logs)
        return await self.api_client.send_template(template, chat_id)

    async def send_help(self, chat_id: str) -> Dict[str, Any]:
        """Send help message"""
        template = TelegramTemplates.help_main()
        return await self.api_client.send_template(template, chat_id)

    async def send_getting_started(self, chat_id: str) -> Dict[str, Any]:
        """Send getting started guide"""
        template = TelegramTemplates.help_getting_started()
        return await self.api_client.send_template(template, chat_id)

    async def send_wizard_step(self, wizard_type: str, step: int, data: Dict[str, Any] = None, chat_id: str = None) -> Dict[str, Any]:
        """Send wizard step message"""
        template = None

        if wizard_type == "add_credentials":
            if step == 1:
                template = TelegramTemplates.wizard_add_credentials_step1()
            elif step == 2:
                template = TelegramTemplates.wizard_add_credentials_step2(data.get('profile_name', ''))
            elif step == 3:
                template = TelegramTemplates.wizard_add_credentials_step3(data.get('profile_name', ''))
            elif step == 4:
                template = TelegramTemplates.wizard_add_credentials_step4(
                    data.get('profile_name', ''),
                    data.get('display_name', '')
                )

        elif wizard_type == "create_bot":
            if step == 1:
                template = TelegramTemplates.wizard_create_bot_step1()
            elif step == 2:
                template = TelegramTemplates.wizard_create_bot_step2()
            elif step == 3:
                template = TelegramTemplates.wizard_create_bot_step3()
            elif step == 4:
                template = TelegramTemplates.wizard_create_bot_step4(data or {})

        if template:
            return await self.api_client.send_template(template, chat_id)
        else:
            raise ValueError(f"Unknown wizard step: {wizard_type} step {step}")


class TelegramResponseFormatter:
    """Format responses from bot.sh commands for Telegram"""

    @staticmethod
    def format_command_response(returncode: int, stdout: str, stderr: str, command: str = "") -> str:
        """Format command response for Telegram"""
        if returncode == 0:
            if stdout:
                # Truncate very long output
                display_stdout = stdout[:2000] + "...\n\n(Output truncated)" if len(stdout) > 2000 else stdout
                return TelegramTemplates.success_message("Command completed successfully", display_stdout)
            else:
                return "✅ Command completed successfully"
        else:
            error_msg = stderr if stderr else stdout
            if not error_msg:
                error_msg = f"Command failed with exit code {returncode}"

            # Add troubleshooting info for common issues
            if "docker" in error_msg.lower():
                error_msg += "\n\n💡 <b>Docker Issue?</b>\n• Check if Docker is running\n• Verify image is available\n• Check Docker permissions"

            if any(word in error_msg.lower() for word in ["credentials", "api"]):
                error_msg += "\n\n🔐 <b>Credentials Issue?</b>\n• Use /addcreds to setup API keys\n• Use /loadcreds to activate them\n• Check API key permissions"

            return TelegramTemplates.error_message(error_msg, command)

    @staticmethod
    def format_credentials_list(output: str) -> List[Dict[str, Any]]:
        """Parse and format credentials list output from bot.sh"""
        profiles = []

        # Parse bot.sh output for credentials
        lines = output.strip().split('\n')
        for line in lines:
            line = line.strip()
            if line and not line.startswith('✅') and not line.startswith('📋') and '=' not in line:
                # Simple parsing - assume format: profile_name (display_name)
                if '(' in line and ')' in line:
                    name = line.split('(')[0].strip()
                    display_name = line.split('(')[1].split(')')[0].strip()
                else:
                    name = line
                    display_name = line

                profiles.append({
                    'name': name,
                    'display_name': display_name,
                    'created_at': 'Unknown',
                    'usage_count': 0,
                    'security_level': 'Encrypted',
                    'encrypted': True
                })

        return profiles

    @staticmethod
    def format_docker_status(containers: List[Dict[str, str]]) -> List[Dict[str, Any]]:
        """Format container list for Telegram display"""
        formatted = []

        for container in containers:
            formatted.append({
                'name': container.get('name', 'Unknown'),
                'status': container.get('status', 'Unknown'),
                'image': container.get('image', 'Unknown'),
                'created': container.get('created', 'Unknown'),
                'uptime': 'N/A',  # Could calculate from created time
                'health': 'Unknown'
            })

        return formatted
