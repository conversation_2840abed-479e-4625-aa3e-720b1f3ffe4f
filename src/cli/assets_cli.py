#!/usr/bin/env python3
"""
Assets CLI - Command line interface for viewing account assets
"""

import asyncio
import argparse
import sys
import os
import json
from typing import Dict, Any, Optional

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from core.credential_utils import load_credentials, list_profiles
from infrastructure.exchange.exchange_connector import ExchangeConnector
from core.models.trade_config import TradeConfig


class AssetsCliError(Exception):
    """Custom exception for Assets CLI errors"""
    pass


class AssetsCli:
    """CLI for viewing account assets"""
    
    def __init__(self):
        self.exchange = None
    
    async def initialize_exchange(self, profile: str) -> bool:
        """Initialize exchange connection with credentials"""
        try:
            # Load credentials
            cred_data = load_credentials(profile)
            if not cred_data:
                print(f"❌ Profile '{profile}' not found")
                return False
            
            # Create minimal config for exchange connection
            config = TradeConfig(
                symbol='BTC/USDT:USDT',  # Dummy symbol, not used for balance
                exchange='bybit',
                direction='LONG',
                amount=50,
                use_test_mode=False,  # Use production mode for assets
                use_sandbox=False     # Use production API
            )

            # Prepare credentials
            credentials = {
                'api_key': cred_data['api_key'],
                'api_secret': cred_data['api_secret']
            }

            # Initialize exchange
            self.exchange = ExchangeConnector(config, credentials)
            await self.exchange.connect()

            return True
            
        except Exception as e:
            print(f"❌ Failed to initialize exchange: {e}")
            return False
    
    async def get_assets(self, profile: str) -> Dict[str, Any]:
        """Get account assets for a profile"""
        try:
            if not await self.initialize_exchange(profile):
                raise AssetsCliError(f"Failed to initialize exchange for profile '{profile}'")
            
            # Get balance details directly from exchange
            balance = await self.exchange.fetch_balance()

            # Parse balance for display
            account_info = self._parse_balance_info(balance)

            assets = {
                'profile': profile,
                'account_info': account_info,
                'balance': balance,
                'timestamp': asyncio.get_event_loop().time()
            }
            
            return assets
            
        except Exception as e:
            raise AssetsCliError(f"Failed to get assets: {e}")
        finally:
            if self.exchange:
                await self.exchange.close()

    def _parse_balance_info(self, balance: Dict[str, Any]) -> Dict[str, float]:
        """Parse balance information from exchange response"""
        try:
            balance_dict = dict(balance) if isinstance(balance, dict) else {}

            # Initialize default values
            usdt_free = 0.0
            usdt_used = 0.0
            usdt_total = 0.0

            # Method 1: Try Bybit Unified Account structure first
            if self._is_bybit_unified_structure(balance_dict):
                account_info = balance_dict['info']['result']['list'][0]

                # Find USDT coin info
                if 'coin' in account_info:
                    for coin_info in account_info['coin']:
                        if coin_info.get('coin') == 'USDT':
                            # Bybit Unified Account structure
                            wallet_balance = float(coin_info.get('walletBalance', 0))
                            locked_balance = float(coin_info.get('locked', 0))

                            usdt_total = wallet_balance
                            usdt_used = locked_balance
                            usdt_free = wallet_balance - locked_balance
                            break

            # Method 2: Standard structure (fallback)
            elif 'USDT' in balance_dict and isinstance(balance_dict['USDT'], dict):
                usdt_info = balance_dict['USDT']

                # Handle null values from Bybit
                free_val = usdt_info.get('free')
                used_val = usdt_info.get('used')
                total_val = usdt_info.get('total', 0)

                if free_val is not None and used_val is not None:
                    # Standard case with real values
                    usdt_free = float(free_val)
                    usdt_used = float(used_val)
                    usdt_total = float(total_val) or (usdt_free + usdt_used)
                else:
                    # Bybit case with null free/used but valid total
                    usdt_total = float(total_val) if total_val is not None else 0.0
                    usdt_free = usdt_total  # Assume all is free if not specified
                    usdt_used = 0.0

            # Method 3: Free/Used/Total structure (fallback)
            elif self._is_standard_balance_structure(balance_dict):
                free_balance = balance_dict.get('free', {})
                used_balance = balance_dict.get('used', {})
                total_balance = balance_dict.get('total', {})

                free_val = free_balance.get('USDT')
                used_val = used_balance.get('USDT')
                total_val = total_balance.get('USDT')

                if free_val is not None and used_val is not None:
                    usdt_free = float(free_val)
                    usdt_used = float(used_val)
                    usdt_total = float(total_val) if total_val is not None else (usdt_free + usdt_used)
                elif total_val is not None:
                    usdt_total = float(total_val)
                    usdt_free = usdt_total  # Assume all is free
                    usdt_used = 0.0

            return {
                'free_balance': usdt_free,
                'used_balance': usdt_used,
                'total_balance': usdt_total
            }

        except (KeyError, TypeError, ValueError) as e:
            print(f"⚠️ Could not parse balance: {e}")
            return {
                'free_balance': 0.0,
                'used_balance': 0.0,
                'total_balance': 0.0
            }

    def _is_bybit_unified_structure(self, balance_dict: Dict[str, Any]) -> bool:
        """Check if balance has Bybit Unified Account structure"""
        return (
            'info' in balance_dict and
            isinstance(balance_dict['info'], dict) and
            'result' in balance_dict['info'] and
            isinstance(balance_dict['info']['result'], dict) and
            'list' in balance_dict['info']['result'] and
            len(balance_dict['info']['result']['list']) > 0
        )

    def _is_standard_balance_structure(self, balance_dict: Dict[str, Any]) -> bool:
        """Check if balance has standard free/used/total structure"""
        return (
            'free' in balance_dict and
            'used' in balance_dict and
            'total' in balance_dict
        )
    
    def format_assets_display(self, assets: Dict[str, Any]) -> str:
        """Format assets for display"""
        try:
            profile = assets['profile']
            account_info = assets['account_info']
            
            # Format display
            lines = [
                f"💰 Assets for Profile: {profile}",
                "=" * 40,
                "",
                "📊 Account Summary:",
                f"  💵 Total Balance: ${account_info.get('total_balance', 0):.2f} USDT",
                f"  💸 Available: ${account_info.get('free_balance', 0):.2f} USDT", 
                f"  🔒 Used: ${account_info.get('used_balance', 0):.2f} USDT",
                "",
                "📈 Balance Details:",
                f"  • Free Balance: ${account_info.get('free_balance', 0):.4f}",
                f"  • Used Balance: ${account_info.get('used_balance', 0):.4f}",
                f"  • Total Balance: ${account_info.get('total_balance', 0):.4f}",
                ""
            ]
            
            # Add additional balance info if available
            balance = assets.get('balance', {})
            if isinstance(balance, dict) and 'USDT' in balance:
                usdt_info = balance['USDT']
                lines.extend([
                    "🔍 Detailed USDT Info:",
                    f"  • Free: ${usdt_info.get('free', 0):.4f}",
                    f"  • Used: ${usdt_info.get('used', 0):.4f}",
                    f"  • Total: ${usdt_info.get('total', 0):.4f}",
                    ""
                ])
            
            lines.append("✅ Assets retrieved successfully")
            
            return "\n".join(lines)
            
        except Exception as e:
            return f"❌ Error formatting assets: {e}"
    
    async def list_profiles_command(self) -> None:
        """List available credential profiles"""
        try:
            profiles = list_profiles()
            
            if not profiles:
                print("📋 No credential profiles found")
                print("💡 Use 'store-credentials' command to add profiles")
                return
            
            print("📋 Available Credential Profiles:")
            print("=" * 40)
            
            for profile in profiles:
                display_name = profile.get('display_name', profile['profile'])
                print(f"🔑 {profile['profile']}")
                print(f"   Name: {display_name}")
                print(f"   Format: {profile.get('format', 'json')}")
                if profile.get('error'):
                    print(f"   ⚠️ Error loading profile")
                print()
            
            print(f"📊 Total: {len(profiles)} profiles")
            
        except Exception as e:
            print(f"❌ Error listing profiles: {e}")
    
    async def show_assets_command(self, profile: str) -> None:
        """Show assets for a specific profile"""
        try:
            print(f"🔍 Fetching assets for profile: {profile}")
            
            assets = await self.get_assets(profile)
            display = self.format_assets_display(assets)
            print(display)
            
        except AssetsCliError as e:
            print(f"❌ {e}")
            sys.exit(1)
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
            sys.exit(1)


async def main():
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(
        description='AutoTrader Assets CLI - View account assets',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python assets_cli.py show myprofile     # Show assets for 'myprofile'
  python assets_cli.py list              # List all available profiles
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Show assets command
    show_parser = subparsers.add_parser('show', help='Show assets for a profile')
    show_parser.add_argument('profile', help='Credential profile name')
    
    # List profiles command
    list_parser = subparsers.add_parser('list', help='List available credential profiles')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        sys.exit(1)
    
    cli = AssetsCli()
    
    try:
        if args.command == 'show':
            await cli.show_assets_command(args.profile)
        elif args.command == 'list':
            await cli.list_profiles_command()
        else:
            print(f"❌ Unknown command: {args.command}")
            parser.print_help()
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⚠️ Operation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)


if __name__ == '__main__':
    asyncio.run(main())
