"""
Strategy helper utilities for managing trading strategies
"""

from typing import Dict, List, Optional
from src.core.models.strategy_config import StrategyTemplateFactory, StrategyTemplate, StrategyType


class StrategyHelper:
    """Helper class for strategy management"""
    
    @staticmethod
    def get_all_strategy_summaries() -> Dict[str, Dict]:
        """Get summary information for all available strategies"""
        templates = StrategyTemplateFactory.get_all_templates()
        summaries = {}
        
        for strategy_type, template in templates.items():
            summaries[strategy_type] = {
                'name': template.name,
                'display_name': template.display_name,
                'description': template.description,
                'direction': template.direction.value,
                'take_profit': template.take_profit_percent,
                'stop_loss': template.stop_loss_percent,
                'risk_level': StrategyHelper._get_risk_level(template),
                'best_for': StrategyHelper._get_best_for(template),
                'max_position': template.max_position_size,
                'signal_threshold': template.signal_threshold,
                'cooldown_minutes': template.cooldown_minutes
            }
        
        return summaries
    
    @staticmethod
    def get_strategy_comparison() -> List[Dict]:
        """Get strategy comparison table"""
        templates = StrategyTemplateFactory.get_all_templates()
        comparison = []
        
        for strategy_type, template in templates.items():
            comparison.append({
                'strategy': template.display_name,
                'direction': template.direction.value,
                'tp_sl': f"{template.take_profit_percent}%/{template.stop_loss_percent}%",
                'risk': StrategyHelper._get_risk_level(template),
                'frequency': StrategyHelper._get_frequency_level(template),
                'best_for': StrategyHelper._get_best_for(template)
            })
        
        return comparison
    
    @staticmethod
    def get_strategy_details(strategy_type: str) -> Optional[Dict]:
        """Get detailed information for a specific strategy"""
        template = StrategyTemplateFactory.get_template_by_name(strategy_type)
        if not template:
            return None
        
        return {
            'name': template.name,
            'display_name': template.display_name,
            'description': template.description,
            'direction': template.direction.value,
            'entry_conditions': template.entry_conditions,
            'take_profit_percent': template.take_profit_percent,
            'stop_loss_percent': template.stop_loss_percent,
            'tp_sl_strategy': template.tp_sl_strategy,
            'dca_enabled': template.dca_enabled,
            'dca_strategies': template.dca_strategies,
            'max_position_size': template.max_position_size,
            'risk_per_trade': template.risk_per_trade,
            'ema_periods': template.ema_periods,
            'primary_timeframe': template.primary_timeframe,
            'signal_threshold': template.signal_threshold,
            'cooldown_minutes': template.cooldown_minutes,
            'risk_level': StrategyHelper._get_risk_level(template),
            'frequency_level': StrategyHelper._get_frequency_level(template),
            'best_for': StrategyHelper._get_best_for(template)
        }
    
    @staticmethod
    def get_recommended_strategy(user_experience: str = "beginner") -> str:
        """Get recommended strategy based on user experience"""
        recommendations = {
            'beginner': 'conservative_long',
            'intermediate': 'balanced_long_short',
            'advanced': 'aggressive_long',
            'expert': 'swing_trading',
            'scalper': 'scalping'
        }
        
        return recommendations.get(user_experience.lower(), 'conservative_long')
    
    @staticmethod
    def _get_risk_level(template: StrategyTemplate) -> str:
        """Determine risk level based on template settings"""
        if template.risk_per_trade <= 30:
            return "Low"
        elif template.risk_per_trade <= 45:
            return "Medium"
        else:
            return "High"
    
    @staticmethod
    def _get_frequency_level(template: StrategyTemplate) -> str:
        """Determine trading frequency based on cooldown and threshold"""
        if template.cooldown_minutes <= 2 and template.signal_threshold <= 0.5:
            return "Very High"
        elif template.cooldown_minutes <= 5 and template.signal_threshold <= 0.6:
            return "High"
        elif template.cooldown_minutes <= 10:
            return "Medium"
        else:
            return "Low"
    
    @staticmethod
    def _get_best_for(template: StrategyTemplate) -> str:
        """Get best use case for strategy"""
        best_for_map = {
            'conservative_long': "Beginners, stable growth",
            'aggressive_long': "Experienced traders, higher returns",
            'balanced_long_short': "All market conditions",
            'scalping': "Active monitoring, quick profits",
            'swing_trading': "Patient traders, medium-term"
        }
        
        return best_for_map.get(template.name, "General trading")
    
    @staticmethod
    def validate_strategy_config(strategy_type: str, custom_settings: Dict = None) -> tuple:
        """Validate strategy configuration"""
        template = StrategyTemplateFactory.get_template_by_name(strategy_type)
        if not template:
            return False, f"Strategy type '{strategy_type}' not found"
        
        # Validate custom settings if provided
        if custom_settings:
            for key, value in custom_settings.items():
                if key == 'take_profit_percent' and (value <= 0 or value > 20):
                    return False, "Take profit must be between 0.1% and 20%"
                elif key == 'stop_loss_percent' and (value <= 0 or value > 10):
                    return False, "Stop loss must be between 0.1% and 10%"
                elif key == 'max_position_size' and (value <= 0 or value > 2000):
                    return False, "Max position size must be between $1 and $2000"
                elif key == 'signal_threshold' and (value < 0.1 or value > 1.0):
                    return False, "Signal threshold must be between 0.1 and 1.0"
        
        return True, "Strategy configuration is valid"
    
    @staticmethod
    def get_strategy_emoji(strategy_type: str) -> str:
        """Get emoji for strategy type"""
        emoji_map = {
            'conservative_long': '🛡️',
            'aggressive_long': '⚡',
            'balanced_long_short': '⚖️',
            'scalping': '🏃',
            'swing_trading': '📈'
        }
        
        return emoji_map.get(strategy_type, '🎯')


class StrategyPresetManager:
    """Manager for strategy presets and user preferences"""
    
    @staticmethod
    def create_custom_strategy(base_strategy: str, modifications: Dict) -> Dict:
        """Create custom strategy based on existing template"""
        template = StrategyTemplateFactory.get_template_by_name(base_strategy)
        if not template:
            raise ValueError(f"Base strategy '{base_strategy}' not found")
        
        # Create custom strategy dict
        custom_strategy = {
            'name': f"custom_{base_strategy}",
            'display_name': f"Custom {template.display_name}",
            'description': f"Customized version of {template.display_name}",
            'direction': template.direction.value,
            'take_profit_percent': template.take_profit_percent,
            'stop_loss_percent': template.stop_loss_percent,
            'tp_sl_strategy': template.tp_sl_strategy,
            'dca_enabled': template.dca_enabled,
            'dca_strategies': template.dca_strategies.copy(),
            'max_position_size': template.max_position_size,
            'risk_per_trade': template.risk_per_trade,
            'ema_periods': template.ema_periods.copy(),
            'primary_timeframe': template.primary_timeframe,
            'signal_threshold': template.signal_threshold,
            'cooldown_minutes': template.cooldown_minutes
        }
        
        # Apply modifications
        for key, value in modifications.items():
            if key in custom_strategy:
                custom_strategy[key] = value
        
        return custom_strategy
    
    @staticmethod
    def get_preset_combinations() -> Dict[str, Dict]:
        """Get preset combinations for different scenarios"""
        return {
            'conservative_portfolio': {
                'strategies': ['conservative_long'],
                'allocation': {'conservative_long': 100},
                'description': 'Safe, LONG-only approach for beginners'
            },
            'balanced_portfolio': {
                'strategies': ['conservative_long', 'balanced_long_short'],
                'allocation': {'conservative_long': 60, 'balanced_long_short': 40},
                'description': 'Balanced approach with both conservative and adaptive strategies'
            },
            'aggressive_portfolio': {
                'strategies': ['aggressive_long', 'scalping'],
                'allocation': {'aggressive_long': 70, 'scalping': 30},
                'description': 'High-frequency trading for experienced users'
            },
            'swing_portfolio': {
                'strategies': ['swing_trading', 'conservative_long'],
                'allocation': {'swing_trading': 80, 'conservative_long': 20},
                'description': 'Medium-term trading with safety backup'
            }
        }