#!/usr/bin/env python3
"""Container helper utilities for Telegram bot commands"""

import subprocess
import json
import re
import time
from functools import lru_cache
from typing import List, Dict, Optional, Tuple
import logging

logger = logging.getLogger(__name__)

class ContainerHelper:
    """Helper class for Docker container operations"""
    
    def __init__(self, docker_cmd: str = "docker"):
        self.docker_cmd = docker_cmd
        self._trading_cache = {}
        self._cache_ttl = 60  # Cache for 60 seconds
    
    def _run_command(self, cmd: List[str]) -> Tuple[int, str, str]:
        """Run a command and return (returncode, stdout, stderr)"""
        try:
            result = subprocess.run(
                cmd, capture_output=True, text=True, timeout=30
            )
            return result.returncode, result.stdout.strip(), result.stderr.strip()
        except subprocess.TimeoutExpired:
            return 1, "", "Command timed out"
        except Exception as e:
            return 1, "", str(e)
    
    def normalize_symbol(self, symbol: str) -> str:
        """Normalize symbol to container name format - consistent with shell_constants.sh"""
        if not symbol:
            return ""

        # Handle different symbol formats
        if "/" in symbol:
            # Full format like "ETH/USDT:USDT" -> "eth"
            base_symbol = symbol.split("/")[0]
        elif symbol.upper().endswith("USDT") and len(symbol) > 4:
            # Format like "ETHUSDT" -> "eth"
            base_symbol = symbol[:-4]
        else:
            # Simple format like "ETH" or "eth" -> "eth"
            base_symbol = symbol

        # Convert to lowercase
        base_symbol = base_symbol.lower()

        # Remove any existing 'usdt' suffix to avoid duplication
        if base_symbol.lower().endswith('usdt'):
            base_symbol = base_symbol[:-4]

        # Add 'usdt' suffix for clarity - consistent with shell_constants.sh
        return f"{base_symbol}usdt"
    
    def find_container_by_symbol(self, symbol: str) -> Optional[str]:
        """Find container by symbol with multi-profile support"""
        normalized = self.normalize_symbol(symbol)
        if not normalized:
            return None

        # Get all trading containers (filtered)
        containers = self.list_trading_containers()

        if not containers:
            return None

        # Extract just the names
        container_names = [c['name'] for c in containers]

        # Strategy 1: Exact match (legacy containers)
        if normalized in container_names:
            return normalized

        # Strategy 2: Multi-profile pattern matching
        # Look for containers ending with the normalized symbol
        symbol_part = normalized  # e.g., "solusdt"
        matching_containers = []

        for name in container_names:
            name_lower = name.lower()
            # Check if container ends with our symbol (multi-profile pattern)
            if name_lower.endswith(symbol_part):
                # Verify it's actually a profile-symbol pattern
                if '-' in name_lower:
                    # Extract the part before the symbol
                    prefix = name_lower[:-len(symbol_part)]
                    if prefix.endswith('-'):
                        matching_containers.append(name)
                else:
                    # Direct match (legacy)
                    matching_containers.append(name)

        # Strategy 3: Fuzzy search as fallback
        if not matching_containers:
            for name in container_names:
                if symbol_part in name.lower():
                    matching_containers.append(name)

        # Return first match or None
        return matching_containers[0] if matching_containers else None
    
    def get_container_status(self, container_name: str) -> Optional[Dict]:
        """Get detailed container status"""
        if not container_name:
            return None
        
        # Get container info
        returncode, stdout, stderr = self._run_command([
            self.docker_cmd, "ps", "-a", "--filter", f"name={container_name}",
            "--format", "{{.Names}}\t{{.Status}}\t{{.Image}}\t{{.CreatedAt}}"
        ])
        
        if returncode != 0 or not stdout:
            return None
        
        lines = stdout.split('\n')
        if not lines or not lines[0]:
            return None
        
        parts = lines[0].split('\t')
        if len(parts) < 4:
            return None
        
        status_info = {
            'name': parts[0],
            'status': parts[1],
            'image': parts[2],
            'created': parts[3],
            'running': 'Up' in parts[1]
        }
        
        # Get additional stats if running
        if status_info['running']:
            stats_returncode, stats_stdout, _ = self._run_command([
                self.docker_cmd, "stats", "--no-stream", "--format",
                "{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}", container_name
            ])
            
            if stats_returncode == 0 and stats_stdout:
                stats_parts = stats_stdout.split('\t')
                if len(stats_parts) >= 3:
                    status_info['cpu'] = stats_parts[0]
                    status_info['memory'] = stats_parts[1]
                    status_info['network'] = stats_parts[2]
        
        return status_info
    
    def get_container_logs(self, container_name: str, lines: int = 50) -> Optional[str]:
        """Get container logs"""
        if not container_name:
            return None
        
        returncode, stdout, stderr = self._run_command([
            self.docker_cmd, "logs", "--tail", str(lines), "--timestamps", container_name
        ])
        
        if returncode != 0:
            return f"Error getting logs: {stderr}"
        
        return stdout
    
    def list_trading_containers(self) -> List[Dict]:
        """List all trading bot containers with intelligent detection"""
        returncode, stdout, stderr = self._run_command([
            self.docker_cmd, "ps", "-a", "--format",
            "{{.Names}}\t{{.Status}}\t{{.Image}}\t{{.CreatedAt}}"
        ])

        if returncode != 0:
            logger.error(f"Failed to list containers: {stderr}")
            return []

        containers = []
        lines = stdout.split('\n') if stdout else []

        # Pre-filter candidates to reduce expensive checks
        candidates = []
        for line in lines:
            if not line:
                continue

            parts = line.split('\t')
            if len(parts) < 4:
                continue

            name = parts[0]

            # Quick pre-filtering
            if self._quick_trading_filter(name, parts[2]):
                candidates.append({
                    'name': name,
                    'status': parts[1],
                    'image': parts[2],
                    'created': parts[3],
                    'running': 'Up' in parts[1]
                })

        # Now do intelligent detection only on candidates
        for candidate in candidates:
            if self._is_trading_container(candidate['name']):
                containers.append(candidate)

        return containers

    def _quick_trading_filter(self, name: str, image: str) -> bool:
        """Quick pre-filter to identify potential trading containers"""
        name_lower = name.lower()
        image_lower = image.lower()

        # Exclude obvious system containers
        excluded_patterns = {
            'telegram', 'postgres', 'redis', 'nginx', 'mysql', 'mongo',
            'rabbitmq', 'kafka', 'elasticsearch', 'kibana', 'grafana',
            'prometheus', 'jaeger', 'consul', 'vault', 'traefik'
        }

        if any(pattern in name_lower for pattern in excluded_patterns):
            return False

        # Include containers with trading-related indicators
        trading_indicators = ['trade', 'bot', 'trader', 'autotrader', 'usdt']

        if any(indicator in name_lower for indicator in trading_indicators):
            return True

        if any(indicator in image_lower for indicator in trading_indicators):
            return True

        # Include containers that end with 'usdt' pattern (crypto trading)
        if name_lower.endswith('usdt'):
            return True

        return False

    def _is_trading_container(self, container_name: str) -> bool:
        """
        Intelligently detect trading containers using multiple strategies with caching:
        1. Check for actual trading activity (logs, configs)
        2. Analyze container environment and image
        3. Pattern matching as fallback
        """
        # Check cache first
        cache_key = f"trading_{container_name}"
        current_time = time.time()

        if cache_key in self._trading_cache:
            cached_result, cached_time = self._trading_cache[cache_key]
            if current_time - cached_time < self._cache_ttl:
                return cached_result

        # Perform detection
        result = self._detect_trading_container(container_name)

        # Cache result
        self._trading_cache[cache_key] = (result, current_time)

        return result

    def _detect_trading_container(self, container_name: str) -> bool:
        """Core detection logic without caching"""
        name = container_name.lower()

        # Quick exclusions for obvious non-trading containers
        excluded_patterns = {
            'telegram', 'postgres', 'redis', 'nginx', 'mysql', 'mongo',
            'rabbitmq', 'kafka', 'elasticsearch', 'kibana', 'grafana',
            'prometheus', 'jaeger', 'consul', 'vault', 'traefik'
        }

        if any(pattern in name for pattern in excluded_patterns):
            return False

        # Strategy 1: Pattern-based detection (fast, try first)
        if self._matches_trading_patterns(name):
            # For pattern matches, do a quick validation if container exists
            try:
                if self._quick_trading_validation(container_name):
                    return True
            except Exception:
                # If validation fails (container doesn't exist), still trust pattern match
                return True

        # Strategy 2: Check for trading activity indicators (more expensive)
        if self._has_trading_activity(container_name):
            return True

        # Strategy 3: Check container configuration (expensive)
        if self._has_trading_config(container_name):
            return True

        # Strategy 4: Check container image and environment (most expensive)
        if self._has_trading_environment(container_name):
            return True

        return False

    def _quick_trading_validation(self, container_name: str) -> bool:
        """Quick validation for pattern-matched containers"""
        try:
            # Just check if container is running and has recent activity
            cmd = [self.docker_cmd, "logs", "--tail", "5", "--since", "1m", container_name]
            returncode, stdout, stderr = self._run_command(cmd)

            if returncode == 0 and stdout:
                # If container has recent logs, likely active
                return True

            # Check if container is running
            cmd = [self.docker_cmd, "inspect", "--format", "{{.State.Running}}", container_name]
            returncode, stdout, stderr = self._run_command(cmd)

            return returncode == 0 and stdout.strip().lower() == "true"

        except Exception:
            return True  # If we can't validate, assume pattern match is correct

    def _has_trading_activity(self, container_name: str) -> bool:
        """Check if container has actual trading activity"""
        try:
            # Check recent logs for trading indicators
            cmd = [self.docker_cmd, "logs", "--tail", "50", container_name]
            returncode, stdout, stderr = self._run_command(cmd)

            if returncode != 0:
                return False

            logs = stdout.lower()

            # Trading activity indicators in logs
            trading_indicators = [
                'position', 'order', 'trade', 'buy', 'sell', 'price',
                'balance', 'pnl', 'profit', 'loss', 'binance', 'bybit',
                'exchange', 'market', 'limit', 'stop', 'entry', 'exit',
                'orchestrator', 'strategy', 'signal', 'ema', 'rsi',
                'volume', 'ticker', 'symbol', 'usdt', 'btc', 'eth'
            ]

            # Count trading indicators
            indicator_count = sum(1 for indicator in trading_indicators if indicator in logs)

            # If we find multiple trading indicators, it's likely a trading container
            return indicator_count >= 3

        except Exception:
            return False

    def _has_trading_config(self, container_name: str) -> bool:
        """Check if container has trading configuration"""
        try:
            # Check for config files or environment variables
            cmd = [self.docker_cmd, "exec", container_name, "find", "/app", "-name", "*.json", "-o", "-name", "*.yaml", "-o", "-name", "*.yml"]
            returncode, stdout, stderr = self._run_command(cmd)

            if returncode == 0 and stdout:
                # Check if config files contain trading-related content
                config_files = stdout.strip().split('\n')
                for config_file in config_files[:5]:  # Check first 5 files
                    if self._check_config_file_content(container_name, config_file.strip()):
                        return True

            # Check environment variables
            cmd = [self.docker_cmd, "exec", container_name, "env"]
            returncode, stdout, stderr = self._run_command(cmd)

            if returncode == 0:
                env_vars = stdout.lower()
                trading_env_indicators = [
                    'api_key', 'api_secret', 'binance', 'bybit', 'exchange',
                    'symbol', 'amount', 'trading', 'bot', 'strategy'
                ]

                return any(indicator in env_vars for indicator in trading_env_indicators)

        except Exception:
            pass

        return False

    def _check_config_file_content(self, container_name: str, config_file: str) -> bool:
        """Check if config file contains trading-related content"""
        try:
            cmd = [self.docker_cmd, "exec", container_name, "head", "-20", config_file]
            returncode, stdout, stderr = self._run_command(cmd)

            if returncode == 0:
                content = stdout.lower()
                trading_config_indicators = [
                    'symbol', 'amount', 'exchange', 'api_key', 'strategy',
                    'trading', 'position', 'order', 'price', 'usdt'
                ]

                return any(indicator in content for indicator in trading_config_indicators)

        except Exception:
            pass

        return False

    def _has_trading_environment(self, container_name: str) -> bool:
        """Check container image and runtime environment"""
        try:
            # Get container info
            cmd = [self.docker_cmd, "inspect", container_name]
            returncode, stdout, stderr = self._run_command(cmd)

            if returncode != 0:
                return False

            import json
            container_info = json.loads(stdout)[0]

            # Check image name
            image = container_info.get('Config', {}).get('Image', '').lower()
            if any(keyword in image for keyword in ['trader', 'trading', 'bot', 'autotrader']):
                return True

            # Check working directory
            workdir = container_info.get('Config', {}).get('WorkingDir', '').lower()
            if any(keyword in workdir for keyword in ['trading', 'bot', 'trader']):
                return True

            # Check command/entrypoint
            cmd_info = container_info.get('Config', {}).get('Cmd', [])
            if cmd_info:
                cmd_str = ' '.join(cmd_info).lower()
                if any(keyword in cmd_str for keyword in ['trading', 'bot', 'trader', 'main.py']):
                    return True

        except Exception:
            pass

        return False

    def _matches_trading_patterns(self, name: str) -> bool:
        """Fallback pattern matching for container names"""
        # Pattern 1: Ends with 'usdt' (crypto trading pattern)
        if name.endswith('usdt'):
            # Extract potential symbol part
            symbol_part = name[:-4]

            # Check if it looks like a crypto symbol pattern
            if self._looks_like_crypto_symbol(symbol_part):
                return True

        # Pattern 2: Contains trading keywords
        trading_keywords = ['trade', 'bot', 'trader', 'trading', 'autotrader']
        if any(keyword in name for keyword in trading_keywords):
            return True

        # Pattern 3: Profile-symbol pattern (e.g., main-btcusdt, test-ethusdt)
        if '-' in name and name.endswith('usdt'):
            parts = name.split('-')
            if len(parts) >= 2:
                symbol_part = parts[-1][:-4]  # Remove 'usdt'
                if self._looks_like_crypto_symbol(symbol_part):
                    return True

        return False

    def _looks_like_crypto_symbol(self, symbol: str) -> bool:
        """Check if a string looks like a cryptocurrency symbol"""
        if not symbol:
            return False

        # Crypto symbols are typically:
        # - 2-10 characters long
        # - Alphanumeric
        # - Often all uppercase or lowercase
        if not (2 <= len(symbol) <= 10):
            return False

        if not symbol.isalnum():
            return False

        # Additional heuristics:
        # - Common crypto symbol patterns
        # - Avoid common non-crypto words
        non_crypto_words = {
            'app', 'api', 'web', 'db', 'cache', 'queue', 'worker',
            'admin', 'user', 'auth', 'log', 'test', 'dev', 'prod'
        }

        if symbol in non_crypto_words:
            return False

        # If it passes all checks, likely a crypto symbol
        return True
    
    def stop_container(self, container_name: str) -> Tuple[bool, str]:
        """Stop a container"""
        if not container_name:
            return False, "Container name is required"
        
        returncode, stdout, stderr = self._run_command([
            self.docker_cmd, "stop", container_name
        ])
        
        if returncode == 0:
            return True, f"Container {container_name} stopped successfully"
        else:
            return False, f"Failed to stop container: {stderr}"
    
    def restart_container(self, container_name: str) -> Tuple[bool, str]:
        """Restart a container"""
        if not container_name:
            return False, "Container name is required"
        
        returncode, stdout, stderr = self._run_command([
            self.docker_cmd, "restart", container_name
        ])
        
        if returncode == 0:
            return True, f"Container {container_name} restarted successfully"
        else:
            return False, f"Failed to restart container: {stderr}"
    
    def remove_container(self, container_name: str) -> Tuple[bool, str]:
        """Remove a container"""
        if not container_name:
            return False, "Container name is required"
        
        # Stop first
        self._run_command([self.docker_cmd, "stop", container_name])
        
        # Then remove
        returncode, stdout, stderr = self._run_command([
            self.docker_cmd, "rm", container_name
        ])
        
        if returncode == 0:
            return True, f"Container {container_name} removed successfully"
        else:
            return False, f"Failed to remove container: {stderr}"

# Global instance
container_helper = ContainerHelper()
