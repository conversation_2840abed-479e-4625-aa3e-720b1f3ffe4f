# Telegram Bot Configuration
# Copy this file to .env and fill in your actual values

# Telegram <PERSON>t <PERSON> (get from @BotFather)
TELEGRAM_BOT_TOKEN=your_bot_token_here

# Optional: For testing direct API calls only (not needed for normal operation)
TELEGRAM_CHAT_ID=your_chat_id_for_testing

# Note: TELEGRAM_CHAT_ID is no longer needed with the new authorization system
# Use ./bot.sh setup-auth to configure authorized users instead

# Trading API Configuration
BYBIT_API_KEY=your_bybit_api_key_here
BYBIT_API_SECRET=your_bybit_api_secret_here

# Optional: Test mode credentials
BYBIT_TESTNET_API_KEY=your_testnet_api_key_here
BYBIT_TESTNET_API_SECRET=your_testnet_api_secret_here

# Application Configuration
LOG_LEVEL=INFO
DEBUG=false

# Docker Configuration
TRADER_IMAGE=autotrader-trader:latest
TELEGRAM_IMAGE=autotrader-telegram:latest
