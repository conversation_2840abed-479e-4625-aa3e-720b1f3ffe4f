{"start_time": 1754383423.272442, "tests": {"imports": {"passed": 2, "failed": 5, "details": ["❌ Strategy Builder Handler: Import failed - No module named 'aiohttp'", "❌ Strategy Builder Templates: Import failed - No module named 'aiohttp'", "❌ Custom Strategy Service: Import failed - No module named 'aiohttp'", "✅ Strategy Builder Model: Import successful", "✅ Custom Strategy Model: Import successful", "❌ Telegram Templates: Import failed - No module named 'aiohttp'", "❌ Base Handler: Import failed - No module named 'aiohttp'"]}, "templates": {"passed": 0, "failed": 1, "details": ["❌ Template system: Failed to import - No module named 'aiohttp'"]}, "services": {"passed": 0, "failed": 1, "details": ["❌ Service layer: Failed to initialize - No module named 'aiohttp'"]}, "handlers": {"passed": 0, "failed": 1, "details": ["❌ Handler initialization: Failed - No module named 'aiohttp'"]}, "integration": {"passed": 0, "failed": 1, "details": ["❌ Integration test: Failed - No module named 'aiohttp'"]}, "callbacks": {"passed": 18, "failed": 0, "details": ["✅ Step callback: 'step_basic_info' routed correctly", "✅ Step callback: 'step_entry' routed correctly", "✅ Step callback: 'step_dca' routed correctly", "✅ Step callback: 'step_exit' routed correctly", "✅ Builder callback: 'builder_save' routed correctly", "✅ Builder callback: 'builder_preview' routed correctly", "✅ Builder callback: 'builder_cancel' routed correctly", "✅ Strategy callback: 'strategy_view_test' routed correctly", "✅ Special callback: 'my_strategies' routed correctly", "✅ Delete callback: 'delete_strategy_test' routed correctly", "✅ Confirm callback: 'confirm_delete_test' routed correctly", "✅ Entry callback: 'entry_long' routed correctly", "✅ DCA callback: 'dca_triggers' routed correctly", "✅ Exit callback: 'exit_tp' routed correctly", "✅ TP callback: 'tp_fixed' routed correctly", "✅ SL callback: 'sl_dynamic' routed correctly", "✅ Should not route: 'random_callback' routed correctly", "✅ Should not route: 'unknown_data' routed correctly"]}}, "summary": {"total_tests": 29, "total_passed": 20, "total_failed": 9, "success_rate": 68.96551724137932, "test_categories": 6, "status": "FAILED"}, "end_time": 1754383423.3146281, "duration": 0.04218602180480957}