# 🚀 Professional Crypto Trading Bot

Một trading bot crypto chuyên nghiệp với chiến lược DCA (Dollar Cost Averaging) và phân tích kỹ thuật tiên tiến.

## ✨ Tính Năng

- **🔑 Multi-Profile Support**: <PERSON><PERSON><PERSON> nhiều bot cùng symbol với profiles khác nhau
- **🧠 Smart Detection**: Tự động phát hiện containers với selection UI
- **Auto Update**: Tự động update bot.sh từ GitHub Gist với `./bot.sh upgrade`
- **DCA Strategy**: Enhanced Dollar Cost Averaging với 7 loại strategies
- **Planned Pair Strategy**: Stink bid strategy với entry tại EMA_34
- **Technical Analysis**: RSI, EMA, Bollinger Bands, MACD
- **Risk Management**: Stop Loss, Take Profit, position sizing
- **Real-time Monitoring**: CLI Dashboard và Web interface
- **Clean Architecture**: Modular design với dependency injection
- **🔄 Backward Compatible**: Legacy containers vẫn hoạt động bình thường

## 🚀 Cài Đặt

### 1. <PERSON><PERSON><PERSON> Thống

- Python 3.8+
- 4GB RAM minimum
- Internet connection ổn định

### 2. Cài Đặt Dependencies

```bash
pip install -r requirements.txt
```

### 3. Thiết Lập Credentials

⚠️ **Quan trọng**: Không bao giờ commit credentials vào source code!

#### Cách 1: Sử dụng .env file (Khuyến nghị)

```bash
# Tạo .env file từ template
cp .env.example .env

# Chỉnh sửa .env với credentials thật của bạn
nano .env

# Load environment variables
source load_env.sh
```

#### Cách 2: Export trực tiếp

```bash
# Telegram Bot (bắt buộc)
export TELEGRAM_BOT_TOKEN="your_bot_token_from_botfather"
export TELEGRAM_CHAT_ID="your_telegram_chat_id"

# Bybit API (bắt buộc cho trading)
export BYBIT_API_KEY="your_api_key_here"
export BYBIT_API_SECRET="your_api_secret_here"
```

#### Lấy Telegram Credentials

1. **Bot Token**: Message @BotFather → `/newbot` → copy token
2. **Chat ID**: Message bot của bạn → visit `https://api.telegram.org/bot<TOKEN>/getUpdates`

#### Lấy Bybit API Credentials

1. Đăng nhập [Bybit.com](https://www.bybit.com/)
2. **Account Settings** → **API Management** → **Create New Key**
3. Permissions: **Read** + **Trade**
4. Whitelist IP (khuyến nghị)

📖 **Chi tiết**: Xem [README_SECURITY.md](README_SECURITY.md)

### 4. Cấu Hình Bot

```bash
# Tạo config từ template
cp configs/config.json configs/my-config.json
nano configs/my-config.json
```

**Cấu hình cơ bản:**

```json
{
  "symbol": "BTC/USDT:USDT",
  "exchange": "bybit",
  "direction": "LONG",
  "amount": 50.0,
  "use_test_mode": true,
  "indicators": {
    "ema_periods": [34, 89, 120],
    "timeframes": ["15m", "1h", "4h"],
    "long_signal_threshold": 0.6,
    "short_signal_threshold": 0.6
  },
  "dca": {
    "enabled": true,
    "strategies": {
      "BB_LOWER": {
        "timeframe": "15m",
        "amount": 50,
        "enabled": true
      },
      "EMA_89": {
        "timeframe": "1h",
        "amount": 75,
        "enabled": false
      }
    }
  },
  "risk": {
    "max_position_size": 200.0,
    "daily_loss_limit": 10.0,
    "default_stop_loss": 2.0,
    "default_take_profit": 3.0
  }
}
```

## 🔄 Auto Update

Bot hỗ trợ tự động cập nhật từ GitHub Gist:

```bash
# Cập nhật bot.sh lên version mới nhất
./bot.sh upgrade

# Hoặc sử dụng global command (sau khi bind)
traderbot upgrade
```

### Setup Auto Update

1. **Xem hướng dẫn chi tiết**: [SETUP_GIST_SYNC.md](SETUP_GIST_SYNC.md)
2. **Developers**: Push code sẽ tự động sync với gist
3. **Users**: Chỉ cần chạy `./bot.sh upgrade` để có version mới

### Tính năng an toàn

- ✅ Auto backup trước khi update
- ✅ Validation file integrity
- ✅ Preview changes trước khi apply
- ✅ Rollback nếu có lỗi

## 🔑 Multi-Profile Support

### Tạo Bot với Profile

```bash
# Tạo bot với profile "main"
./bot.sh start btc --profile main --amount 1000

# Tạo bot với profile "test"
./bot.sh start btc --profile test --amount 100

# Tạo bot legacy (không có profile)
./bot.sh start eth --amount 500
```

**Kết quả:**
- `main-btcusdt` - Profile main trading BTC
- `test-btcusdt` - Profile test trading BTC
- `ethusdt` - Legacy ETH container

### Smart Detection Commands

```bash
# Smart detection - hiển thị selection nếu có nhiều containers
./bot.sh status btc     # Shows: main-btcusdt, test-btcusdt
./bot.sh logs btc 100   # Shows selection UI
./bot.sh stop btc       # Shows confirmation with selection

# Direct access - truy cập trực tiếp container
./bot.sh status main-btc    # Direct access to main-btcusdt
./bot.sh logs test-btc 50   # Direct access to test-btcusdt
```

### Telegram Bot với Multi-Profile

```
/list                   # Hiển thị bots grouped by profile
/createbot              # Wizard hỏi profile name
/status btc             # Smart detection với selection UI
/logs main-btc 100      # Direct access
/stop test-btc          # Direct confirmation
```

### Use Cases

**1. Multi-Account Trading:**
```bash
./bot.sh start btc --profile main --amount 1000    # Primary account
./bot.sh start btc --profile backup --amount 500   # Backup account
./bot.sh start btc --profile test --amount 100     # Paper trading
```

**2. Strategy Diversification:**
```bash
./bot.sh start btc --profile scalp --amount 200    # Scalping
./bot.sh start btc --profile swing --amount 800    # Swing trading
./bot.sh start btc --profile dca --amount 1000     # DCA strategy
```

**3. Environment Separation:**
```bash
./bot.sh start btc --profile dev --amount 10       # Development
./bot.sh start btc --profile staging --amount 50   # Staging
./bot.sh start btc --profile prod --amount 1000    # Production
```

📖 **Chi tiết:** [Multi-Profile Guide](docs/MULTI_PROFILE_GUIDE.md) | [Migration Guide](docs/MIGRATION_GUIDE.md)

## 🎮 Sử Dụng

### Quick Start

```bash
# Xem hướng dẫn
./run.sh

# Test mode (an toàn)
./run.sh test

# CLI Dashboard
./run.sh dashboard

# Live trading
./run.sh start
```

### Python Commands

```bash
# Hiển thị usage
python main.py

# Test mode
python main.py --test

# Dashboard
python main.py --dashboard

# Start trading
python main.py --start

# Show config
python main.py --show-config
```

### Với Custom Config

```bash
# Tạo config riêng
cp configs/config.json configs/btc-long.json

# Sử dụng config riêng
./run.sh test --config configs/btc-long.json
./run.sh start --config configs/btc-long.json
```

## 📊 Chiến Lược Trading

### DCA Strategy

Bot sử dụng Dollar Cost Averaging với 7 loại entry points:

- **BB_LOWER**: Mua tại Bollinger Lower Band (oversold)
- **EMA_89**: Mua tại EMA 89 (support trung hạn)
- **EMA_34**: Mua tại EMA 34 (support ngắn hạn)
- **EMA_120**: Mua tại EMA 120 (support dài hạn)
- **BB_MIDDLE**: Mua tại Bollinger Middle Band
- **RSI_OVERSOLD**: Mua khi RSI < 30
- **MACD_SIGNAL**: Mua theo MACD bullish signal

### Planned Pair Strategy

Stink bid strategy tại EMA_34:

- **Entry**: Đặt order tại EMA_34, đợi giá về mức này
- **Take Profit**: Bollinger Upper Band + multiplier
- **Stop Loss**: EMA_120 - multiplier
- **No Threshold**: Luôn generate signal

## 🛡️ Risk Management

- **Position Sizing**: Tự động tính dựa trên risk percentage
- **Stop Loss**: Tự động đặt stop loss
- **Take Profit**: Tự động đặt take profit
- **Daily Loss Limit**: Giới hạn lỗ hàng ngày
- **Capital Management**: Tự động cập nhật từ exchange balance

## 🐳 Docker Support

### Quick Start

```bash
# Build và run
docker-compose up -d

# Xem logs
docker-compose logs -f
```

### Docker Commands

```bash
# Build image
docker build -t crypto-bot .

# Run container
docker run -d --name crypto-bot \
  -e BYBIT_API_KEY=your_key \
  -e BYBIT_API_SECRET=your_secret \
  -v $(pwd)/configs:/app/configs \
  -v $(pwd)/data:/app/data \
  crypto-bot
```

## 📁 Cấu Trúc Project

```
trade/
├── src/                    # Source code
│   ├── core/              # Domain logic
│   ├── infrastructure/    # External services
│   ├── application/       # Orchestration
│   └── presentation/      # Interfaces
├── configs/               # Configuration files
├── logs/                  # Log files
├── data/                  # Trading data
├── main.py               # Entry point
├── run.sh                # Quick launcher
└── requirements.txt      # Dependencies
```

## 🧪 Testing

```bash
# Test components
python test_components.py

# Test với config riêng
python main.py --test --config configs/test-config.json
```

## 📊 Monitoring

### Log Files

- `logs/tradingorchestrator.log`: Main operations
- `logs/dcastrategy.log`: DCA operations
- `logs/errors.log`: Error tracking
- `logs/trades.jsonl`: Trade records

### Data Files

- `data/positions.csv`: Position history
- `data/daily_stats.csv`: Performance stats
- `data/performance.json`: Detailed metrics

## 🆘 Troubleshooting

### Common Issues

1. **API Connection Failed**

   ```bash
   # Check credentials
   echo $BYBIT_API_KEY
   echo $BYBIT_API_SECRET

   # Test connection
   python test_components.py
   ```

2. **Config Not Found**

   ```bash
   # Check file exists
   ls -la configs/

   # Show config
   python main.py --show-config
   ```

3. **Position Not Created**

   ```bash
   # Check logs
   tail -f logs/tradingorchestrator.log

   # Check balance
   python main.py --test
   ```

### Debug Mode

```bash
# Enable debug logging
./run.sh dashboard --debug
./run.sh start --debug
```

## ⚠️ Risk Disclaimer

**QUAN TRỌNG**: Bot này dành cho mục đích giáo dục và thử nghiệm. Giao dịch crypto có rủi ro cao.

- Luôn test với số tiền nhỏ trước
- Hiểu rõ strategy trước khi sử dụng
- Không đầu tư quá khả năng chịu đựng
- Theo dõi và điều chỉnh thường xuyên

## 🤝 Hỗ Trợ

- **Logs**: Kiểm tra files trong `logs/` directory
- **Config**: Sử dụng `python main.py --show-config`
- **Test**: Chạy `python test_components.py`
- **Debug**: Thêm flag `--debug` khi chạy

---

## 🔧 Sửa Lỗi Docker Image Not Found

### ❌ Lỗi: `Unable to find image 'autotrader:latest' locally`

**Nguyên nhân**: Bot đang cố gắng pull Docker image từ registry nhưng image không tồn tại hoặc không thể truy cập.

### ✅ Giải pháp

#### Option 1: Quick Fix cho Server (Khuyến nghị)

```bash
# Kiểm tra images hiện tại
./bot.sh images

# Tự động sửa lỗi images
./bot.sh fix-images

# Cập nhật images từ registry
./bot.sh update-images
```

#### Option 2: Manual Fix trên Server

```bash
# Kiểm tra images hiện có
./bot.sh images

# Nếu có conflict giữa registry và local images
docker rmi autotrader-telegram:latest autotrader-trader:latest

# Pull từ registry
docker pull ghcr.io/hoangtrung99/autotrader-telegram:latest
docker pull ghcr.io/hoangtrung99/autotrader-trader:latest

# Hoặc build local
./bot.sh build

# Test
./bot.sh telegram
```

#### Option 3: Force Local Mode

```bash
# Sử dụng local images
export LOCAL=true
./bot.sh telegram
```

#### Option 4: Sử dụng Local Runner (Không cần Docker)

```bash
# Setup môi trường local
./run_local.sh setup

# Cấu hình token
export TELEGRAM_BOT_TOKEN='your_bot_token'
export BYBIT_API_KEY='your_api_key'
export BYBIT_API_SECRET='your_api_secret'

# Chạy Telegram bot
./run_local.sh telegram

# Hoặc chạy trading bot
./run_local.sh start eth --amount 100
```

#### Option 2: Build Docker Images Locally

```bash
# Build images locally
./bot.sh build

# Hoặc setup toàn bộ (sẽ tự động build)
./bot.sh setup

# Chạy bot
./bot.sh telegram
```

### 🚀 Automatic Fallback

Bot đã được cập nhật với hệ thống fallback tự động:

1. **Kiểm tra Docker**: Nếu Docker không có sẵn → Chuyển sang local runner
2. **Kiểm tra Images**: Nếu images không tồn tại → Tự động build local
3. **Thông báo rõ ràng**: Hiển thị hướng dẫn cụ thể cho từng tình huống

### 📋 So sánh các phương pháp

| Phương pháp | Ưu điểm | Nhược điểm | Khuyến nghị |
|-------------|---------|------------|-------------|
| **Local Runner** | Đơn giản, nhanh, không cần Docker | Phụ thuộc Python system | ⭐ Tốt nhất cho development |
| **Docker Build** | Isolated, portable | Phức tạp hơn, cần Docker | ⭐ Tốt cho production |

---

**🌟 Happy Trading! Chúc bạn giao dịch thành công! 🌟**
