#!/bin/bash
# AutoTrader Bot - Standalone Server Deployment Script
# Single file deployment - no dependencies required
# Version: 3.2.0-standalone

set -euo pipefail

# ===============================
# Constants (inlined from shell_constants.sh)
# ===============================

# Version and basic config
SCRIPT_VERSION="3.2.0-standalone"
VERSION="$SCRIPT_VERSION"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Directory constants
AUTOTRADER_DIR="$HOME/.autotrader"
CREDENTIALS_DIR="$AUTOTRADER_DIR/credentials"
CONFIG_DIR="./configs"
DATA_DIR="./data"
LOGS_DIR="./logs"

# Docker image names - use registry images by default
TELEGRAM_IMAGE="${TELEGRAM_IMAGE:-ghcr.io/hoangtrung99/autotrader-telegram:latest}"
TRADER_IMAGE="${TRADER_IMAGE:-ghcr.io/hoangtrung99/autotrader-trader:latest}"

# Environment variable names
TELEGRAM_BOT_TOKEN_VAR="TELEGRAM_BOT_TOKEN"
BYBIT_API_KEY_VAR="BYBIT_API_KEY"
BYBIT_API_SECRET_VAR="BYBIT_API_SECRET"
BYBIT_SECRET_KEY_VAR="BYBIT_SECRET_KEY"

# Default values
DEFAULT_TRADING_AMOUNT="50"
DEFAULT_SYMBOL_SUFFIX="/USDT:USDT"
DEFAULT_DIRECTION="LONG"
DEFAULT_TEST_MODE="false"
DEFAULT_CREDENTIAL_PROFILE="default"
CREDENTIAL_FILE_PERMISSIONS="600"

# Auto-load .env file if exists
if [[ -f ".env" ]]; then
    source .env
fi

# Environment variables with defaults
TELEGRAM_BOT_TOKEN="${TELEGRAM_BOT_TOKEN:-}"

# ===============================
# Utility Functions (inlined)
# ===============================

print_success() {
    echo "✅ $1"
}

print_error() {
    echo "❌ $1" >&2
}

print_warning() {
    echo "⚠️ $1"
}

print_info() {
    echo "ℹ️ $1"
}

get_env_var() {
    local var_name="$1"
    echo "${!var_name:-}"
}

ensure_directories() {
    local dirs=("$AUTOTRADER_DIR" "$CREDENTIALS_DIR" "$CONFIG_DIR" "$DATA_DIR" "$LOGS_DIR")
    
    for dir in "${dirs[@]}"; do
        if [[ ! -d "$dir" ]]; then
            mkdir -p "$dir"
            echo "📁 Created directory: $dir"
        fi
    done
}

check_docker() {
    if ! command -v docker &> /dev/null; then
        echo "❌ Docker is not installed or not in PATH"
        echo "Please install Docker and try again"
        return 1
    fi
    
    if ! docker info &> /dev/null; then
        echo "❌ Docker daemon is not running"
        echo "Please start Docker and try again"
        return 1
    fi
    
    return 0
}

# ===============================
# Template Management
# ===============================

extract_template_from_image() {
    echo "📄 Extracting template config from Docker image..."
    
    # Pull trader image if not exists
    if ! docker image inspect "$TRADER_IMAGE" &>/dev/null; then
        echo "🐳 Pulling trader image: $TRADER_IMAGE"
        if ! docker pull "$TRADER_IMAGE"; then
            echo "❌ Failed to pull trader image"
            return 1
        fi
    fi
    
    # Extract template.json from image
    local temp_container="temp_extract_$$"
    
    # Create temporary container and copy template
    if docker create --name "$temp_container" "$TRADER_IMAGE" >/dev/null 2>&1; then
        if docker cp "$temp_container:/app/template.json" "$CONFIG_DIR/template.json" 2>/dev/null; then
            echo "✅ Extracted template.json from image"
        else
            echo "⚠️ Could not extract template.json from image, creating basic template..."
            create_basic_template
        fi
        docker rm "$temp_container" >/dev/null 2>&1
    else
        echo "⚠️ Could not create temporary container, creating basic template..."
        create_basic_template
    fi
}

create_basic_template() {
    cat > "$CONFIG_DIR/template.json" << 'EOF'
{
  "symbol": "SYMBOL_PLACEHOLDER",
  "exchange": "bybit",
  "direction": "LONG",
  "amount": 50.0,
  "use_test_mode": false,
  "use_sandbox": false,
  "order_type": "limit",
  "signal_cooldown_minutes": 3.0,
  "trading_loop_interval_seconds": 10,
  "log_level": "INFO",
  "save_trades_to_csv": true,
  "enable_notifications": true
}
EOF
}

# ===============================
# Setup Functions
# ===============================

setup_environment() {
    echo "🔧 AutoTrader Complete Setup"
    echo "============================"

    # Check and install Docker (Linux only)
    if ! command -v docker &> /dev/null; then
        if [[ "$OSTYPE" == "linux-gnu"* ]]; then
            echo "🐳 Installing Docker..."
            curl -fsSL https://get.docker.com -o get-docker.sh
            sudo sh get-docker.sh
            sudo usermod -aG docker $USER
            rm get-docker.sh
            echo "✅ Docker installed. Please log out and back in."
        else
            echo "❌ Please install Docker manually for your OS"
            return 1
        fi
    fi

    # Verify Docker is running
    if ! docker info &> /dev/null; then
        echo "❌ Docker daemon is not running. Please start Docker."
        return 1
    else
        echo "✅ Docker is running"
    fi

    # Pull Docker images first (before creating configs)
    if check_docker; then
        echo "🐳 Pulling Docker images..."
        docker pull "$TELEGRAM_IMAGE" 2>/dev/null || echo "⚠️ Could not pull Telegram image"
        docker pull "$TRADER_IMAGE" 2>/dev/null || echo "⚠️ Could not pull Trader image"
    fi

    # Create directories and config files
    echo "📁 Creating directories and config files..."
    ensure_directories

    # Extract template config from Docker image
    if [[ ! -f "$CONFIG_DIR/template.json" ]]; then
        extract_template_from_image
        echo "✅ Created template config: $CONFIG_DIR/template.json"
    else
        echo "✅ Template config already exists"
    fi

    # Check Telegram setup
    echo "📱 Checking Telegram configuration..."
    if [[ -z "$TELEGRAM_BOT_TOKEN" ]]; then
        echo "⚠️ Telegram not configured. Set environment variable:"
        echo "   export TELEGRAM_BOT_TOKEN='your_bot_token'"
    else
        echo "✅ Telegram configured"
    fi

    echo ""
    echo "🎉 Setup finished!"
    echo ""
    echo "📋 Next steps:"
    echo "1. Configure Telegram (if not done):"
    echo "   export TELEGRAM_BOT_TOKEN='your_token'"
    echo "2. Setup Telegram bot authorization:"
    echo "   $0 setup-auth"
    echo "3. Start Telegram bot:"
    echo "   $0 telegram"
    echo "4. Use Telegram commands to manage trading bots"
}

# ===============================
# Telegram Bot Functions
# ===============================

start_telegram_bot() {
    echo "🤖 Starting Telegram Bot (Docker)"
    echo "=================================="

    # Check Docker
    if ! check_docker; then
        return 1
    fi

    # Check environment
    if [[ -z "$TELEGRAM_BOT_TOKEN" ]]; then
        echo "❌ Missing Telegram credentials"
        echo ""
        echo "💡 Set environment variable:"
        echo "   export TELEGRAM_BOT_TOKEN='your_bot_token'"
        return 1
    fi

    # Stop existing container
    if docker ps -a --format "table {{.Names}}" | grep -q "^telegram-bot$"; then
        echo "🛑 Stopping existing Telegram bot..."
        docker stop telegram-bot >/dev/null 2>&1 || true
        docker rm telegram-bot >/dev/null 2>&1 || true
    fi

    # Ensure directories exist
    ensure_directories

    # Get absolute paths for volume mounts
    local ABS_CONFIG_DIR="$(realpath "$CONFIG_DIR")"
    local ABS_DATA_DIR="$(realpath "$DATA_DIR")"
    local ABS_LOGS_DIR="$(realpath "$LOGS_DIR")"
    local ABS_AUTOTRADER_DIR="$(realpath "$AUTOTRADER_DIR")"

    echo "📁 Volume mounts:"
    echo "   Config: $ABS_CONFIG_DIR -> /app/configs"
    echo "   Data: $ABS_DATA_DIR -> /app/data"
    echo "   Logs: $ABS_LOGS_DIR -> /app/logs"
    echo "   AutoTrader: $ABS_AUTOTRADER_DIR -> /root/.autotrader"

    # Start container
    if docker run -d \
        --name telegram-bot \
        --restart unless-stopped \
        -e TELEGRAM_BOT_TOKEN="$TELEGRAM_BOT_TOKEN" \
        -e RUNNING_IN_CONTAINER="true" \
        -e HOST_CONFIG_DIR="$ABS_CONFIG_DIR" \
        -e HOST_DATA_DIR="$ABS_DATA_DIR" \
        -e HOST_LOGS_DIR="$ABS_LOGS_DIR" \
        -v "$ABS_CONFIG_DIR:/app/configs" \
        -v "$ABS_DATA_DIR:/app/data" \
        -v "$ABS_LOGS_DIR:/app/logs" \
        -v "$ABS_AUTOTRADER_DIR:/root/.autotrader" \
        -v /var/run/docker.sock:/var/run/docker.sock \
        "$TELEGRAM_IMAGE" >/dev/null; then

        echo "✅ Telegram bot started successfully!"
        echo "📊 Container: telegram-bot"
        echo "🔗 Token: ${TELEGRAM_BOT_TOKEN:0:10}..."
        echo ""
        echo "📋 Monitor with:"
        echo "   docker logs telegram-bot"
        echo "   $0 logs telegram-bot"
        echo ""
        echo "🧪 Test with:"
        echo "   Send /help to your Telegram bot"
        echo "   Send /createbot to test wizard"
    else
        echo "❌ Failed to start Telegram bot"
        return 1
    fi
}

# ===============================
# Main Command Handler
# ===============================

main() {
    case "${1:-help}" in
        "setup")
            setup_environment
            ;;
        "telegram")
            start_telegram_bot
            ;;
        "logs")
            if [[ -n "${2:-}" ]]; then
                docker logs -f --tail 50 "$2"
            else
                echo "❌ Usage: $0 logs <container_name>"
                echo "Example: $0 logs telegram-bot"
            fi
            ;;
        "stop")
            if [[ -n "${2:-}" ]]; then
                echo "🛑 Stopping container: $2"
                docker stop "$2" && docker rm "$2"
                echo "✅ Container stopped and removed"
            else
                echo "❌ Usage: $0 stop <container_name>"
                echo "Example: $0 stop telegram-bot"
            fi
            ;;
        "check"|"status")
            echo "🔍 AutoTrader System Status"
            echo "=========================="
            echo "📦 Version: $VERSION"
            echo ""
            
            # Docker status
            if command -v docker &> /dev/null; then
                if docker info &> /dev/null; then
                    echo "✅ Docker: Running"
                else
                    echo "⚠️ Docker: Installed but not running"
                fi
            else
                echo "❌ Docker: Not installed"
            fi
            
            # Telegram status
            if [[ -n "$TELEGRAM_BOT_TOKEN" ]]; then
                echo "✅ Telegram: Configured"
            else
                echo "⚠️ Telegram: Not configured"
            fi
            
            # Container status
            echo ""
            echo "📊 Container Status:"
            if docker ps --format "table {{.Names}}\t{{.Status}}" | grep -E "(telegram-bot|trader-)" | head -10; then
                :
            else
                echo "   No containers running"
            fi
            ;;
        "help"|*)
            cat << 'EOF'
🤖 AutoTrader Bot - Standalone Deployment Script

USAGE:
    ./autotrader.sh <command> [options]

COMMANDS:
    setup                       Complete environment setup
    telegram                    Start Telegram bot
    logs <container>           Show container logs
    stop <container>           Stop and remove container
    check, status              Show system status
    help                       Show this help

EXAMPLES:
    # First time setup
    ./autotrader.sh setup                    # Complete environment setup (pulls images, extracts template)
    
    # System management
    ./autotrader.sh telegram                 # Start Telegram bot
    ./autotrader.sh logs telegram-bot        # Show bot logs
    ./autotrader.sh stop telegram-bot        # Stop bot
    ./autotrader.sh check                    # Check system status

ENVIRONMENT VARIABLES:
    TELEGRAM_BOT_TOKEN         Telegram bot token (required)

NOTES:
    - This is a standalone script with no external dependencies
    - Docker will be installed automatically on Linux systems
    - All configurations are managed through Telegram bot interface
    - Template configs are extracted from Docker images automatically

EOF
            ;;
    esac
}

# Run main function with all arguments
main "$@"
