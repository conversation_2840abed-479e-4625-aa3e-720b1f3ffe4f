#!/usr/bin/env python3
"""Test callback routing"""

def test_callback_routing():
    """Test callback data routing"""
    test_data = [
        "step_basic_info",
        "step_entry", 
        "step_dca",
        "step_exit",
        "step_risk",
        "builder_save",
        "builder_preview",
        "builder_cancel"
    ]
    
    for data in test_data:
        print(f"Testing callback: {data}")
        
        # Test routing logic
        if data.startswith("builder_") or data.startswith("step_"):
            print(f"  ✅ Would route to builder callback")
        else:
            print(f"  ❌ Would NOT route to builder callback")
        
        # Test specific handlers
        if data == "builder_steps":
            print(f"  → _handle_step_selection")
        elif data.startswith("step_"):
            step = data.replace("step_", "")
            print(f"  → _handle_step('{step}')")
        elif data == "builder_save":
            print(f"  → _handle_save_strategy")
        elif data == "builder_preview":
            print(f"  → _handle_preview_strategy")
        elif data == "builder_cancel":
            print(f"  → _handle_cancel")
        else:
            print(f"  → unhandled callback")
        
        print()

if __name__ == "__main__":
    test_callback_routing()
